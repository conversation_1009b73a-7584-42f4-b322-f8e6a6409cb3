/* eslint-disable sonarjs/code-eval */
import { Project, SyntaxKind } from 'ts-morph';
import path from 'path';
import fs from 'fs';
import process from 'process';
import { execSync } from 'child_process';

const cwd = process.cwd();

function resolveModule(basePath: string) {
  const exts = ['.tsx', '.ts', '.jsx', '.js'];

  for (const ext of exts) {
    const fullPath = basePath + ext;
    if (fs.existsSync(fullPath)) {
      return fullPath;
    }
  }

  return basePath;
}

async function getAppRouter() {
  const res: {
    path: string;
    route: string;
  }[] = [];
  const project = new Project({
    tsConfigFilePath: 'tsconfig.json',
  });
  project.addSourceFilesAtPaths('src/**/*.tsx');
  for (const sourceFile of project.getSourceFiles()) {
    const jsxElements = sourceFile.getDescendantsOfKind(SyntaxKind.JsxSelfClosingElement);

    for (const element of jsxElements) {
      const tagName = element.getTagNameNode().getText();

      if (tagName === 'Route' || tagName === 'GuardRoute') {
        const pathAttr = element.getAttribute('path');
        const componentAttr = element.getAttribute('component');

        if (!componentAttr) continue;

        const componentValue =
          componentAttr.getFirstChildByKind(SyntaxKind.JsxExpression)?.getExpression()?.getText() ||
          componentAttr.getFirstChild()?.getText();

        if (!componentValue) continue;

        // 在当前文件里找这个变量对应的 import
        const importDeclarations = sourceFile.getImportDeclarations();
        for (const importDecl of importDeclarations) {
          const namedImports = importDecl.getNamedImports();
          for (const namedImport of namedImports) {
            const name = namedImport.getName();

            if (name === componentValue) {
              const moduleSource = importDecl.getModuleSpecifierValue();
              const importedPath = resolveModule(
                path.resolve(path.dirname(sourceFile.getFilePath()), moduleSource),
              ).replace(cwd + '/', '');

              const pathText = pathAttr?.getText();
              const regexRoutePath = /\b(PATH_[A-Z0-9_]+)\b/g;
              let __p;
              if (pathText && regexRoutePath.test(pathText)) {
                const cleanPathText = pathText.replace('path={', '__p=').replace(regexRoutePath, 'M.$1').slice(0, -1);
                const context = `
              const M = require('../src/router/paths.js');
              ${cleanPathText};
              `;

                try {
                  // biome-ignore lint/security/noGlobalEval: <explanation>
                  eval(context);
                } catch {
                  // ignore
                }
              } else if (pathText?.includes('path="')) {
                __p = pathText.match(/path="(.*?)"/)?.[1];
              }

              if (__p) {
                const route = {
                  path: importedPath,
                  route: __p,
                };
                res.push(route);
              }
            }
          }
        }
      }
    }
  }
  return res;
}

async function getDepsTree() {
  // eslint-disable-next-line sonarjs/no-os-command-from-path
  execSync('dpdm src/index.tsx -T -S -d none -o tree.json --no-circular --no-tree --no-progress --no-warning');
}

async function main() {
  getDepsTree();
  const route = await getAppRouter();
  fs.writeFileSync('route.json', JSON.stringify(route));
}
main();
