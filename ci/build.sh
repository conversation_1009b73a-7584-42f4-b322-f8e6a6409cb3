#!/usr/bin/env bash
# @since 2021-07-22 13:06:21
# <AUTHOR> <EMAIL>

set -xeuo pipefail

export S3_PATH="/w3/go/v2"
export PUBLIC_URL="https://cdn.moego.pet$S3_PATH"
export BUILD_FILE="./output.json"

pnpm -v

pnpm config set auto-install-peers true
pnpm install --frozen-lockfile

# api definitions should always be up-to-date
if [ "$BRANCH_NAME" = "main" ]; then
  pnpm i @moego/api-web@latest
  # pnpm openapi
fi

pnpm build
node -p -e "JSON.stringify({ revision: '$GIT_COMMIT', time: $(date +%s) }, void 0, 2)" > "$BUILD_FILE"

# upload sourcemaps to Datadog RUM
# https://docs.datadoghq.com/real_user_monitoring/guide/upload-javascript-source-maps
./.datadog-ci sourcemaps upload ./build/static \
  --service Boarding_Desktop \
  --release-version $BRANCH_NAME:$(git rev-parse --short=7 HEAD) \
  --minified-path-prefix ${PUBLIC_URL}/static
