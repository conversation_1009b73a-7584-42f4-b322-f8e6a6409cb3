#!/usr/bin/env bash
# @since 2022-08-04 10:47:10
# <AUTHOR> <EMAIL>

export XDG_DATA_HOME="$PWD/.cache"
export NODE_OPTIONS="--max-old-space-size=8192"
set -euo pipefail
rm -f "$XDG_DATA_HOME/lint.error"

if [ ! -d "$XDG_DATA_HOME" ]; then
  mkdir -p "$XDG_DATA_HOME"
fi

function echo_error() {
  echo -e "\n"
  echo -e "\033[31mERROR: $1\033[0m"
  touch "$XDG_DATA_HOME/lint.error"
}

function echo_info() {
  echo -e "\033[32mINFO: $1\033[0m"
}

function check_native_modules() {
  if grep -E $(echo $(grep -E "from '[^.']+'" ./src/utils/NativeModules.ts -o | awk -F "'" '{print $2}') | sed 's/ /|/g') -r ./src | grep -v src/utils/NativeModules.ts | grep -v .d.ts:; then
    echo_error 'please import native modules from src/utils/NativeModules.ts rather than node modules directly.'
  fi
}

function check_moment() {
  if grep -E 'from .moment.' -r src; then
    echo_error 'moment is deprecated, use dayjs instead.'
  fi
}

function check_ts_ignore() {
  echo_info "Checking ts-ignore"
  if git --no-pager grep -n -i @ts''-ignore; then
    echo_error '@ts''-ignore is forbidden, please fix it or use @ts-expect-error instead.'
  fi
  echo_info "Check ts-ignore done"
}

function check_generate() {
  if git status -s | grep src/entry/generated/; then
    echo_error 'The generated directory is dirty, please stash or commit it first.'
  fi
  yarn generate
  if git status -s | grep src/entry/generated/; then
    echo_error 'Please do not edit /src/entry/generated/ directory, use \`yarn generate\` to generate it.'
  fi
}

function check_index() {
  echo_info "Checking index files"
  if git ls-files | grep -v -E '^(src|public)/index' | grep /index; then
    echo_error 'index.* is forbidden, please use valid file name instead.'
  fi
  echo_info "Check index files done"
}

# lazy import need default export
# function check_default() {
#   echo_info "Checking export default"
#   if git grep 'export default' -- src | grep -v '.d.ts:'; then
#     echo_error 'export default is forbidden, please use export { xxx } instead.'
#   fi
# }

function check_circular() {
  echo_info "Checking circular dependencies"
  if ! pnpm dpdm -T --no-tree --no-warning --exit-code circular:1 src/index.tsx --exclude '/(node_modules|src/container/PaymentFlow)/'; then
    echo_error 'Circular dependencies detected, please fix it.'
  fi
  echo_info "Check circular dependencies done"
}

function check_biome() {
  echo_info "Checking biome"
  if ! pnpm biome check --diagnostic-level error; then
    echo_error 'Code is not formatted correctly, please fix it.'
  fi
  echo_info "Check biome done"
}

function check_text_lint() {
  echo_info "Checking text lint"
  if ! pnpm check:text-lint; then
    echo_error 'Text lint check failed, please fix it.'
  fi
  echo_info "Check text lint done"
}

function check_types() {
  echo_info "Checking types"
  if ! pnpm tsc --noEmit --skipLibCheck; then
    echo_error 'Ts check failed, please fix it.'
  fi
  echo_info "Check types done"
}

function check_commit_msg() {
  echo_info "Checking commit messages"
  if ! pnpm commitlint --from origin/main; then
    echo_error 'Commit message check failed, please fix it.'
  fi
  echo_info "Check commit messages done"
}

function check_dep_branch() {
  echo_info 'Checking dependency tags'
  if grep -n -E '^ *\"@moego/[^\"]+\": \".*-.*\",?$' package.json; then
    echo_error 'Dependencies containing npm packages with non-latest tag are prohibited from merging.'
  fi
  echo_info 'Check dependency tags done'
}

function check_all() {
  check_ts_ignore &
  check_index &
  check_dep_branch &
  wait
  check_circular &
  wait
  check_types &
  check_biome &
  check_text_lint &
  check_commit_msg &
  wait
}

if [[ $# -gt 0 ]]; then
  "check_$1"
else
  export CHECK_TS_TYPE="false"
  pnpm install --frozen-lockfile
  check_all
fi

if [ -f "$XDG_DATA_HOME/lint.error" ]; then
  echo "Found error(s) please fix."
  rm "$XDG_DATA_HOME/lint.error"
  exit 1
fi
