/* eslint-disable sonarjs/no-nested-template-literals */
import { Octokit } from '@octokit/rest';
import fs from 'fs-extra';
import path from 'path';
import { analyzeDiff, type DiffResult } from 'diff-effect';
import crypto from 'node:crypto';
import OpenAI from 'openai';

export const Repo = process.env.REPO_NAME || 'Boarding_Desktop';
export const Owner = process.env.OWNER_NAME || 'MoeGolibrary';
export const PrNumber = process.env.PR_NUMBER || '0';
export const GithubToken = process.env.ADMIN_TOKEN_GITHUB || 'xxxxx';
export const OpenAiToken = process.env.OPENAI_TOKEN || 'xxxxxxx';
const Cwd = process.cwd();

const AI_CACHE_DIR = path.join(Cwd, '.cache', 'ai');

export const octokit = new Octokit({
  auth: GithubToken,
});
export const openai = new OpenAI({
  apiKey: OpenAiToken, // 记得设置
});

export const DEFAULT_WEIGHTS = {
  globalFileCount: 0.1,
  globalFileType: 0.05,
  inSrc: 0.05,
  shared: 0.05,
  entry: 0,
  deps: 0.05,
  perf: 0.15, // 从 0.68 大幅降低
  changes: 0.05,
  patchLength: 0.1,
  patchAI: 0.4, // 从 0.036 大幅提高
  prKeywords: 0.0, // 从 0.001 降低

  patchLength_formula_selector: 0.25,
  patchLength_linear_k: 0.01,
  patchLength_log_k: 1.0,

  effectedRoutes: 0.002,
  effectedFiles: 0.0006,

  // ✅ 新增：子项聚合公式选择器的默认值
  // 0-0.25: max, 0.25-0.5: mean, 0.5-0.75: weightedMean, 0.75-1: sum
  inSrc_agg_selector: 0.6, // 默认用 weightedMean
  shared_agg_selector: 0.6, // 默认用 weightedMean
  deps_agg_selector: 0.1, // 默认用 max
  perf_agg_selector: 0.1, // 默认用 max
  changes_agg_selector: 0.6, // 默认用 weightedMean
  patchLength_agg_selector: 0.6, // 默认用 weightedMean
  patchAI_agg_selector: 0.1, // 默认用 max
};

export const USEFUL_WEIGHTS = {
  globalFileCount: 0.08,
  globalFileType: 0.0272,
  inSrc: 0.0694,
  shared: 0.003,
  entry: 0,
  deps: 0.0018,
  perf: 0.3675,
  changes: 0.0401,
  patchLength: 0.0,
  patchAI: 0.352,
  prKeywords: 0.059,

  patchLength_formula_selector: 0.134,
  patchLength_linear_k: 0.002,
  patchLength_log_k: 0.9375,
  inSrc_agg_selector: 0.6425,
  shared_agg_selector: 0.631,
  deps_agg_selector: 0.0404,
  perf_agg_selector: 0.1709,
  changes_agg_selector: 0.6187,
  patchLength_agg_selector: 0.6004,
  patchAI_agg_selector: 0.0995,

  effectedRoutes: 0.002,
  effectedFiles: 0.0006,
};

// #region Type Definitions
export interface RouteInfo {
  path: string;
  route: string | string[];
}

export interface ImpactWeights {
  globalFileCount: number;
  globalFileType: number;
  inSrc: number;
  shared: number;
  entry: number;
  deps: number;
  perf: number;
  changes: number;
  patchLength: number;
  patchAI: number;
  prKeywords: number;

  // --- patchLength 的公式学习基因 ---
  patchLength_formula_selector?: number;
  patchLength_linear_k?: number;
  patchLength_log_k?: number;

  // --- ✅ 新增：每个子项的公式选择器基因 ---
  // 每个选择器都是一个 [0, 1] 范围的数值，用于决定该子项使用哪种聚合函数
  inSrc_agg_selector?: number;
  shared_agg_selector?: number;
  deps_agg_selector?: number;
  perf_agg_selector?: number;
  changes_agg_selector?: number;
  patchLength_agg_selector?: number;
  patchAI_agg_selector?: number;

  effectedRoutes: number;
  effectedFiles: number;
}

// Updated SymbolInfo to reflect the new data structure with depends_on and id
export interface ExportInfo {
  exported: string;
  local: string;
  depends_on?: number[];
  reexport_source?: string | null;
}

export interface ImportInfo {
  id: number;
  imported: string;
  local: string;
  source: string;
}

export interface SymbolInfo {
  exports: ExportInfo[];
  imports: ImportInfo[];
}

export interface TreeData {
  symbol: Record<string, SymbolInfo>;
  tree?: Record<string, any>;
}

export type PRFile = {
  filename: string;
  status: string;
  additions: number;
  deletions: number;
  changes: number;
  patch?: string;
  patchLength: number;
  inSrc: boolean;
  isShared: boolean;
  hasDepsChange: boolean;
  isPerfSensitive: boolean;
};

export type PRImpact = {
  files: PRFile[];
  fileCount: number;
  fileTypeCount: number;
  impactScore: number;
};

export type PREffect = {
  effected: string[];
  maxRouteLength: number;
  impactedFilePaths: Set<string>;
  impactedFileCount: number;
};

export type SourcePRFiles = Awaited<ReturnType<typeof octokit.rest.pulls.listFiles>>['data'];
export type PRDetails = Awaited<ReturnType<typeof octokit.rest.pulls.get>>['data'];

export type SourcePRFile = {
  filename: string;
  status: string;
  additions: number;
  deletions: number;
  changes: number;
  patch?: string;
};

export type PRFileScore = {
  [key: string]: number;
};

// #endregion

// #region AI Analysis with Cache
export const aiCachePath = path.join(AI_CACHE_DIR, '.ai_cache.json');

export function getCacheKey(filePath: string, fileContent: string, patch: string, mask?: string): string {
  const hex = crypto
    .createHash('sha256')
    .update(fileContent + patch)
    .digest('hex');
  return `${mask}-${filePath}-${hex}`;
}

const cache = fs.existsSync(aiCachePath) ? fs.readJsonSync(aiCachePath) : {};
export async function getAIPerformanceAnalysis(
  filePath: string,
  fileContent: string,
  patch: string,
  mask?: string,
): Promise<number> {
  const cacheKey = getCacheKey(filePath, fileContent, patch, mask);

  if (cache[cacheKey] || cache[cacheKey] === 0) {
    // console.log(`[AI Cache] HIT for ${filePath}`);
    return cache[cacheKey];
  }

  console.log(`[AI Cache] MISS for ${filePath}. Analyzing...`);
  // Mock AI call
  const analysisResult = await aiAnalysis(filePath, fileContent, patch);

  cache[cacheKey] = analysisResult / 100;
  await fs.writeJson(aiCachePath, cache);

  return analysisResult;
}

export async function aiAnalysis(filePath: string, fileContent: string, patch: string): Promise<number> {
  const isTargetFile =
    filePath.endsWith('.ts') ||
    filePath.endsWith('.tsx') ||
    filePath.endsWith('.js') ||
    filePath.endsWith('.jsx') ||
    filePath.endsWith('.css') ||
    filePath.endsWith('.scss') ||
    filePath.endsWith('.less') ||
    filePath.endsWith('.patch') ||
    filePath.endsWith('.html');

  if (!isTargetFile) {
    return 0;
  }

  // ✅ 1️⃣ 如果 patch 太大（比如超过 20k），直接给 70
  if (patch.length > 20000) {
    console.log(`[AI Analysis] Patch too long, skip call. Return 70.`);
    return 70;
  }

  // ✅ 2️⃣ 根据 fileContent 长度，决定是否带上下文
  const includeFileContent = fileContent.length > 0 && fileContent.length < 10000;

  // ✅ 3️⃣ system prompt 中明确：fileContent 只作上下文参考，真正评分只基于 patch
  const systemPrompt = `
你是一个前端性能分析 AI，请对给定的补丁（patch）进行代码分析，并输出可能引发的性能风险分数（0~100），越高表示风险越大。

请特别关注以下常见的前端性能劣化场景，并参考示例给出合理分数：

✅ 安全示例（0~20）
- 仅修改格式、注释、拼写、类型：0~5
- 重命名变量、函数或简单重构：5~10
- 新增无副作用的纯工具函数：10~20
- 修改少量静态样式变量（如颜色、字体大小）：10~20

⚠️ 逻辑风险示例（30~100）
- 新增嵌套循环、递归、或 O(N^2) 以上复杂度：60~80
- 在渲染函数或组件主体内放入大计算逻辑（如 map/filter/reduce）：70~90
- 在 useEffect/useLayoutEffect 中直接操作 DOM 或动态测量（如 getBoundingClientRect）：40~70
- 引入了额外的 useEffect 用来同步状态，而不是通过 events 直接同步: 40~60
- 在大量状态管理（如 useSelector、上下文）中未做选择性更新：40~60
- 引入了来自 amos 的 useSelector hook: 70~80
- 引入大型外部依赖、polyfill、大体积文件：50~80
- 涉及 refresh、update 逻辑时: 70~80
- 改动与性能关系不大但上下文不足无法判断：30

⚠️ 样式与 CSS 风险示例（30~80）
- 新增高复杂度选择器（如深层嵌套 > 3 层）：40~60
- 涉及 DOM 结构修改或布局属性变化时：70~80
- 使用通配符选择器（如 "*"）或后代选择器滥用：40~60
- 在大列表或高频渲染节点上使用 "!important" 强行覆盖样式：30~50
- 新增高开销 CSS 动画（如 box-shadow 动画、频繁改变布局属性）：50~80
- 使用 "position: fixed"、"position: absolute" 带复杂层叠上下文，且未限制作用范围：40~70
- 引入可能触发回流（reflow）的布局变更，如 "width"、"height" 动态计算但无容器限制：40~70
- 大量使用 CSS-in-JS 且未做样式缓存：40~60

请务必只基于 patch 的变更进行判断，fileContent 仅供上下文参考，不要基于 fileContent 的其他未改动部分分析风险。

输出要求：
- 只输出一个整数，范围 0~100
- 不要输出任何解释或标点
- 如果无法判断，请输出 30
`.trim();

  const response = await openai.chat.completions.create({
    model: 'gpt-4o',
    temperature: 0,
    messages: [
      {
        role: 'system',
        content: systemPrompt,
      },
      {
        role: 'user',
        content: `请分析以下补丁对性能的风险：
FilePath: ${filePath}

Patch:
\`\`\`
${patch}
\`\`\`
${includeFileContent ? `\n以下是该文件完整内容，仅供参考上下文：\n\`\`\`\n${fileContent}\n\`\`\`` : ''}`,
      },
    ],
  });

  const result = response.choices[0].message.content || 'Unknown';
  console.log(`[AI Analysis] Result: ${result}`);

  // 确保是合法整数
  const num = parseInt(result.trim(), 10);
  return isNaN(num) ? 30 : Math.min(100, Math.max(0, num));
}

// #endregion

// #region Impacts Analyze (No changes here, kept as is)

function selectAndAggregate(selector: number, values: number[], weights?: number[]): number {
  const max = (arr: number[]) => (arr.length > 0 ? Math.max(...arr) : 0);
  const mean = (arr: number[]) => (arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0);
  const sum = (arr: number[]) => arr.reduce((a, b) => a + b, 0);
  const weightedMean = (vals: number[], wts: number[]) => {
    const totalWeight = wts.reduce((a, b) => a + b, 0);
    if (totalWeight === 0) return 0;
    return vals.reduce((s, v, i) => s + v * wts[i], 0) / totalWeight;
  };

  if (selector < 0.25) {
    return max(values);
  } else if (selector < 0.5) {
    return mean(values);
  } else if (selector < 0.75) {
    // 加权平均需要权重数组，如果未提供则退化为普通平均
    return weightedMean(values, weights || Array(values.length).fill(1));
  } else {
    return sum(values);
  }
}

export async function analyzePRImpacts(
  effect: PREffect,
  details: PRDetails,
  data: SourcePRFile[],
  w: ImpactWeights = USEFUL_WEIGHTS, // 使用训练好的
  mask?: string,
): Promise<PRImpact> {
  const isLinear = w.patchLength_formula_selector !== undefined && w.patchLength_formula_selector < 0.5;
  const k = (isLinear ? w.patchLength_linear_k : w.patchLength_log_k) ?? 0;
  const files: PRFile[] = data.map((file) => {
    const patchLength = file.patch ? file.patch.length : 0;

    const filename = file.filename;
    // 1️⃣ 是否在 src/ 目录下
    const inSrc = filename.startsWith('src/');
    // 2️⃣ 是否为初级目录下公共性质文件
    const isShared =
      filename.includes('src/components') || filename.includes('src/utils') || filename.includes('src/container');
    // 3️⃣ 是否为依赖项
    const hasDepsChange =
      filename === 'package.json' || filename.includes('pnpm-lock') || filename.includes('yarn.lock');
    // 4️⃣ 是否包含敏感关键词
    const isPerfSensitive = Boolean(
      filename.includes('Infinite') ||
        filename.includes('infinite') ||
        filename.includes('hook') ||
        filename.includes('utils') ||
        file.patch?.includes('useSelector') ||
        file.patch?.includes('update()'),
    );

    return {
      filename,
      status: file.status,
      additions: file.additions,
      deletions: file.deletions,
      changes: file.changes,
      patch: file.patch,
      patchLength,
      inSrc,
      isShared,
      hasDepsChange,
      isPerfSensitive,
    };
  });

  const fileTypes = new Set(files.map((f) => f.filename.split('.').pop()));

  const childScores: PRFileScore[] = [];
  for await (const f of files) {
    const s_inSrc = f.inSrc ? 1 : 0;
    const s_shared = f.isShared ? 1 : 0;
    const s_deps = f.hasDepsChange ? 1 : 0;
    const s_perf = f.isPerfSensitive ? 1 : 0;
    const s_changes = Math.min(f.changes / 100, 1);
    const s_patch = isLinear ? k * f.patchLength : k * Math.log(1 + f.patchLength);

    let s_patchAI = 0;
    if (f.patch && f.inSrc) {
      try {
        const fileContent = await fs.readFile(f.filename, 'utf-8').catch(() => '');
        const rawScore = await getAIPerformanceAnalysis(f.filename, fileContent, f.patch, mask);
        // 原始分数归一化（0~5）
        const s_patchAI_raw = Math.min(Math.max(rawScore, 0), 100) / 100;

        // 可信度因子
        const trustFactor = 0.5 + 0.5 * s_perf + 0.5 * s_changes;

        // 加权后的分数
        s_patchAI = s_patchAI_raw * trustFactor;
      } catch (e) {
        console.error(`Error during AI analysis for ${f.filename}:`, e);
      }
    }

    childScores.push({ s_inSrc, s_shared, s_deps, s_perf, s_changes, s_patch, s_patchAI });
  }

  const fileWeights = files.map((f) => {
    const base = 1;
    const patchLengthFactor = f.patchLength / 5000;
    const changesFactor = f.changes / 100;
    const depsFactor = f.hasDepsChange ? 2 : 0;
    const perfFactor = f.isPerfSensitive ? 2 : 0;

    return base + patchLengthFactor + changesFactor + depsFactor + perfFactor;
  });

  const baseScores = {
    globalFileCount: Math.min(files.length / 20, 1),
    globalFileType: Math.min(fileTypes.size / 5, 1),
    prKeywords: (() => {
      const title = details.title;
      const body = details.body ?? '';
      const isPerf = title.toLowerCase().includes('perf');
      const isPerfBody = body.toLowerCase().includes('perf') || body.includes('性能');
      return isPerf || isPerfBody ? 1 : 0; // Use 1 for true, 0 for false
    })(),
    // 对每个子项，使用 selectAndAggregate 函数和对应的选择器基因来计算
    inSrc: selectAndAggregate(
      w.inSrc_agg_selector ?? 0.6,
      childScores.map((s) => s.s_inSrc),
      fileWeights,
    ),
    shared: selectAndAggregate(
      w.shared_agg_selector ?? 0.6,
      childScores.map((s) => s.s_shared),
      fileWeights,
    ),
    deps: selectAndAggregate(
      w.deps_agg_selector ?? 0.1,
      childScores.map((s) => s.s_deps),
    ),
    perf: selectAndAggregate(
      w.perf_agg_selector ?? 0.1,
      childScores.map((s) => s.s_perf),
    ),
    changes: selectAndAggregate(
      w.changes_agg_selector ?? 0.6,
      childScores.map((s) => s.s_changes),
      fileWeights,
    ),
    patchLength: selectAndAggregate(
      w.patchLength_agg_selector ?? 0.6,
      childScores.map((s) => s.s_patch),
      fileWeights,
    ),
    patchAI: selectAndAggregate(
      w.patchAI_agg_selector ?? 0.1,
      childScores.map((s) => s.s_patchAI),
    ),
    effectedRoutes: effect.maxRouteLength > 0 ? effect.effected.length / effect.maxRouteLength : 0,
    effectedFiles: files.length > 0 ? effect.impactedFileCount / files.length : 0,
  };
  const score = (() => {
    if (effect.effected.length === 0) {
      return 0;
    }
    return (
      w.globalFileCount * baseScores.globalFileCount +
      w.globalFileType * baseScores.globalFileType +
      w.prKeywords * baseScores.prKeywords +
      w.inSrc * baseScores.inSrc +
      w.shared * baseScores.shared +
      w.deps * baseScores.deps +
      w.perf * baseScores.perf +
      w.changes * baseScores.changes +
      w.patchLength * baseScores.patchLength +
      w.patchAI * baseScores.patchAI +
      w.effectedRoutes * baseScores.effectedRoutes +
      w.effectedFiles * baseScores.effectedFiles
    );
  })();

  return {
    files,
    fileCount: files.length,
    fileTypeCount: fileTypes.size,
    impactScore: +(score * 100).toFixed(2),
  };
}
// #endregion Impacts Analyze

// #region Symbol-level Effect Analysis (New Logic)

const createSymbolId = (filePath: string, symbolName: string) => `${filePath}#${symbolName}`;

function resolveModule(basePath: string, moduleSource: string) {
  const resolvedPath = path.resolve(path.dirname(basePath), moduleSource);
  const exts = ['.ts', '.tsx', '.js', '.jsx', '.json'];
  // Check for file with extension
  for (const ext of exts) {
    const fullPath = resolvedPath + ext;
    if (fs.existsSync(fullPath)) return fullPath.replace(Cwd + '/', '');
  }
  // Check for directory index file
  for (const ext of exts) {
    const fullPath = path.join(resolvedPath, 'index' + ext);
    if (fs.existsSync(fullPath)) return fullPath.replace(Cwd + '/', '');
  }
  return resolvedPath.replace(Cwd + '/', '');
}

/**
 * Builds a precise reverse dependency graph, now with support for dynamic imports.
 * @param symbolData The symbol information from tree.json.
 * @returns A Map where the key is an imported symbol's ID and the value is a Set of exported symbol IDs that depend on it.
 */
function buildSymbolDependentsGraph(symbolData: Record<string, SymbolInfo>): Map<string, Set<string>> {
  const dependentsGraph = new Map<string, Set<string>>();
  const filePaths = Object.keys(symbolData);

  for (const filePath of filePaths) {
    const info = symbolData[filePath];
    if (!info || !info.imports || !info.exports) continue;

    // Create a map of an import's ID to its full import object for easy lookup
    const importIdToInfoMap = new Map<number, ImportInfo>();
    for (const imp of info.imports) {
      if (imp.id !== undefined) {
        importIdToInfoMap.set(imp.id, imp);
      }
    }

    // For each export, find what it depends on and build the reverse link
    for (const exp of info.exports) {
      if (!exp.depends_on) continue;
      const dependentSymbolId = createSymbolId(filePath, exp.exported);

      for (const importId of exp.depends_on) {
        const importInfo = importIdToInfoMap.get(importId);

        // We only care about internal project dependencies
        if (!importInfo || !importInfo.source.startsWith('.')) {
          continue;
        }

        const sourcePath = resolveModule(filePath, importInfo.source);

        // ✅ NEW LOGIC: Check for dynamic imports
        if (importInfo.imported === '*') {
          // This export depends on a dynamic import, so it depends on ALL exports from the source module.
          const sourceModuleInfo = symbolData[sourcePath];
          if (sourceModuleInfo?.exports) {
            for (const sourceExp of sourceModuleInfo.exports) {
              const importedSymbolId = createSymbolId(sourcePath, sourceExp.exported);
              if (!dependentsGraph.has(importedSymbolId)) {
                dependentsGraph.set(importedSymbolId, new Set());
              }
              dependentsGraph.get(importedSymbolId)!.add(dependentSymbolId);
            }
          }
        } else {
          // It's a regular named import
          const importedSymbolId = createSymbolId(sourcePath, importInfo.imported);
          if (!dependentsGraph.has(importedSymbolId)) {
            dependentsGraph.set(importedSymbolId, new Set());
          }
          dependentsGraph.get(importedSymbolId)!.add(dependentSymbolId);
        }
      }
    }
  }
  return dependentsGraph;
}

export async function analyzeSymbolEffect(changedFiles: SourcePRFile[]): Promise<PREffect> {
  // console.log("\n--- Starting Symbol-Level Impact Analysis ---");
  const treeData: TreeData = fs.readJSONSync('tree.json');
  const routeData: RouteInfo[] = fs.readJSONSync('route.json');
  const maxRouteLength = routeData.length;

  const symbolData = treeData.symbol;
  if (!symbolData) {
    console.error("❌ 'symbol' data not found in tree.json. Aborting analysis.");
    return { effected: [], maxRouteLength, impactedFileCount: 0, impactedFilePaths: new Set() };
  }

  // 1. Build the precise dependents graph, now with dynamic import handling.
  const dependentsGraph = buildSymbolDependentsGraph(symbolData);

  const impactedSymbols = new Set<string>();
  const queue: string[] = [];

  // 2. Initialize queue with all exports from directly changed files.
  // NOTE: This is the area for future improvement. A more advanced version
  // would analyze the 'patch' data to only add *actually modified* exports.
  for (const { filename: file, patch } of changedFiles) {
    const exports = symbolData[file]?.exports;
    let diffEffect: DiffResult[] = [];
    try {
      if (patch) {
        const fileCode = await fs.readFile(file, 'utf-8').catch(() => '');
        diffEffect = analyzeDiff(fileCode, patch);
      }
    } catch {
      // console.error(`❌ Error analyzing diff for file ${file}:`, error);
      // ignore
    }
    if (exports) {
      for (const exp of exports) {
        if (!diffEffect.find((d) => d.name === exp.exported)) {
          continue;
        }
        const symbolId = createSymbolId(file, exp.exported);
        if (!impactedSymbols.has(symbolId)) {
          impactedSymbols.add(symbolId);
          queue.push(symbolId);
        }
      }
    }
  }
  console.log(`Found ${queue.length} initially changed exported symbols to start the analysis.`);

  // 3. Propagate the "dirty" state through the dependency graph.
  // Using a pointer for the queue is more efficient than shift() for large queues.
  let head = 0;
  while (head < queue.length) {
    const currentSymbolId = queue[head];
    head++;

    const dependents = dependentsGraph.get(currentSymbolId) || new Set();

    for (const dependentSymbolId of dependents) {
      if (!impactedSymbols.has(dependentSymbolId)) {
        impactedSymbols.add(dependentSymbolId);
        queue.push(dependentSymbolId);
      }
    }
  }
  console.log(`Total impacted symbols after propagation: ${impactedSymbols.size}`);

  // 4. Map the final set of impacted symbols back to files and routes.
  const impactedFilePaths = new Set<string>();
  for (const symbolId of impactedSymbols) {
    const [filePath] = symbolId.split('#');
    impactedFilePaths.add(filePath);
  }
  console.log('impactedFilePaths:', impactedFilePaths);

  const routeMap = new Map(routeData.map((r) => [r.path, Array.isArray(r.route) ? r.route : [r.route]]));
  const impactedRoutes = new Set<string>();
  for (const file of impactedFilePaths) {
    if (routeMap.has(file)) {
      routeMap.get(file)!.forEach((r) => impactedRoutes.add(r));
    }
  }

  return {
    effected: Array.from(impactedRoutes).sort(),
    maxRouteLength,
    impactedFilePaths,
    impactedFileCount: impactedFilePaths.size,
  };
}
// #endregion

// #region PR Body Generation and Update
function generatePrBodyContent(impact: PRImpact, effect: PREffect): string {
  const { impactScore, fileCount, fileTypeCount } = impact;
  const { effected, maxRouteLength, impactedFileCount, impactedFilePaths } = effect;

  const scoreEmoji = impactScore > 70 ? '🔴' : impactScore > 50 ? '🟡' : '🟢';

  const routesList =
    effected.length > 0
      ? '```js\n' + effected.map((route) => `"${route}"`).join('\n') + '\n```\n'
      : 'No routes directly affected.';

  const impactedFilesList =
    impactedFilePaths.size > 0
      ? '```js\n' +
        Array.from(impactedFilePaths)
          .map((p) => `"${p}"`)
          .join('\n') +
        '\n```\n'
      : 'No files directly affected.';

  const content = `
## 🤖 PR Analysis

**影响评分: ${scoreEmoji} ${impactScore} / 100**
> 该分数基于文件数量 (${fileCount}), 文件类型多样性 (${fileTypeCount}), 以及敏感文件更改等因素，反映了更改的潜在风险和范围。

<details>
<summary><strong>影响路由 (${effected.length} / ${maxRouteLength})</strong></summary>

**受影响的路由列表:**
${routesList}

</details>

<details>
<summary><strong>影响文件 (${impactedFileCount})</strong></summary>

**受影响的文件列表:**
${impactedFilesList}
</details>
`;

  return content.trim();
}

async function updatePrBody(impact: PRImpact, effect: PREffect) {
  const prNumber = Number(PrNumber);
  console.log(`\nUpdating PR #${prNumber} body...`);

  const startMarker = '<!-- #region Meta Block: Auto generated, do not add anything within the region -->';
  const endMarker = '<!-- #endregion Meta Block -->';

  try {
    const { data: pr } = await octokit.rest.pulls.get({
      owner: Owner,
      repo: Repo,
      pull_number: prNumber,
    });

    const currentBody = pr.body || '';
    const newContent = generatePrBodyContent(impact, effect);
    const newBlock = `${startMarker}\n\n${newContent}\n\n${endMarker}`;

    const blockRegex = new RegExp(`${startMarker}[\\s\\S]*${endMarker}`);
    let newBody: string;

    if (blockRegex.test(currentBody)) {
      console.log('Found existing analysis block. Replacing content.');
      newBody = currentBody.replace(blockRegex, newBlock);
    } else {
      console.log('No analysis block found. Appending new block.');
      newBody = `${currentBody}\n\n${newBlock}`;
    }

    await octokit.rest.pulls.update({
      owner: Owner,
      repo: Repo,
      pull_number: prNumber,
      body: newBody,
    });

    console.log(`✅ Successfully updated PR #${prNumber} body.`);
  } catch (error) {
    console.error(`❌ Failed to update PR body:`, error);
  }
}
// #endregion

export async function main() {
  if (!GithubToken) {
    console.error('错误：未设置 ADMIN_TOKEN_GITHUB 环境变量或使用了示例值。');
    // return; // Commented out for demonstration purposes
  }
  const prNumber = Number(PrNumber);
  if (isNaN(prNumber)) {
    console.error('错误：PR_NUMBER 不是一个有效的数字。');
    return;
  }

  console.log(`正在分析仓库 ${Owner}/${Repo} 中的 PR #${prNumber}...`);

  try {
    const { data: prDetails } = await octokit.rest.pulls.get({
      owner: Owner,
      repo: Repo,
      pull_number: prNumber,
    });
    const { data: prFiles } = await octokit.rest.pulls.listFiles({
      owner: Owner,
      repo: Repo,
      pull_number: prNumber,
    });

    const fileList = prFiles.map((d) => d.filename);
    console.log('变更文件列表:', fileList);

    // Run new symbol-level analysis
    const effect = await analyzeSymbolEffect(prFiles);
    const impact = await analyzePRImpacts(effect, prDetails, prFiles);

    console.log('\n--- PR 影响分析 (Impact Score) ---');
    console.log('影响分数:', impact.impactScore);
    console.log('变更文件总数:', impact.fileCount);

    console.log('\n--- PR 影响分析 (Effect Analysis) ---');
    console.log('精确影响文件数:', effect.impactedFileCount);
    console.log('路由总数', effect.maxRouteLength);
    if (effect.effected.length > 0) {
      console.log('受影响的路由:', effect.effected);
    } else {
      console.log('没有直接的路由受到影响。');
    }
    console.log('-------------------\n');

    await updatePrBody(impact, effect);
  } catch (error) {
    console.error('分析过程中出现错误:', error);
  }
}

main();
