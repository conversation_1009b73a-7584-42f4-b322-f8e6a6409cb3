import { type EnumValues, createEnum, createStrictEnum } from '../../store/utils/createEnum';

// 这里原则上没有实际意义，但是为了声明每个 feature 的作用范围，这里还是跟 metadata 一样，做一下定义，后期维护也比较直观
export const GrowthBookOwnerType = createEnum({
  // 整个系统
  System: [0, 'System'],
  // 企业列入：xxx 宠物企业（包含所有 xxx 公司）
  Enterprise: [1, 'Enterprise'],
  // 公司例如：xxx 宠物公司
  Company: [2, 'Company'],
  // 门店例如：xxx 宠物公司下的门店
  Business: [3, 'Business'],
  // 账户例如：xxx 宠物公司下的账户
  Account: [4, 'Account'],
});

export const GrowthBookFeatureList = createStrictEnum({
  EnableNewClientPortalForGrooming: [
    'obc_new_client_portal_for_grooming',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnablePlaygroup: [
    'enable_playgroup',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  APIEndpointType: [
    'api_endpoint_type',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableTaskManagement: [
    'enable_task_management',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableGroomingBundleService: ['enable_bundle_service', { ownerType: GrowthBookOwnerType.Company }],
  EnableDropWhenEditSchedule: [
    'enable_drop_when_edit_schedule',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableSendMultipleImages: [
    'enable_send_multiple_images',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnablePermissionSaveUpgrade: [
    'permission-save-upgrade',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableCallingSeat: [
    'switch-seats',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableWorkflow: [
    'workflow_whitelist',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  WorkflowSuperAdmin: [
    'workflow_super_admin',
    {
      ownerType: GrowthBookOwnerType.Account,
    },
  ],
  Credit: [
    'credit',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  AllowMarkAsResolved: [
    'allow_mark_as_resolved',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  AllowClientRetention: [
    'allow_client_retention',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  ThemeFollowEnterprise: [
    'theme_follow_enterprise',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableMultiplePets: [
    'enable_multiple_pets',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableServiceChargePricingUnit: [
    'enable_service_charge_pricing_unit',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  DisplaySubscriptionPrice: [
    'display_subscription_price',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnablePerformanceOptimize: [
    'enable_performance_optimize',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  AllowDogWalking: [
    'allow_dog_walking',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  AllowGroupClass: [
    'allow_group_class',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EditorAllowInput: [
    'editor_allow_input',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableOBSellPackage: [
    'enable_ob_sell_package',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableMobileDeviceScale: [
    'enable_mobile_device_scale',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnablePageSpy: [
    'enablePageSpy',
    {
      ownerType: GrowthBookOwnerType.Account,
    },
  ],
  EnableAmosAutoBatch: [
    'enable_amos_auto_batch',
    {
      ownerType: GrowthBookOwnerType.Account,
    },
  ],
  EnableSaleMembershipByACHLink: [
    'enable_sale_membership_by_ach_link',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableSaleMembershipByACHBankAccount: [
    'enable_sale_membership_by_ach_bank_account',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableDiscountForLineItem: [
    'enterprise_discount_redeem_lineitem',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableSplitLodgings: [
    'enable_split_lodgings',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableCheckInOpenApptDrawer: [
    'enable_checkin_open_appt_drawer',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  FullPaymentNoStatusChange: [
    'full_payment_no_status_change',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  NewOrderV4Flow: ['new_order_flow', { ownerType: GrowthBookOwnerType.Company }],
  EnableLeadManagement: [
    'leads_management_whitelist',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableFeedingMedicationPricingRule: [
    'enable_feeding_medication_pricing_rule',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableHideDefaultAreaInLodgingView: [
    'enable_hide_default_area_in_lodging_view',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableVetVerify: ['enable_vet_verify', { ownerType: GrowthBookOwnerType.Company }],
  EnableMultiPetBySlot: [
    'enable_multi_pet_by_slot',
    {
      ownerType: GrowthBookOwnerType.Business,
    },
  ],
  k9CompanyIdList: [
    'k9_company_ids',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableBoardingBundleServiceAndAddon: [
    'enable_boarding_add_on',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableHideAddCardInLodgingView: [
    'enable_hide_add_card_in_lodging_view',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  ExtraAmenities: [
    'extra_amenities',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  /**
   * 将metadata 的 EnableMembership 迁移到这里
   */
  EnableMembership: [
    'enable_membership',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableOpenApptAfterAcceptingRequest: [
    'enable_open_appt_after_accepting_request',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  DisableSplitLodgingEditPrice: [
    'disable_split_lodging_edit_price',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  EnableFulfillmentFlow: [
    'fulfillment_flow',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
  EnableMembershipRefund: [
    'enable_membership_refund',
    {
      ownerType: GrowthBookOwnerType.Enterprise,
    },
  ],
  AllowGroomingReportMultiPhoto: [
    'allow_grooming_report_multi_photo',
    {
      ownerType: GrowthBookOwnerType.Company,
    },
  ],
});

export type GrowthBookFeatureKey = EnumValues<typeof GrowthBookFeatureList>;
