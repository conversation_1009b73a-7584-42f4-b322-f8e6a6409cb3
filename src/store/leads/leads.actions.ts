import { action } from 'amos';

import { BffLeadsClient } from '../../middleware/bff';
import { createCustomerObservableAction } from '../observableServices/observableServices';
import {
  CustomerListFilterRecord,
  CustomerTypeEnum,
  businessCustomerListBox,
  customerMapBox,
} from '../customer/customer.boxes';
import { type GetCustomerListInput } from '../customer/customer.actions';
import { currentAccountIdBox } from '../account/account.boxes';
import { ClientFilterListSourceMap, leadInternalFilter } from '../customer/clientFilters.boxes';
import { petSummaryListBox } from '../pet/pet.boxes';
import { addClientFilter } from '../customer/clientFilters.action';
import {
  ComMoegoCommonParamsSortParamsOrder,
  ComMoegoCommonParamsSortParamsProperty,
} from '../../openApi/customer-schema';
import { DEFAULT_REQUEST_FILTER } from '../customer/clientView.utils';
import { filterEmptyValues } from '../../utils/common';
import { type GetCustomerListRequest, type HistoryLog_Convert } from '@moego/bff-openapi/clients/client.leads';
import { Customer_Type, HistoryLog_Type } from '@moego/bff-openapi/clients/client.leads';

export const getKanbanLeadsList = createCustomerObservableAction(
  'getKanbanLeadsList',
  async (
    _dispatch,
    _select,
    input: {
      data: GetCustomerListRequest;
    },
  ) => {
    const { data } = input;
    return await BffLeadsClient.getCustomerList(
      filterEmptyValues({ ...data, filter: { ...data.filter, type: Customer_Type.LEAD, customerIds: [] } }),
    );
  },
);

export const fetchLeadList = createCustomerObservableAction(
  'fetchLeadList',
  async (_dispatch, _select, input: GetCustomerListInput) => {
    const params = {
      ...input,
      filters: {
        ...input.filters,
        filters: Array.isArray(input.filters?.filters) ? input.filters?.filters : [],
      },
    };
    const res = await BffLeadsClient.getLeadList(params);
    return res;
  },
);

// 参照 getCustomerList 实现
export const getLeadList = action(async (dispatch, select, input: GetCustomerListInput) => {
  const accountId = select(currentAccountIdBox);
  const ownKey = CustomerListFilterRecord.ownKey(accountId, input.source, input.viewId);
  const state = select(businessCustomerListBox.mustGetItem(ownKey));
  const originListFilter = state.getFilter(input);
  dispatch(businessCustomerListBox.updateItem(ownKey, (v) => v.applyStart(input)));

  // TODO(xiaoyang): add abort controller
  // TODO(xiaoyang): 需要确认 input 是否需要转换 以及 bff 需要适配 smart list 参数(需要考虑兼容性)

  const res = await dispatch(fetchLeadList(originListFilter));
  // 目前几乎只更新了 customer 状态，但暂时没有使用 customerMap
  dispatch([
    businessCustomerListBox.updateItem(ownKey, (v) =>
      v.applySuccess(
        res.customers.map((customer) => +customer.id),
        res.total,
        originListFilter.pageNum,
        !!input.clear,
      ),
    ),
    customerMapBox.mergeItems(
      res.customers.map((customer) => ({
        ...customer,
        createTime: 0,
        updateTime: 0,
        type: CustomerTypeEnum.LEAD,
        customerId: +customer.id,
      })),
    ),
    petSummaryListBox.setList(accountId, [
      {
        petTypeId: -1,
        typeName: 'Total clients',
        count: res.total,
      },
    ]),
  ]);

  return res;
});

export const updateLeadListFilter = action(
  async (
    dispatch,
    select,
    { source = ClientFilterListSourceMap.LeadList, viewId = 0, ...input }: GetCustomerListInput,
  ) => {
    const accountId = select(currentAccountIdBox);
    const ownKey = CustomerListFilterRecord.ownKey(accountId, source, viewId);
    dispatch(
      businessCustomerListBox.updateItem(ownKey, (v) =>
        v.applyStart({
          ...input,
          silent: true,
        }),
      ),
    );
  },
);

// 临时代码，暂时只有一个 view
export const initializeLeadViewFilter = action(async (dispatch, select) => {
  const accountId = select(currentAccountIdBox);
  const ownKey = CustomerListFilterRecord.ownKey(accountId, ClientFilterListSourceMap.LeadList, 0);
  dispatch([
    businessCustomerListBox.updateItem(ownKey, (v) =>
      v.applyStart({
        sort: {
          order: ComMoegoCommonParamsSortParamsOrder.desc,
          property: ComMoegoCommonParamsSortParamsProperty.client_id,
        },
        filters: {
          ...DEFAULT_REQUEST_FILTER,
          filters: [leadInternalFilter],
        },
        silent: true,
      }),
    ),
    addClientFilter({
      source: ClientFilterListSourceMap.LeadList,
      ...leadInternalFilter,
    }),
  ]);
});

export const getLeadsConvertHistory = action(
  async (_dispatch, _select, input: { pageIndex: number; pageSize: number }) => {
    const res = await BffLeadsClient.listCompanyConvertHistory({
      pageNum: input.pageIndex,
      pageSize: input.pageSize,
    });
    return res;
  },
);

/**
 * client 是否曾经是 lead，lead 不应该调用该接口
 */
export const checkWasLead = action(async (_dispatch, _select, { customerId }: { customerId: number }) => {
  // list all history
  const { list: convertHistoryList } = await BffLeadsClient.listHistoryLog({
    customerId: customerId.toString(),
    pageNum: 1,
    // magic number，理论上一条用户 convert history 不会超过这个数量
    pageSize: 1000,
    filter: {
      type: HistoryLog_Type.CONVERT,
    },
  });
  // 判断条件：1.曾经包含一条记录：从 lead 到 customer 2. 当前是 customer(当前业务下一定满足，否则不应该调用该接口)
  const wasLead = convertHistoryList.some((log) => {
    const convertAction = log.action?.action;
    if (!convertAction || Array.isArray(convertAction)) return false;
    const convertValue = convertAction.value as HistoryLog_Convert;
    return convertValue.originType === Customer_Type.LEAD && convertValue.targetType === Customer_Type.CUSTOMER;
  });
  return wasLead;
});

/**
 * 通过 phoneNumber 检查 customer 内是否存在同 phone number 的 lead，如果存在则返回 true，否则返回 false
 */
export const checkPhoneNumberExistedWithLead = action(async (_dispatch, _select, input: { phoneNumber: string }) => {
  const { customers } = await BffLeadsClient.getCustomerList({
    filter: {
      mainPhoneNumber: input.phoneNumber,
      customerIds: [],
    },
  });
  if (!customers.length) {
    return {
      isExisted: false as const,
      customer: null,
    };
  }
  const customer = customers[0];
  const isExisted = customer.type === Customer_Type.LEAD;
  if (!isExisted) {
    return {
      isExisted: false as const,
      customer: null,
    };
  }
  return {
    isExisted,
    customer,
  };
});
