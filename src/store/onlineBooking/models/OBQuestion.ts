import {
  AcceptCustomerType,
  AcceptPetEntryType,
  ExistingClientAccessMode,
  ExistingPetAccessMode,
  NewClientAccessMode,
  NewPetAccessMode,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { Record } from 'immutable';
import { NormalBool } from '../../utils/createEnum';
import {
  ClientFixedQuestionSortMap,
  type ClientFixedQuestionSortMapKeys,
  type OBQuestionBEModal,
  type OBQuestionParams,
  OnlineBookingQuestionCategory,
  OnlineBookingQuestionType,
  PetFixedQuestionSortMap,
  type PetFixedQuestionSortMapKeys,
} from './OBQuestion.base';
import { OBQuestionExtraRecord } from './OBQuestionExtra';

// 前端扩展字段
export interface OBQuestionFEExtends {
  /** for extraJson */
  extraInfo: OBQuestionExtraRecord;
  /** pet weight limit */
  petWeightLimitNotify: number;
  petWeightLimit: string;
  petWeightOverTips: string;
}

// 基于后端数据扩展前端字段用
export interface OBQuestionOptionalExtends extends OBQuestionBEModal, Partial<OBQuestionFEExtends> {}

// 最终前端 model
export interface OBQuestionModel extends Omit<OBQuestionBEModal, 'extraJson'>, OBQuestionFEExtends {}

export class OBQuestionRecord extends Record<OBQuestionModel>({
  key: '',
  id: 0,
  question: '',
  placeholder: '',
  isShow: 1,
  isRequired: 0,
  type: OnlineBookingQuestionCategory.ForPet,
  acceptCustomerType: AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER,
  acceptPetEntryType: AcceptPetEntryType.NEW_AND_EXISTING,
  isAllowChange: 1,
  isAllowDelete: 1,
  isAllowEdit: 1,
  sort: 0,
  questionType: OnlineBookingQuestionType.ShortText,
  extraInfo: new OBQuestionExtraRecord(),

  petWeightLimitNotify: 0,
  petWeightLimit: '',
  petWeightOverTips: '',

  businessId: 0,
  status: 1, // 1 代表新增
  createTime: '',
  updateTime: '',
  newPetAccessMode: NewPetAccessMode.UNSPECIFIED,
  existingPetAccessMode: ExistingPetAccessMode.UNSPECIFIED,
  newClientAccessMode: NewClientAccessMode.UNSPECIFIED,
  existingClientAccessMode: ExistingClientAccessMode.UNSPECIFIED,
}) {
  static listKey(ownerId: number, category: number) {
    return `${ownerId}-${category}`;
  }

  get isNewQuestion() {
    return this.id <= 0;
  }

  get isCustomQuestion() {
    const { key } = this;
    return /^custom_/.test(key);
  }

  get isChecked() {
    return this.isShow === 1;
  }

  /**
   * 在列表中隐藏，不需要展示的问题
   * 1. 暂时就一个Vaccine_document
   * */
  get isNotAvailable() {
    return this.key === 'Vaccine_document';
  }

  /**
   * 展示在编辑列表中的
   */
  get isAvailableInEditor() {
    return NormalBool.truly(this.isShow) && !this.isNotAvailable;
  }

  get disabled() {
    return NormalBool.falsy(this.isAllowChange);
  }

  /**
   * 当用户选择触发特定条件，需要提交时后端删除，草稿的展示 / 编辑保持正常的情况
   */
  get shouldBeDeleted() {
    if (this.type === OnlineBookingQuestionCategory.ForPet) {
      return (
        this.newPetAccessMode === NewPetAccessMode.DISABLED &&
        this.existingPetAccessMode === ExistingPetAccessMode.DISABLED
      );
    } else if (this.type === OnlineBookingQuestionCategory.ForOwner) {
      return (
        this.newClientAccessMode === NewClientAccessMode.DISABLED &&
        this.existingClientAccessMode === ExistingClientAccessMode.DISABLED
      );
    }
    return false;
  }

  /**
   * 是否是固定保留的question
   */
  isPreservedQuestion(type: OnlineBookingQuestionCategory) {
    if (type === OnlineBookingQuestionCategory.ForPet) {
      return (
        PetFixedQuestionSortMap.mapLabels[PetFixedQuestionSortMap[this.question as PetFixedQuestionSortMapKeys]] ===
        'Preserved'
      );
    }
    return (
      ClientFixedQuestionSortMap.mapLabels[
        ClientFixedQuestionSortMap[this.question as ClientFixedQuestionSortMapKeys]
      ] === 'Preserved'
    );
  }

  get isAcceptCustomerTypeAvailable() {
    return this.type === OnlineBookingQuestionCategory.ForOwner;
  }

  get isAcceptPetEntryTypeAvailable() {
    return this.type === OnlineBookingQuestionCategory.ForPet;
  }

  get isShortTxt() {
    return this.questionType === OnlineBookingQuestionType.ShortText;
  }

  get isLongTxt() {
    return this.questionType === OnlineBookingQuestionType.LongText;
  }

  get isMultiOption() {
    const { questionType } = this;
    return (
      questionType === OnlineBookingQuestionType.Dropdown ||
      questionType === OnlineBookingQuestionType.Radio ||
      questionType === OnlineBookingQuestionType.Checkbox
    );
  }

  // 转化为后端保存用的参数
  toBEParams(extra?: Partial<OBQuestionParams>): OBQuestionParams {
    const values: Partial<OBQuestionOptionalExtends> = { ...this.toJSON() };
    // for new item
    if (this.isNewQuestion) {
      delete values.id;
      delete values.key;
    }
    // extraJson
    const extraInfo = values.extraInfo;
    if (extraInfo) {
      values.extraJson = extraInfo.toExtraJson();
    }

    // remove fe-only fields
    delete values.extraInfo;
    return Object.assign(values, extra);
  }
}
