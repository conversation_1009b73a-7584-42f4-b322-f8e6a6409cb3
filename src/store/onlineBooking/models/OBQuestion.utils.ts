import {
  ExistingClientAccessMode,
  ExistingPetAccessMode,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { OBClientQuestionKey, OBPetQuestionKey, OnlineBookingQuestionCategory } from './OBQuestion.base';

export const isServiceQuestionCategory = (type: OnlineBookingQuestionCategory) => {
  return type === OnlineBookingQuestionCategory.ForBoarding || type === OnlineBookingQuestionCategory.ForDaycare;
};

export const isExistingPetAccessModeEnabled = (mode?: ExistingPetAccessMode) =>
  mode === ExistingPetAccessMode.VIEW || mode === ExistingPetAccessMode.VIEW_AND_EDIT;

export const isExistingClientAccessModeEnabled = (mode?: ExistingClientAccessMode) =>
  mode === ExistingClientAccessMode.VIEW || mode === ExistingClientAccessMode.VIEW_AND_EDIT;

/**
 * 根据问题类型，获取对应问题的初始配置
 * 需注意的是：当前产品层面是定死的，但不排除有可能客户要求定制化的可能性，如果有需要定制化，需要单独再扩展一下 hook 来做
 */
export const getExistingPetAccessModeConfig = (questionKey: OBPetQuestionKey | string) => {
  let isModeChangeable = false;
  const defaultEnableValue = ExistingPetAccessMode.VIEW_AND_EDIT;

  switch (questionKey) {
    case OBPetQuestionKey.Weight:
    case OBPetQuestionKey.CoatType:
      isModeChangeable = true;
      break;
    default:
      break;
  }

  return {
    isModeChangeable,
    defaultEnableValue,
  };
};

export const getExistingClientAccessModeConfig = (questionKey: OBClientQuestionKey | string) => {
  const isModeChangeable = false;
  let defaultEnableValue = ExistingClientAccessMode.VIEW_AND_EDIT;

  switch (questionKey) {
    case OBClientQuestionKey.PhoneNumber:
    case OBClientQuestionKey.ReferralSource:
      defaultEnableValue = ExistingClientAccessMode.VIEW;
      break;
    default:
      break;
  }

  return {
    isModeChangeable,
    defaultEnableValue,
  };
};
