/**
 * OBQuestion 相关公共 interface 定义，只放最基础的 interface / type / enum，需避免循环引用
 * 另外这里也会沉淀关于 OB Question 数据结构的历史包袱以及处理的各种 workaround
 */
import {
  AcceptCustomerType,
  AcceptPetEntryType,
  ExistingClientAccessMode,
  ExistingPetAccessMode,
  type NewClientAccessMode,
  type NewPetAccessMode,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import SvgQuestionCheckboxSvg from '../../../assets/svg/question-checkbox.svg';
import SvgQuestionDropdownSvg from '../../../assets/svg/question-dropdown.svg';
import SvgQuestionLongTextSvg from '../../../assets/svg/question-long-text.svg';
import SvgQuestionRadioSvg from '../../../assets/svg/question-radio.svg';
import SvgQuestionShortTextSvg from '../../../assets/svg/question-short-text.svg';
import { type OpenApiModels } from '../../../openApi/schema';
import { type EnumKeys, createEnum, createStrictEnum } from '../../utils/createEnum';

// 后端返回数据
export interface OBQuestionBEModal
  extends Omit<OpenApiModels['GET/grooming/bookOnline/question']['Res']['data']['questions'][0], ''> {
  /**
   * OB Question 的唯一标识符，非 Custom Question 也依赖它区分不同的问题类型
   *
   * 注意：
   * 1. 后端没有针对题目种类真正写死 Question Key，这里的 Key 是用 Question Name 自动用 _ 拼出来的，是一个隐式依赖；
   * 2. 所有针对 Question 的判断逻辑，需要从 OBClientQuestionKey / OBPetQuestionKey 两个枚举出发来处理，避免遗漏，如有新增，也要加到这俩枚举里面
   *
   * 更多上下文参见：https://mengshikeji.feishu.cn/wiki/ZKA0w0XDii52oEksu1Lc4iZWnVN
   */
  key: string | OBClientQuestionKey | OBPetQuestionKey;
  /**
   * Custom Question 的类型
   */
  questionType: QuestionType;
  /** @deprecated accept customer type, replaced with newClientAccessMode / existingClientAccessMode */
  acceptCustomerType: AcceptCustomerType;
  /** @deprecated accept pet entry type, replaced with newPetAccessMode / existingPet */
  acceptPetEntryType: AcceptPetEntryType;
  // 新增 Pet Question 相关字段
  newPetAccessMode: NewPetAccessMode;
  existingPetAccessMode: ExistingPetAccessMode;
  // 新增 Client Question 相关字段
  newClientAccessMode: NewClientAccessMode;
  existingClientAccessMode: ExistingClientAccessMode;
}

// 后端保存参数
export interface OBQuestionParams
  extends Partial<Exclude<OpenApiModels['PUT/grooming/bookOnline/questionList']['Req']['questions'], undefined>[0]> {}

/**
 * Pet Question 的 key 定义，注意 key 的历史包袱和隐患点
 */
export enum OBPetQuestionKey {
  PetImage = 'Pet_image',
  Breed = 'Pet_breed',
  PetType = 'Pet_type',
  PetName = 'Pet_name',
  Fixed = 'Fixed',
  Birthday = 'Birthday',
  Gender = 'Gender',
  VaccineList = 'Vaccine',
  VaccineDocument = 'Vaccine_document',
  Weight = 'Weight',
  VetName = 'Vet_name',
  VetPhone = 'Vet_phone_number',
  VetAddress = 'Vet_address',
  EmergencyContact = 'Emergency_contact',
  HealthIssues = 'Health_issues',
  Behavior = 'Behavior',
  HairLength = 'Hair_length',
  CoatType = 'Coat_type',
}

/**
 * Client Question 的 key 定义
 */
export enum OBClientQuestionKey {
  FirstName = 'First_name',
  LastName = 'Last_name',
  PhoneNumber = 'Phone_number',
  EmergencyContact = 'Emergency_contact',
  Email = 'Email',
  Address = 'Address',
  ReferralSource = 'Referral_source',
  PreferredGroomer = 'Preferred_groomer',
  PreferredFrequency = 'Preferred_frequency',
  PreferredDayOfTheWeek = 'Preferred_day_of_the_week',
  PreferredTimeOfTheDay = 'Preferred_time_of_the_day',
  Birthday = 'Birthday',
  PeopleAuthorizeToPickupPets = 'People_authorized_to_pickup_pets',
}

/** addtional client questions fixed order */
export const ClientAdditionFixedOrder = {
  Email: 3,
  Address: 4,
  'Referral source': 5,
  'Preferred groomer': 6,
  'Preferred frequency': 8,
  'Preferred day of the week': 9,
  'Preferred time of the day': 10,
};

/**
 * 决定pet 左侧 Question List的展示顺序, label表示是否为首选字段
 */
export const PetFixedQuestionSortMap = createEnum({
  'Pet type': [0, 'Preserved'],
  'Pet name': [1, 'Preserved'],
  'Pet breed': [2, 'Preserved'],
  Weight: [3, 'Preserved'],
  'Coat type': [4, 'Preserved'],
});

export type PetFixedQuestionSortMapKeys = EnumKeys<typeof PetFixedQuestionSortMap>;

/** addtional pet questions fixed order */
export const PetAdditionQuestionFixedOrder = {
  Vaccine: 5,
  Birthday: 6,
  Gender: 7,
  Fixed: 8,
  'Pet image': 9,
  Behavior: 10,
  'Vet name': 11,
  'Vet phone number': 12,
  'Vet address': 13,
  'Emergency contact': 14,
  'Health issues': 15,
};

export const ClientFixedQuestionSortMap = createEnum({
  'First name': [0, 'Preserved'],
  'Last name': [1, 'Preserved'],
  'Phone number': [2, 'Preserved'],
});

export type ClientFixedQuestionSortMapKeys = EnumKeys<typeof ClientFixedQuestionSortMap>;

/**
 * 需注意 intake form 也在用
 */
export const enum OnlineBookingQuestionCategory {
  ForPet = 1,
  ForOwner = 2,
  /**
   * BD 专用
   */
  ForBoarding = 3,
  ForDaycare = 4,
}

export const OnlineBookingQuestionCategoryMap = {
  [OnlineBookingQuestionCategory.ForPet]: 'pet',
  [OnlineBookingQuestionCategory.ForOwner]: 'client',
  [OnlineBookingQuestionCategory.ForBoarding]: 'boarding',
  [OnlineBookingQuestionCategory.ForDaycare]: 'daycare',
};

export enum QuestionType {
  ShortText = 1,
  LongText = 2,
  Dropdown = 3,
  Radio = 4,
  Checkbox = 5,
}

/**
 * 需注意 intake form 也在用，需解耦合
 */
// 1: short txt 2: long txt 3: drop down 4: radio 5: check box
export const OnlineBookingQuestionType = createEnum({
  ShortText: [QuestionType.ShortText, { icon: SvgQuestionShortTextSvg, label: 'Short text answer' }],
  LongText: [QuestionType.LongText, { icon: SvgQuestionLongTextSvg, label: 'Long text answer' }],
  Dropdown: [QuestionType.Dropdown, { icon: SvgQuestionDropdownSvg, label: 'Dropdown' }],
  Radio: [QuestionType.Radio, { icon: SvgQuestionRadioSvg, label: 'Radio buttons' }],
  Checkbox: [QuestionType.Checkbox, { icon: SvgQuestionCheckboxSvg, label: 'Checkboxes' }],
});

/**
 * 单独处理渲染的 Date 类型，但不出现在 custom question 的选项
 * 目前这里只有 intake form 在用
 * TODO: 直接在 question 扩展一个字段，不需要独立 enum
 * 6: birthday for customer, but it is using date type in question others are string type
 */
export const OnlineBookingNonCustomQuestionType = createEnum({
  Date: [6, { label: 'Birthday' }],
} as const);

export const AcceptCustomerTypeEnum = createStrictEnum({
  All: [AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER, { sort: 1, label: 'All' }],
  New: [AcceptCustomerType.NEW_CUSTOMER, { sort: 2, label: 'New visitors' }],
  Existing: [AcceptCustomerType.EXISTING_CUSTOMER, { sort: 3, label: 'Existing clients' }],
  Unspecified: [AcceptCustomerType.UNSPECIFIED, { sort: 999, label: 'Unspecified' }],
});

export const acceptCustomerTypeOptions = AcceptCustomerTypeEnum.values
  .filter((v) => v !== AcceptCustomerTypeEnum.Unspecified)
  .map((value) => ({
    label: AcceptCustomerTypeEnum.mapLabels[value].label,
    value,
  }))
  .sort((a, b) => AcceptCustomerTypeEnum.mapLabels[a.value].sort - AcceptCustomerTypeEnum.mapLabels[b.value].sort);

export const AcceptPetEntryTypeEnum = createStrictEnum({
  All: [AcceptPetEntryType.NEW_AND_EXISTING, { sort: 1, label: 'All' }],
  New: [AcceptPetEntryType.NEW, { sort: 2, label: 'New pets' }],
  Existing: [AcceptPetEntryType.EXISTING, { sort: 3, label: 'Existing pets' }],
  Unspecified: [AcceptPetEntryType.UNSPECIFIED, { sort: 999, label: 'Unspecified' }],
});

export const acceptPetEntryTypeOptions = AcceptPetEntryTypeEnum.values
  .filter((v) => v !== AcceptPetEntryTypeEnum.Unspecified)
  .map((value) => ({
    label: AcceptPetEntryTypeEnum.mapLabels[value].label,
    value,
  }))
  .sort((a, b) => AcceptPetEntryTypeEnum.mapLabels[a.value].sort - AcceptPetEntryTypeEnum.mapLabels[b.value].sort);

export const ExistingPetAccessModeEnum = createStrictEnum({
  View: [ExistingPetAccessMode.VIEW, { sort: 1, label: 'View only' }],
  ViewAndEdit: [ExistingPetAccessMode.VIEW_AND_EDIT, { sort: 2, label: 'View and edit' }],
  /** 不勾选的情况 */
  Disabled: [ExistingPetAccessMode.DISABLED, { sort: 998, label: '', hidden: true }],
  /** 未初始化情况，业务不应依赖 */
  Unspecified: [ExistingPetAccessMode.UNSPECIFIED, { sort: 999, label: '', hidden: true }],
});

export const ExistingPetAccessModeOptions = ExistingPetAccessModeEnum.values
  .filter((v) => !ExistingPetAccessModeEnum.mapLabels[v].hidden)
  .sort((a, b) => ExistingPetAccessModeEnum.mapLabels[a].sort - ExistingPetAccessModeEnum.mapLabels[b].sort)
  .map((v) => ({
    value: v,
    label: ExistingPetAccessModeEnum.mapLabels[v].label,
  }));

export const ExistingClientAccessModeEnum = createStrictEnum({
  View: [ExistingClientAccessMode.VIEW, { sort: 1, label: 'View only' }],
  ViewAndEdit: [ExistingClientAccessMode.VIEW_AND_EDIT, { sort: 2, label: 'View and edit' }],
  /** 不勾选的情况 */
  Disabled: [ExistingClientAccessMode.DISABLED, { sort: 998, label: '', hidden: true }],
  /** 未初始化情况，业务不应依赖 */
  Unspecified: [ExistingClientAccessMode.UNSPECIFIED, { sort: 999, label: '', hidden: true }],
});

export const ExistingClientAccessModeOptions = ExistingClientAccessModeEnum.values
  .filter((v) => !ExistingClientAccessModeEnum.mapLabels[v].hidden)
  .sort((a, b) => ExistingClientAccessModeEnum.mapLabels[a].sort - ExistingClientAccessModeEnum.mapLabels[b].sort)
  .map((v) => ({
    value: v,
    label: ExistingClientAccessModeEnum.mapLabels[v].label,
  }));
