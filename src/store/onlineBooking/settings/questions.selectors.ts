import { selector } from 'amos';
import { Set as ISet } from 'immutable';
import { isEqual, pick } from 'lodash';
import { currentBusinessIdBox } from '../../business/business.boxes';
import { type OBQuestionModel } from '../models/OBQuestion';
import {
  ClientFixedQuestionSortMap,
  OnlineBookingQuestionCategory,
  PetFixedQuestionSortMap,
  type PetFixedQuestionSortMapKeys,
} from '../models/OBQuestion.base';
import {
  ADD_QUESTION_ID,
  draftOnlineBookingQuestionListBox,
  draftOnlineBookingQuestionMapBox,
  onlineBookingQuestionListBox,
  onlineBookingQuestionMapBox,
} from './questions.boxes';

export const selectOnlineBookingQuestionIdList = selector(
  (select, type: OnlineBookingQuestionCategory, businessId: number = select(currentBusinessIdBox)) => {
    return select(onlineBookingQuestionListBox).getList(`${businessId}-${type}`);
  },
);

export const selectDraftOnlineBookingQuestionIdList = selector(
  (select, type: OnlineBookingQuestionCategory, businessId: number = select(currentBusinessIdBox)) => {
    return select(draftOnlineBookingQuestionListBox).getList(`${businessId}-${type}`);
  },
);

export const selectDraftQuestionIdInFixedSort = selector((select, type: OnlineBookingQuestionCategory) => {
  const ids = select(selectDraftOnlineBookingQuestionIdList(type));
  const sortMap = (
    type === OnlineBookingQuestionCategory.ForPet ? PetFixedQuestionSortMap : ClientFixedQuestionSortMap
  ) as typeof PetFixedQuestionSortMap;
  const cleanIds = ids.filter((id) => id !== ADD_QUESTION_ID);
  return cleanIds.sort((aId, bId) => {
    const aItem = select(draftOnlineBookingQuestionMapBox.mustGetItem(aId));
    const bItem = select(draftOnlineBookingQuestionMapBox.mustGetItem(bId));
    const aOrder = sortMap[aItem.question as PetFixedQuestionSortMapKeys];
    const bOrder = sortMap[bItem.question as PetFixedQuestionSortMapKeys];
    const hasAOrder = typeof aOrder === 'number';
    const hasBOrder = typeof bOrder === 'number';
    if (hasAOrder && hasBOrder) {
      return aOrder - bOrder;
    }
    if (hasAOrder) {
      return -1;
    }
    if (hasBOrder) {
      return 1;
    }
    return aItem.id - bItem.id;
  });
});

export const selectDraftVaccineDocumentQuestion = selector((select) => {
  const list = select(selectDraftQuestionList(OnlineBookingQuestionCategory.ForPet));
  const vaccineDocument = list.find((i) => i.question === 'Vaccine document')!;
  return vaccineDocument;
});

export const selectDraftVaccineQuestionFields = selector((select) => {
  const list = select(selectDraftQuestionList(OnlineBookingQuestionCategory.ForPet));
  const vaccine = list.find((i) => i.question === 'Vaccine');
  return {
    showExpirationDate: vaccine?.extraInfo?.vaccineTypeInfo?.showExpirationDate ?? true,
    requireExpirationDate: vaccine?.extraInfo?.vaccineTypeInfo?.requireExpirationDate ?? true,
    showVaccineDocument: vaccine?.extraInfo?.vaccineTypeInfo?.showVaccineDocument ?? false,
    requireVaccineDocument: vaccine?.extraInfo?.vaccineTypeInfo?.requireVaccineDocument ?? false,
  };
});

export const selectDraftQuestionList = selector(
  (select, type: OnlineBookingQuestionCategory, businessId: number = select(currentBusinessIdBox)) => {
    const ids = select(draftOnlineBookingQuestionListBox).getList(`${businessId}-${type}`);
    return ids
      .filter((id) => id !== ADD_QUESTION_ID)
      .map((questionId) => select(draftOnlineBookingQuestionMapBox.mustGetItem(questionId)));
  },
);

export const selectDraftQuestionSnapshot = selector(
  (select, type: OnlineBookingQuestionCategory, businessId: number = select(currentBusinessIdBox)) => {
    const idList = select(draftOnlineBookingQuestionListBox).getList(`${businessId}-${type}`);
    return idList.map((id) => select(draftOnlineBookingQuestionMapBox).mustGetItem(id)).toArray();
  },
);

export const selectDraftQuestionDirty = selector(
  (select, type: OnlineBookingQuestionCategory, _businessId: number = select(currentBusinessIdBox)) => {
    const originIdList = select(selectOnlineBookingQuestionIdList(type));
    const setOriginIds = ISet(originIdList);
    const draftIdList = select(selectDraftOnlineBookingQuestionIdList(type)).filter((i) => i !== ADD_QUESTION_ID);
    const setDraftIds = ISet(draftIdList);
    const hasAdded = setDraftIds.subtract(setOriginIds).size > 0;
    const hasDeleted = setOriginIds.subtract(setDraftIds).size > 0;
    const isNotEqual = !isEqual(originIdList.toArray(), draftIdList.toArray());
    if (hasAdded || hasDeleted || isNotEqual) {
      return true;
    }

    // modified questions
    const hasModified = originIdList.some((id) => {
      const originItem = select(onlineBookingQuestionMapBox.mustGetItem(id));
      const targetItem = select(draftOnlineBookingQuestionMapBox.mustGetItem(id));
      const diffKeys: (keyof OBQuestionModel)[] = [
        'question',
        'isRequired',
        'isShow',
        /** @deprecated 两个字段即将废弃 */
        'acceptCustomerType',
        'acceptPetEntryType',
        // pet weight
        'petWeightLimitNotify',
        'petWeightLimit',
        'petWeightOverTips',
        // custom question
        'placeholder',
        'questionType',
        // pet question
        'newPetAccessMode',
        'existingPetAccessMode',
        // client question
        'newClientAccessMode',
        'existingClientAccessMode',
      ];
      const compareOriginItem = pick(originItem, diffKeys);
      const compareDraftItem = pick(targetItem, diffKeys);
      const compareDraftExtraItem = originItem.extraInfo;
      const compareOriginExtraItem = targetItem.extraInfo;
      return !(isEqual(compareOriginItem, compareDraftItem) && compareDraftExtraItem.isEqualTo(compareOriginExtraItem));
    });
    return hasModified;
  },
);
