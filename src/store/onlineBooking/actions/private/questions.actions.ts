import { action } from 'amos';
import { type List } from 'immutable';
import isNumber from 'lodash/isNumber';
import { http } from '../../../../middleware/api';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import { createOnlineBookingObservableAction } from '../../../observableServices/observableServices';
import { get } from '../../../utils/utils';
import { type OBQuestionModel, type OBQuestionOptionalExtends, type OBQuestionRecord } from '../../models/OBQuestion';
import {
  AcceptCustomerTypeEnum,
  AcceptPetEntryTypeEnum,
  ClientFixedQuestionSortMap,
  type ClientFixedQuestionSortMapKeys,
  OnlineBookingQuestionCategory,
  PetFixedQuestionSortMap,
  type PetFixedQuestionSortMapKeys,
} from '../../models/OBQuestion.base';
import { OBQuestionExtraRecord } from '../../models/OBQuestionExtra';
import {
  ADD_QUESTION_ID,
  draftOnlineBookingQuestionListBox,
  draftOnlineBookingQuestionMapBox,
  onlineBookingQuestionListBox,
  onlineBookingQuestionMapBox,
} from '../../settings/questions.boxes';
import { selectDraftQuestionList, selectOnlineBookingQuestionIdList } from '../../settings/questions.selectors';

const getPreloadingData = async (type: OnlineBookingQuestionCategory) => {
  return Promise.all([
    http.open('GET/grooming/bookOnline/questionList', { type }).then((r) => r.questions),
    http
      .open('GET/grooming/bookOnline/setting/info', { withoutClientNotification: true })
      .then((r) => r.data.bookOnlineInfo),
    http.open('GET/business/agreement/list').then((r) => r.data),
  ]);
};

export const getOnlineBookingQuestions = createOnlineBookingObservableAction(
  'getOnlineBookingQuestions',
  async (dispatch, select, type: OnlineBookingQuestionCategory, businessId: number = select(currentBusinessIdBox)) => {
    const [list, bookOnlineInfo] = await getPreloadingData(type);
    const questions = list.map((item) => {
      const newItem: OBQuestionOptionalExtends = {
        ...item,
        acceptCustomerType: item.acceptCustomerType ?? AcceptCustomerTypeEnum.All,
        acceptPetEntryType: item.acceptPetEntryType ?? AcceptPetEntryTypeEnum.All,
        extraInfo: OBQuestionExtraRecord.fromQuestion(item),
      };
      // sync with pet weight
      if (newItem.key === 'Weight') {
        const { weightLimit, weightLimitNotify, overLimitTips } = bookOnlineInfo;
        newItem.petWeightLimitNotify = weightLimitNotify;
        newItem.petWeightLimit = isNumber(weightLimit) ? String(weightLimit) : '';
        newItem.petWeightOverTips = overLimitTips;
      }
      return newItem;
    });
    const forPet = type === OnlineBookingQuestionCategory.ForPet;
    const forClient = type === OnlineBookingQuestionCategory.ForOwner;
    const sortWithPreservedFields = (a: OBQuestionOptionalExtends, b: OBQuestionOptionalExtends) => {
      if (forPet || forClient) {
        const qa = a.question;
        const qb = b.question;
        const orderTa = forPet
          ? PetFixedQuestionSortMap[qa as PetFixedQuestionSortMapKeys]
          : ClientFixedQuestionSortMap[qa as ClientFixedQuestionSortMapKeys];
        const orderTb = forPet
          ? PetFixedQuestionSortMap[qb as PetFixedQuestionSortMapKeys]
          : ClientFixedQuestionSortMap[qb as ClientFixedQuestionSortMapKeys];
        const hasTa = typeof orderTa === 'number';
        const hasTb = typeof orderTb === 'number';
        if (hasTa && hasTb) {
          return orderTa - orderTb;
        }
        if (hasTa) {
          return -1;
        }
        if (hasTb) {
          return 1;
        }
      }
      // 后端的排序，sort大的在前面
      return b.sort - a.sort;
    };
    const questionIds = [...questions].sort(sortWithPreservedFields).map(get('id'));
    dispatch([
      onlineBookingQuestionMapBox.mergeItems(questions),
      draftOnlineBookingQuestionMapBox.mergeItems(questions),
      onlineBookingQuestionListBox.setList(`${businessId}-${type}`, questionIds),
      draftOnlineBookingQuestionListBox.setList(`${businessId}-${type}`, [...questionIds]),
    ]);
  },
);

export const setDraftQuestion = action(
  async (dispatch, select, questionId: number, input: Partial<OBQuestionModel>) => {
    dispatch(draftOnlineBookingQuestionMapBox.mergeItem(questionId, input));
  },
);

export const setDraftSortOrder = action(
  async (
    dispatch,
    select,
    newList: number[] | List<number>,
    type: OnlineBookingQuestionCategory,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    dispatch(draftOnlineBookingQuestionListBox.setList(`${businessId}-${type}`, newList));
  },
);

export const removeDraftQuestion = action(
  async (
    dispatch,
    select,
    id: number,
    type: OnlineBookingQuestionCategory,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const currentList = select(draftOnlineBookingQuestionListBox).getList(`${businessId}-${type}`);
    const newList = currentList.filter((currentId) => currentId !== id);
    dispatch(setDraftSortOrder(newList, type));
    dispatch(draftOnlineBookingQuestionMapBox.deleteItem(id));
  },
);

export const resetDraftQuestionFromSnapshot = action(
  async (dispatch, select, type: OnlineBookingQuestionCategory, snapshotList: OBQuestionRecord[]) => {
    const businessId = select(currentBusinessIdBox);
    const snapshotIds = snapshotList.map((i) => i.id);
    dispatch([
      draftOnlineBookingQuestionMapBox.mergeItems(snapshotList),
      draftOnlineBookingQuestionListBox.setList(`${businessId}-${type}`, snapshotIds),
    ]);
  },
);

export const addNewQuestionTempl = action(
  async (dispatch, select, type: OnlineBookingQuestionCategory, businessId: number = select(currentBusinessIdBox)) => {
    dispatch(draftOnlineBookingQuestionListBox.pushList(`${businessId}-${type}`, ADD_QUESTION_ID));
  },
);

/**
 * 删掉自定义问题
 */
export const removeCustomQuestionId = action(
  async (
    dispatch,
    select,
    id: number,
    type: OnlineBookingQuestionCategory,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const list = select(draftOnlineBookingQuestionListBox.getList(`${businessId}-${type}`));
    const addTempIndex = list.findIndex((i) => i === ADD_QUESTION_ID);
    list.splice(addTempIndex, 1);
    dispatch(setDraftSortOrder(list, type));
  },
);

export const addNewCustomQuestion = action(
  async (
    dispatch,
    select,
    input: Partial<OBQuestionModel>,
    type: OnlineBookingQuestionCategory,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const list = select(draftOnlineBookingQuestionListBox.getList(`${businessId}-${type}`));
    const addTempIndex = list.findIndex((i) => i === ADD_QUESTION_ID);
    const id = Math.min(...list.toArray());
    const newId = id - 1;
    const newList = list.splice(addTempIndex, 1, newId);
    const params: Partial<OBQuestionModel> = { ...input, type, key: `custom_${newId}` };
    dispatch(draftOnlineBookingQuestionMapBox.mergeItem(newId, params));
    dispatch(setDraftSortOrder(newList, type));
  },
);

export const updateQuestionList = createOnlineBookingObservableAction(
  'updateQuestionList',
  async (dispatch, select, type: OnlineBookingQuestionCategory, businessId: number = select(currentBusinessIdBox)) => {
    const originIds = select(selectOnlineBookingQuestionIdList(type));
    const draftQuestionList = select(selectDraftQuestionList(type)).filter((item) => !item.shouldBeDeleted);
    const questions = draftQuestionList.map((item, index) => {
      return item.toBEParams({
        sort: draftQuestionList.size - index,
      });
    });
    /**
     * 两种情况需要后端删除：
     * 1. originIds 中存在，但 draftQuestionList 中不存在
     * 2. originIds 中存在，但 draftQuestionList 中被标记为 deleted 的情况，这里上方已做过滤
     */
    const delItems = originIds.filter((id) => !questions.some((i) => i.id === id)).map((id) => ({ id, status: 0 }));
    const paramList = [...questions.toArray(), ...delItems.toArray()];
    // sync question list
    const postQuestionList = http.open('PUT/grooming/bookOnline/questionList', { businessId, questions: paramList });

    // sync pet weight
    let postPetWeight;
    const questionPetWeight = draftQuestionList.find((i) => i.question === 'Weight');
    if (questionPetWeight) {
      const {
        petWeightLimitNotify: weightLimitNotify,
        petWeightLimit: weightLimit,
        petWeightOverTips: overLimitTips,
      } = questionPetWeight;
      postPetWeight = http.open('PUT/grooming/bookOnline/setting/info', {
        weightLimitNotify,
        weightLimit: weightLimit as unknown as number,
        overLimitTips,
      });
    }

    await Promise.all([postQuestionList, postPetWeight]);
    await dispatch(getOnlineBookingQuestions(type));
  },
);
