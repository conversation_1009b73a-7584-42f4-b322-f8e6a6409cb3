/*
 * @since 2020-11-19 10:01:58
 * <AUTHOR> <<EMAIL>>
 */

import { type ComMoegoServerGroomingParamsPetDetailParamsServiceItemEnum } from '@moego/finance-utils/dist/esm/src/types/openApi/grooming-schema';
import { Record } from 'immutable';
import { ANY } from 'monofile-utilities/lib/consts';
import { type Replace } from '../../config/interface';
import { type OpenApiModels } from '../../openApi/schema';
import { LoadMoreList } from '../utils/LoadMoreList';
import { PureRecord } from '../utils/PagedList';
import { type RecordInstance, createRecordMapBox } from '../utils/RecordMap';
import { ID_ANONYMOUS } from '../utils/identifier';

export enum ActivityNotificationType {
  APPT_CREATED = 'APPT_CREATED',
  APPT_ASSIGNED = 'APPT_ASSIGNED',
  APPT_RESCHEDULED = 'APPT_RESCHEDULED',
  APPT_CANCELLED = 'APPT_CANCELLED',
  APPT_CANCELLED_BY_CLIENT = 'APPT_CANCELLED_BY_CLIENT',
  APPT_CONFIRMED_BY_CLIENT = 'APPT_CONFIRMED_BY_CLIENT',
  CLIENT_UPDATE_APPOINTMENT = 'CLIENT_UPDATE_APPOINTMENT',
  OB_REQUEST = 'OB_REQUEST',
  OB_REQUEST_RESCHEDULE = 'OB_REQUEST_RESCHEDULE',
  OB_REQUEST_CANCEL = 'OB_REQUEST_CANCEL',
  OB_ABANDONED = 'OB_ABANDONED',
  OB_RENEW_END_DATE = 'RENEW_ONLINE_BOOKING_END_DATE',
  CLIENT_UPDATE_PET = 'CLIENT_UPDATE_PET',
  CLIENT_UPDATE_CUSTOMER = 'CLIENT_UPDATE_CUSTOMER',
  NEW_INTAKE_FORM = 'NEW_INTAKE_FORM',
  INVOICE_PAID = 'INVOICE_PAID',
  AGREEMENT_SIGNED = 'AGREEMENT_SIGNED',
  REVIEW_SUBMITTED = 'REVIEW_SUBMITTED',
  PAYMENT_DISPUTE = 'PAYMENT_DISPUTE',
  FINANCE_CAPITAL_OFFER_APPROVED = 'FINANCE_CAPITAL_OFFER_APPROVED',
  FINANCE_CAPITAL_OFFER_CREATED = 'FINANCE_CAPITAL_OFFER_CREATED',
  ACCOUNTING_SET_UP_REMINDER = 'ACCOUNTING_SET_UP_REMINDER',

  // Membership 相关
  BUY_MEMBERSHIP_VIA_BRANDED_APP = 'BUY_MEMBERSHIP_VIA_BRANDED_APP',
  MEMBERSHIP_PAYMENT_FAILED = 'MEMBERSHIP_PAYMENT_FAILED',

  ASSIGNED_TASK = 'ASSIGNED_TASK',

  WAITLIST_REQUEST = 'WAITLIST_SIGNED_UP',
  WAITLIST_SLOT_AVAILABLE = 'WAITLIST_AVAILABLE',
}

export enum SystemNotificationType {
  SUB_PAYMENT_FAIL = 'SUB_PAYMENT_FAIL',
  SUB_PAYMENT_SUCCESS = 'SUB_PAYMENT_SUCCESS',
  A2P_REQUIRED = 'A2P_REQUIRED',
  A2P_FAIL = 'A2P_FAIL',
  TWILIO_FINE = 'TWILIO_FINE',
  GOOGLE_RESERVE_GEO_MATCHED = 'GOOGLE_RESERVE_GEO_MATCHED',
  GOOGLE_RESERVE_GEO_UNMATCHED = 'GOOGLE_RESERVE_GEO_UNMATCHED',
  PLATFORM_MARKETING_CAMPAIGN = 'PLATFORM_MARKETING_CAMPAIGN',
  // TODO: incoming types
  // SMS_AMOUNT_LOW = 'SMS_AMOUNT_LOW',
  // STRIPE_RESTRICTED = 'STRIPE_RESTRICTED',
  // PAYMENT_DISPUTED = 'PAYMENT_DISPUTED',
}

export enum PendingReviewNotificationType {
  PET_VACCINE_REQUEST = 'PET_VACCINE_REQUEST',
}

export enum UnknownNotificationType {
  UNKNOWN = 'UNKNOWN',
}

export const MoeGoNotification = {
  ...ActivityNotificationType,
  ...SystemNotificationType,
  ...PendingReviewNotificationType,
  ...UnknownNotificationType,
};
export type NotificationType =
  | ActivityNotificationType
  | SystemNotificationType
  | PendingReviewNotificationType
  | UnknownNotificationType;

export const OB_NOTIFICATION_TYPE_LIST: NotificationType[] = [
  MoeGoNotification.OB_REQUEST,
  MoeGoNotification.OB_REQUEST_RESCHEDULE,
  MoeGoNotification.OB_REQUEST_CANCEL,
];

export const OB_SETTING_NOTIFICATION_TYPE_LIST: NotificationType[] = [
  // MoeGoNotification.GOOGLE_RESERVE_GEO_MATCHED 不用加进来，因为点击它不需要跳转 setting 页面，所以无需校验权限
  MoeGoNotification.GOOGLE_RESERVE_GEO_UNMATCHED,
  MoeGoNotification.OB_RENEW_END_DATE,
];

export interface NotificationApptAssignedModel {
  groomingId: number;
  appointmentDate: string;
  appointmentStartTime: number;
  appointmentEndTime: number;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
}

export interface NotificationApptBaseModel {
  groomingId: number;
  appointmentDate: string;
  appointmentStartTime: number;
  appointmentEndTime: number;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
}

export interface NotificationApptCancelledModel extends NotificationApptBaseModel {}
export interface NotificationApptCancelledByClientModel extends NotificationApptBaseModel {}
export interface NotificationApptConfirmedByClientModel extends NotificationApptBaseModel {}
export interface NotificationAppointmentUpdatedByClientModel extends NotificationApptBaseModel {}

export interface NotificationApptRescheduledModel {
  groomingId: number;
  appointmentDate: string;
  appointmentStartTime: number;
  appointmentEndTime: number;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
}

export interface NotificationOBRequestModel {
  groomingId: number;
  appointmentDate: string;
  appointmentStartTime: number;
  appointmentEndTime: number;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
  // ob request 支持只选日期，这种情况下 appointmentStartTime 为 0，需要用 noStartTime 标记这个值是否可信
  noStartTime: boolean;
}

export interface NotificationOBUpdatePetClientModel {
  petId: number;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
  petName: string;
  updateTime: number;
}

export interface NotifiactionOBAbandoned {
  customerFirstName: string;
  customerLastName: string;
  abandonStep: string;
}

export interface NotificationOBRenewEndDateModel {
  bookingRangeEndDate: string;
}

export interface NotificationNewIntakeFormModel {
  groomingId: number;
  appointmentDate: string;
  appointmentStartTime: number;
  appointmentEndTime: number;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
}

export interface NotificationInvoicePaidModel {
  invoiceId: number;
  invoiceType: string;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
}

export interface NotificationAgreementSignedModel {
  groomingId: number;
  appointmentDate: string;
  appointmentStartTime: number;
  appointmentEndTime: number;
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
  agreementHeader: string;
}

export interface NotificationReviewSubmittedModel {
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
  source: number;
}

export interface NotificationSubscriptionFailedModel {
  billingExpireTime: number;
}

export interface NotificationSubscriptionSuccessModel {
  amount: number;
  last4: string;
}

export interface NotificationA2PRegisterModel {
  companyId: number;
  failReason?: string;
}

export interface TwilioFineModel {
  businessId: number;
  companyId: number;
  customerId: number;
}

export interface NotificationGoogleReserveModel {}

export interface NotificationPlatformMarketingCampaignModel {
  campaignUrl: string;
  iconUrl: string;
}

export interface PaymentDisputeNotificationModel {
  disputeId: string;
  disputeAmount: number;
  paymentId: string;
  customerId: string;
  customerFirstName: string;
  customerLastName: string;
}

export interface NotificationFinanceCapitalOfferApprovedModel {
  offerId: string;
  amount: number;
}

export interface NotificationFinanceCapitalOfferCreatedModel {
  offerId: string;
  amount: number;
}

export interface NotificationAccountingSetupReminderModel {
  title: string;
  body: string;
}

export interface TaskManagementModal {
  startDate: string;
  startTime: number;
  assignCount: number;
}

export interface WaitlistSlotAvailableModel {
  serviceItemType: ComMoegoServerGroomingParamsPetDetailParamsServiceItemEnum;
  startDate: string;
  endDate: string;
}

export interface WaitlistRequestModel {
  customerId: number;
  firstName: string;
  lastName: string;
  startDate: string;
  endDate: string;
  waitlistId: string;
  serviceItemType: ComMoegoServerGroomingParamsPetDetailParamsServiceItemEnum[];
}

export interface MembershipPaymentFailedModel {
  customerId: number;
  subscriptionId: string;
  membershipName: string;
  failureMessage: string;
}

export interface MembershipNewSubscriptionModel {
  customerFirstName: string;
  customerLastName: string;
  membershipName: string;
  purchasedTime: string;
  subscriptionId: string;
  customerId: number;
}

export interface VaccineUpdateModel {
  customerId: number;
  customerFirstName: string;
  customerLastName: string;
  createTime: number;
  petVaccineRequestId: string;
  notificationId: string;
}

export interface NotificationTypeModelMap {
  [MoeGoNotification.APPT_CREATED]: NotificationApptAssignedModel;
  [MoeGoNotification.APPT_ASSIGNED]: NotificationApptAssignedModel;
  [MoeGoNotification.APPT_RESCHEDULED]: NotificationApptRescheduledModel;
  [MoeGoNotification.APPT_CANCELLED]: NotificationApptCancelledModel;
  [MoeGoNotification.APPT_CANCELLED_BY_CLIENT]: NotificationApptCancelledByClientModel;
  [MoeGoNotification.APPT_CONFIRMED_BY_CLIENT]: NotificationApptConfirmedByClientModel;
  [MoeGoNotification.CLIENT_UPDATE_APPOINTMENT]: NotificationAppointmentUpdatedByClientModel;
  [MoeGoNotification.CLIENT_UPDATE_PET]: NotificationOBUpdatePetClientModel;
  [MoeGoNotification.CLIENT_UPDATE_CUSTOMER]: NotificationOBUpdatePetClientModel;
  [MoeGoNotification.OB_REQUEST]: NotificationOBRequestModel;
  [MoeGoNotification.OB_REQUEST_RESCHEDULE]: NotificationOBRequestModel;
  [MoeGoNotification.OB_REQUEST_CANCEL]: NotificationOBRequestModel;
  [MoeGoNotification.OB_ABANDONED]: NotifiactionOBAbandoned;
  [MoeGoNotification.OB_RENEW_END_DATE]: NotificationOBRenewEndDateModel;
  [MoeGoNotification.NEW_INTAKE_FORM]: NotificationNewIntakeFormModel;
  [MoeGoNotification.INVOICE_PAID]: NotificationInvoicePaidModel;
  [MoeGoNotification.AGREEMENT_SIGNED]: NotificationAgreementSignedModel;
  [MoeGoNotification.REVIEW_SUBMITTED]: NotificationReviewSubmittedModel;
  [MoeGoNotification.SUB_PAYMENT_FAIL]: NotificationSubscriptionFailedModel;
  [MoeGoNotification.SUB_PAYMENT_SUCCESS]: NotificationSubscriptionSuccessModel;
  [MoeGoNotification.A2P_REQUIRED]: NotificationA2PRegisterModel;
  [MoeGoNotification.A2P_FAIL]: NotificationA2PRegisterModel;
  [MoeGoNotification.TWILIO_FINE]: TwilioFineModel;
  [MoeGoNotification.GOOGLE_RESERVE_GEO_MATCHED]: NotificationGoogleReserveModel;
  [MoeGoNotification.GOOGLE_RESERVE_GEO_UNMATCHED]: NotificationGoogleReserveModel;
  [MoeGoNotification.PLATFORM_MARKETING_CAMPAIGN]: NotificationPlatformMarketingCampaignModel;
  [MoeGoNotification.PAYMENT_DISPUTE]: PaymentDisputeNotificationModel;
  [MoeGoNotification.FINANCE_CAPITAL_OFFER_APPROVED]: NotificationFinanceCapitalOfferApprovedModel;
  [MoeGoNotification.FINANCE_CAPITAL_OFFER_CREATED]: NotificationFinanceCapitalOfferCreatedModel;
  [MoeGoNotification.ACCOUNTING_SET_UP_REMINDER]: NotificationAccountingSetupReminderModel;
  [MoeGoNotification.ASSIGNED_TASK]: TaskManagementModal;
  [MoeGoNotification.WAITLIST_REQUEST]: WaitlistRequestModel;
  [MoeGoNotification.WAITLIST_SLOT_AVAILABLE]: WaitlistSlotAvailableModel;
  [MoeGoNotification.MEMBERSHIP_PAYMENT_FAILED]: MembershipPaymentFailedModel;
  [MoeGoNotification.BUY_MEMBERSHIP_VIA_BRANDED_APP]: MembershipNewSubscriptionModel;
  [MoeGoNotification.PET_VACCINE_REQUEST]: VaccineUpdateModel;
  [MoeGoNotification.UNKNOWN]: never;
}

export type NotificationModel<T extends NotificationType = NotificationType> = Replace<
  OpenApiModels['GET/message/notification/list']['Res']['data']['notificationList'][0],
  { type: T; extra: NotificationTypeModelMap[T]; createTime: number; businessId?: string | number }
>;

export interface NotificationRecord<T extends NotificationType = NotificationType>
  extends RecordInstance<NotificationModel<T>> {}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export class NotificationRecord<T extends NotificationType = NotificationType> extends PureRecord<NotificationModel>({
  notificationId: 0,
  sendTime: 0,
  readTime: 0,
  type: MoeGoNotification.UNKNOWN,
  extra: ANY,
  createTime: 0,
  title: '',
  body: '',
  businessId: '',
  companyId: ID_ANONYMOUS,
}) {
  is<NT extends NotificationType>(type: NT): this is NotificationRecord<NT> {
    return this.type === (type as any);
  }
}

export const enum NotificationListTab {
  System = 'system',
  Activity = 'activity',
  PendingReview = 'pendingReview',
}

export class NotificationListFilterRecord extends Record({
  tab: NotificationListTab.Activity,
  locationId: '',
}) {}

export class NotificationUnreadCountRecord extends Record({
  system: 0,
  activity: 0,
  pendingReview: 0,
  businessId: ID_ANONYMOUS,
}) {
  total() {
    return this.system + this.activity + this.pendingReview;
  }
}

export class NotificationCompanyUnreadCountRecord extends Record({
  system: 0,
  activity: 0,
  pendingReview: 0,
  companyId: ID_ANONYMOUS,
}) {
  total() {
    return this.system + this.activity + this.pendingReview;
  }
}

export class NotificationCompanyLocationUnreadCountRecord extends Record({
  system: 0,
  activity: 0,
  pendingReview: 0,
  id: '',
}) {
  total() {
    return this.system + this.activity + this.pendingReview;
  }
  ownKey(companyId: string, locationId: string) {
    return `${companyId}-${locationId}`;
  }
}

export const notificationMapBox = createRecordMapBox('notifications', new NotificationRecord(), 'notificationId');

export const businessNotificationMapBox = createRecordMapBox(
  'notifications/business',
  new LoadMoreList(new NotificationListFilterRecord(), 0, 0, '', false),
  'owner',
);

export const companyNotificationMapBox = createRecordMapBox(
  'notifications/company',
  new LoadMoreList(new NotificationListFilterRecord(), 0, 0, '', false),
  'owner',
);

export const notificationBusinessUnreadCountMapBox = createRecordMapBox(
  'notifications/unreadCount',
  new NotificationUnreadCountRecord(),
  'businessId',
);

export const notificationCompanyUnreadCountMapBox = createRecordMapBox(
  'notifications/companyUnreadCount',
  new NotificationCompanyUnreadCountRecord(),
  'companyId',
);
