import { ServiceItemType } from '@moego/bff-openapi/clients/client.order';
import { Checkbox, CheckboxGroup, Form, Radio, RadioGroup, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import { useMount } from 'ahooks';
import { selectBDFeatureEnable } from '../../../../../../../store/company/company.selectors';
import { getAllCompanyFullServiceInfoList } from '../../../../../../../store/service/actions/public/service.actions';
import { ServicesSelector } from './ServicesSelector';
import { type DepositRuleVM } from '../../../../../../../store/payment/actions/private/deposit.actions';
import { type DepositRuleServiceFilter_ServicesByType } from '@moego/bff-openapi/clients/client.order';

export interface FieldServiceSelectProps {
  value?: DepositRuleVM['serviceType'] | null;
  onChange?: (value: DepositRuleVM['serviceType'] | null) => void;
}

export const FieldServiceSelect = React.forwardRef<HTMLDivElement, FieldServiceSelectProps>(
  ({ value = {}, onChange }: FieldServiceSelectProps, ref) => {
    const dispatch = useDispatch();
    const [enableBD] = useSelector(selectBDFeatureEnable);
    const lastSpecificValueRef = React.useRef<DepositRuleVM['serviceType'] | null>({});
    const errMsg = useFormState().errors?.serviceType;

    useMount(() => {
      dispatch(getAllCompanyFullServiceInfoList({ withAddon: false }));
    });

    const { serviceFilterType, applicableServiceList } = React.useMemo(() => {
      if (!value) {
        return {
          serviceFilterType: 'all',
          applicableServiceList: [],
        };
      }
      const applicableServiceList = Object.keys(value).map((key) => +key);
      return {
        serviceFilterType: 'specific',
        applicableServiceList,
      };
    }, [value]);

    // 第一级：all 还是 specific service type
    const onServiceFilterTypeChange = (type: string) => {
      // 临时保存值，万一用户反悔了不至于要重新选择
      if (type === 'all') {
        lastSpecificValueRef.current = value;
      }
      onChange?.(type === 'all' ? null : lastSpecificValueRef.current || {});
    };

    // 第二级：有哪些 service type
    const onApplicableServiceListChange = (list: number[]) => {
      const newValue = { ...value };
      const set = new Set<number>();
      for (const key of list) {
        set.add(key);
      }
      for (const keyStr in newValue) {
        if (!set.has(+keyStr)) {
          // 临时保存值，万一用户反悔了不至于要重新选择
          lastSpecificValueRef.current = value;
          delete newValue[+keyStr as ServiceItemType];
        }
      }
      for (const key of list) {
        if (!newValue[key as ServiceItemType]) {
          newValue[key as ServiceItemType] = lastSpecificValueRef.current?.[key as ServiceItemType] || null;
        }
      }
      onChange?.(newValue);
    };

    // 第三级：某 service type 下选哪些 services
    const onServicesChange = (type: ServiceItemType, services: DepositRuleServiceFilter_ServicesByType | null) => {
      const newValue = { ...value };
      newValue[type] = services;
      onChange?.(newValue);
    };

    const renderSpecificServicesSelector = () => {
      const { BOARDING, DAYCARE, GROOMING } = ServiceItemType;
      const services = [
        { type: BOARDING, label: 'Boarding services' },
        { type: DAYCARE, label: 'Daycare services' },
        { type: GROOMING, label: 'Grooming services' },
      ];
      if (!enableBD) {
        return (
          <ServicesSelector
            serviceType={GROOMING}
            value={value ? value[GROOMING] || null : null}
            onChange={onServicesChange}
          />
        );
      }
      return (
        <CheckboxGroup
          className="moe-ml-[28px]"
          aria-label="Applicable services list"
          value={applicableServiceList}
          onChange={onApplicableServiceListChange}
        >
          {services.map((service) => (
            <React.Fragment key={service.type}>
              <Checkbox value={service.type}>{service.label}</Checkbox>
              {applicableServiceList.includes(service.type) ? (
                <ServicesSelector
                  serviceType={service.type}
                  value={value ? value[service.type] || null : null}
                  onChange={onServicesChange}
                />
              ) : null}
            </React.Fragment>
          ))}
        </CheckboxGroup>
      );
    };

    return (
      <div ref={ref} className="moe-flex moe-flex-col moe-gap-y-[16px]">
        <RadioGroup<string>
          isRequired
          label="Select which services can be applied to"
          value={serviceFilterType}
          onChange={onServiceFilterTypeChange}
        >
          <Radio value="all" className="moe">
            All services (including new services)
          </Radio>
          <Radio value="specific">Specific services</Radio>
        </RadioGroup>
        {serviceFilterType === 'specific' ? renderSpecificServicesSelector() : null}
        {errMsg ? <Form.HelpText className="moe-ml-[28px]" errorMessage={errMsg.message} /> : null}
      </div>
    );
  },
);

export const FieldServiceSelectValidator = (param: DepositRuleVM['serviceType']) => {
  if (param === null) return true;
  if (Object.keys(param).length === 0) {
    return 'Please select at least one service type';
  }
  if (Object.values(param).every((item) => item?.serviceIds.length === 0)) {
    return 'Please select at least one service for each selected service type';
  }
  return true;
};
