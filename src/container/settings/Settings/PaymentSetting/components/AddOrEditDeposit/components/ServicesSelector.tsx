import { type ServiceItemType } from '@moego/bff-openapi/clients/client.order';
import { type DepositRuleServiceFilter_ServicesByType } from '@moego/bff-openapi/clients/client.order';
import { LegacySelect as Select, Switch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { ServiceType, UN_CATEGORY_ID } from '../../../../../../../store/service/category.boxes';
import {
  selectBusinessServiceCategories,
  selectServicesByCategoriesId,
} from '../../../../../../../store/service/category.selectors';
import { useMount } from 'ahooks';

export interface ServicesSelectorProps {
  serviceType: ServiceItemType;
  value: DepositRuleServiceFilter_ServicesByType | null;
  onChange: (type: ServiceItemType, value: DepositRuleServiceFilter_ServicesByType | null) => void;
}

export const ServicesSelector = memo(({ serviceType, value, onChange }: ServicesSelectorProps) => {
  const [currentAccountId] = useSelector(currentAccountIdBox);
  const [categories] = useSelector(
    selectBusinessServiceCategories(`${ServiceType.Service}-${serviceType}`, currentAccountId),
  );
  const categoryIdList = [UN_CATEGORY_ID].concat(categories.toJSON()); // uncategory 的放前面
  const [{ serviceGroupList: originServiceGroupList }] = useSelector(
    selectServicesByCategoriesId(ServiceType.Service, serviceType, categoryIdList),
  );
  const serviceGroupList = useMemo(() => {
    return originServiceGroupList.map((item) => {
      return {
        ...item,
        options: item.options.map((option) => {
          return {
            ...option,
            // 避免 select 组件里出现 description
            description: '',
          };
        }),
      };
    });
  }, [originServiceGroupList]);

  const isSelectedAll = !!value?.isAll;
  const allServicesIdList = useMemo(() => {
    const list: number[] = [];
    serviceGroupList.forEach((item) => item?.options.forEach(({ value }) => list.push(value)));
    return list;
  }, [serviceGroupList]);

  const selectOptions = useMemo(() => {
    if (isSelectedAll) {
      return serviceGroupList.map((service) => ({
        ...service,
        options: service.options.map((option) => ({
          ...option,
          isDisabled: true,
        })),
      }));
    }
    return serviceGroupList;
  }, [isSelectedAll, serviceGroupList]);

  const selectValue = useMemo(
    () => (isSelectedAll ? allServicesIdList : value?.serviceIds.map((item: string) => Number(item)) || []),
    [isSelectedAll, allServicesIdList, value],
  );

  const handleSelectChange = (value: number[] | null) => {
    if (value === null) {
      onChange(serviceType, null);
    } else if (value.length === allServicesIdList.length) {
      onChange(serviceType, {
        isAll: true,
        serviceIds: allServicesIdList.map((item) => String(item)),
        serviceType,
      });
    } else {
      onChange(serviceType, {
        isAll: false,
        serviceIds: value.map((item) => String(item)),
        serviceType,
      });
    }
  };

  const handleSwitchChange = (isAll: boolean) => {
    onChange(serviceType, {
      isAll,
      serviceIds: isAll ? allServicesIdList.map((item) => String(item)) : [],
      serviceType,
    });
  };

  const renderMultipleValues = () => {
    if (isSelectedAll) return <div>All selected</div>;
    if (selectValue.length) return <div>{selectValue.length} services</div>;
    return undefined;
  };

  useMount(() => {
    // 初始化如果当前服务类型没有选择的服务，则默认选择所有服务
    if (!value?.serviceIds?.length) {
      handleSwitchChange(true);
    }
  });

  return (
    <Select
      className="moe-ml-[28px]"
      isMultiple
      options={selectOptions}
      value={selectValue}
      onChange={handleSelectChange}
      multipleMode={isSelectedAll ? 'value' : 'count'}
      renderMultipleValues={renderMultipleValues}
      footer={
        <Switch isSelected={isSelectedAll} onChange={handleSwitchChange} className="moe-p-[6px]">
          All services (including new services)
        </Switch>
      }
    />
  );
});
