import { type schemas } from '@moego/bff-openapi/clients/client.order';
import { type z } from '@moego/bff-openapi';
import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { OrderModelOrderType, type OrderModelV1 } from '@moego/api-web/moego/models/order/v1/order_models';
import { Moe<PERSON><PERSON>, useSerialCallback } from '@moego/finance-utils';
import { AmountCalcType, calcAmountValue, RealmType, type TypeofAmountCalcType } from '@moego/finance-web-kit';
import { Spin } from '@moego/ui';
import { useDebounceFn, useSetState } from 'ahooks';
import { useDispatch, useSelector, useStore } from 'amos';
import { groupBy, noop } from 'lodash';
import React, { useEffect } from 'react';
import { ClientInfoSummaryCard } from '../../../../components/ClientPicker/ClientInfoSummaryCard';
import { BFFOrderClient } from '../../../../middleware/bff';
import { FinanceKit } from '../../../../service/finance-kit';
import { getListPetServices } from '../../../../store/PaymentFlow/cart.action';
import { type CartServiceRecord } from '../../../../store/PaymentFlow/cart.boxes';
import { selectCartList, selectSelectedCartDetailList } from '../../../../store/PaymentFlow/cart.selectors';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getTaxList } from '../../../../store/business/tax.actions';
import { taxMapBox } from '../../../../store/business/tax.boxes';
import { serviceChargeMapBox } from '../../../../store/service/service.boxes';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { productItemInfoMapBox } from '../../TakePaymentDrawer/SubpageRight/AddOrderItemSubpage/modules/AddProducts/store/orderProducts.boxes';
import { RightSideSubpage } from '../../hooks/useSubpageRouter';
import { buildOrderV2 } from '../CardMixinOrderDrawer.utils';
import {
  convertCartItemsToCreateOrderItems,
  convertFeesItemsToCreateOrderItems,
  convertProductToCreateOrderItem,
  defaultTax,
} from '../Cart/Cart.utils';
import { useCartAppt } from '../Cart/useCartAppt';
import { useInitCart } from '../Cart/useInitCart';
import { ColumnsBridgeEvent } from '../Columns/ColumnsBridge';
import { productSelectedBox } from '../SubpageRight/AddMoreItemSubpage/modules/AddProducts/store/orderProducts.boxes';
import { LayoutCard } from '../components/Layout/LayoutCard';
import { LayoutDivider } from '../components/Layout/LayoutDivider';
import { LayoutSubTotal } from '../components/Layout/LayoutSubTotal';
import { LayoutSubpage } from '../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../components/Layout/LayoutSubpageScrollBody';
import { useDrawerScopeContext } from '../context/DrawerScopeContext';
import { type OptimizedPreviewOrderParams, type PreviewOrderResult } from '../hooks/Preview.types';
import { useBridgeListener } from '../hooks/use-bridge-hooks';
import { useBroadcastChargeAmount } from '../hooks/useBroadcastChargeAmount';
import { usePreviewSalesOrder } from '../hooks/usePreviewSalesOrder';
import { DepositAmount } from './Deposit/DepositAmount';
import { ItemLineStyleScoped } from './ItemLineStyleScoped.styles';
import { getSubtotalMoney } from './OrderFlow/OrderDetail.utils';
import { LeftColumnFooter } from './components/LeftColumnFooter';
import { LeftColumnHeader } from './components/LeftColumnHeader';
import { PetServiceList } from './components/PetServiceList/PetServiceList';
import { ServiceChargeList } from './components/ServiceChargeInfo/ServiceChargeList';

export const usePreviewDepositOrderAmountByRules = () => {
  const store = useStore();
  const dispatch = useDispatch();

  const resolveItems = (appointmentId: string) => {
    const sourceItems = store.select(selectSelectedCartDetailList(appointmentId));
    const { surcharges } = store.select(selectCartList(appointmentId));
    const product = store.select(productSelectedBox);

    const services = convertCartItemsToCreateOrderItems(sourceItems);
    const servicesCharges = convertFeesItemsToCreateOrderItems(
      surcharges.map((s) => {
        const { taxId } = store.select(serviceChargeMapBox.mustGetItem(s.serviceChargeId));
        const tax = store.select(taxMapBox.mustGetItem(taxId));
        return {
          ...s,
          tax: isNormal(taxId)
            ? { id: String(tax.id), name: tax.taxName, rate: String(tax.taxRate) }
            : { ...defaultTax },
        };
      }),
    );
    const products = product.map((p) => {
      const productInfo = store.select(productItemInfoMapBox.mustGetItem(p.id));
      const tax = store.select(taxMapBox.mustGetItem(productInfo.taxId));
      const business = store.select(selectCurrentBusiness);
      return convertProductToCreateOrderItem(productInfo, {
        externalUuid: p.externalUuid,
        staffId: p.staffId || '0',
        petId: '0',
        quantity: p.quantity,
        currencyCode: business.currencyCode,
        tax: isNormal(productInfo.taxId)
          ? { id: String(tax.id), name: tax.taxName, rate: String(tax.taxRate) }
          : { ...defaultTax },
      });
    });

    const allItems = services.concat(servicesCharges).concat(products);

    return {
      sourceItems,
      allItems,
    };
  };

  const previewDepositOrderAmountByRules = useLatestCallback(async (sourceId: string) => {
    await Promise.all([dispatch(getListPetServices({ id: sourceId })), dispatch(getTaxList())]);
    const { sourceItems } = resolveItems(sourceId);
    const petServicesMap = groupBy(sourceItems, 'pet.id');
    const petIds = Object.keys(petServicesMap);

    const orderDetail = await BFFOrderClient.previewDepositOrderByRules({
      sourceId: sourceId,
      sourceType: OrderSourceType.APPOINTMENT,
      petServices: petIds.map((petId) => ({
        petId,
        services: petServicesMap[petId].map((s) => ({
          serviceId: s.serviceId,
          unitPrice: s.unitPrice,
          quantity: s.quantity,
          totalPrice: s.totalPrice,
          staffId: s.staffId,
          associatedServiceId: s.associatedServiceId,
          serviceItemType: s.serviceItemType,
          serviceType: s.serviceType,
        })) as unknown as z.infer<
          typeof schemas.postMoego_bfforderpreviewDepositOrderByRules_Body
        >['petServices'][number]['services'],
      })),
    });

    const remainAmount = orderDetail.orderDetail?.order.remainAmount ?? undefined;

    return remainAmount ? MoeMoney.fromMoney(remainAmount).valueOf() : undefined;
  });

  return previewDepositOrderAmountByRules;
};

const useData = ({
  previewOrder,
  inPreview,
}: {
  previewOrder: (params: Partial<OptimizedPreviewOrderParams>) => Promise<PreviewOrderResult>;
  inPreview: boolean;
}) => {
  const [business, staff] = useSelector(selectCurrentBusiness, selectCurrentStaff());
  const { columnsBridge, sourceType, customer, rightColumnPageRouter, sourceId } = useDrawerScopeContext();

  const [amounts, setAmounts] = useSetState<{
    chargeAmountWithConvenienceFee: number;
    preServiceSubtotal: number;
    serviceSubTotal: number;
    depositType: TypeofAmountCalcType;
    depositValue?: string;
    depositAmount: number;
    cvFee: number;
    description: string;
    rulesAmount?: number;
  }>({
    chargeAmountWithConvenienceFee: 0,
    preServiceSubtotal: 0,
    serviceSubTotal: 0,
    depositType: AmountCalcType.AMOUNT as TypeofAmountCalcType,
    depositValue: undefined,
    depositAmount: 0,
    cvFee: 0,
    description: '',
  });

  const previewDepositOrderAmountByRules = usePreviewDepositOrderAmountByRules();

  const { broadcastChargeAmount } = useBroadcastChargeAmount({
    columnsBridge,
  });

  const changeAmount = async (amount: number) => {
    // calculate cv fee base on amount
    broadcastChargeAmount(MoeMoney.fromAmount(amount));
  };

  const changeAmountDebounced = useDebounceFn(changeAmount, { wait: 1000 });
  const { initCart } = useCartAppt(sourceId);
  useBridgeListener(columnsBridge, ColumnsBridgeEvent.OnConvenienceFeeChange, (cvFee) => {
    setAmounts(({ depositAmount, ...rest }) => ({
      ...rest,
      chargeAmountWithConvenienceFee: MoeMoney.fromAmount(depositAmount, business.currencyCode)
        .plus(MoeMoney.fromAmount(cvFee))
        .valueOf(),
      cvFee,
    }));
  });

  const handleChangeDepositValue = useSerialCallback(
    async (_value: {
      type: TypeofAmountCalcType;
      value?: string;
    }) => {
      const { type, value } = _value;

      rightColumnPageRouter.go(RightSideSubpage.DEFAULT);

      const amount = Number(
        calcAmountValue({
          type,
          value: value ?? '0',
          baseAmount: amounts.serviceSubTotal,
        }).toFixed(2),
      );

      const description =
        type === AmountCalcType.PERCENTAGE
          ? `By percentage, ${business.formatAmount(amounts.serviceSubTotal)} * ${value}%`
          : type === AmountCalcType.AMOUNT
            ? `By amount`
            : `By rule`;

      setAmounts({
        depositType: type,
        depositValue: value,
        depositAmount: amount,
        chargeAmountWithConvenienceFee: MoeMoney.fromAmount(amount).plus(MoeMoney.fromAmount(amounts.cvFee)).valueOf(),
        description,
      });

      columnsBridge.runParallel(
        ColumnsBridgeEvent.OnPreviewOrderReady,
        FinanceKit.buildModel(RealmType.OrderV2, {
          orderType: OrderModelOrderType.DEPOSIT,
          customerId: customer.customerId.toString(),
        }),
        null,
      );

      changeAmountDebounced.run(amount);
    },
  );

  const handlePreview = useLatestCallback(
    async (params: Partial<OptimizedPreviewOrderParams>, _originItems: CartServiceRecord[]) => {
      const [previewResult, rulesAmount] = await Promise.all([
        previewOrder(params),
        previewDepositOrderAmountByRules(sourceId),
      ]);

      const subtotal = getSubtotalMoney(previewResult);
      const serviceSubtotal = subtotal.discountedServiceSubtotal.valueOf();
      setAmounts({
        serviceSubTotal: serviceSubtotal,
        preServiceSubtotal: subtotal.preServiceSubtotal.valueOf(),
        rulesAmount: rulesAmount ? Math.min(rulesAmount, serviceSubtotal) : undefined,
      });
      return previewResult;
    },
  );

  const createOrder = useLatestCallback(async () => {
    const result = await BFFOrderClient.createDepositOrder({
      sourceId,
      // TODO(Sam): update types in BFF later
      sourceType: sourceType as unknown as any,
      staffId: staff.id.toString(),
      customerId: customer.customerId.toString(),
      depositAmount: MoeMoney.fromAmount(amounts.depositAmount),
      businessId: business.id.toString(),
      description: amounts.description,
    });

    const order = result.order as unknown as OrderModelV1;
    return buildOrderV2(order);
  });

  useInitCart({
    sourceId,
    initCart,
    autoApplyWhenCartChanges: false,
    preview: async (params, originItems) => {
      return await handlePreview(params, originItems);
    },
    createOrder,
    columnsBridge,
  });

  useEffect(() => {
    if (!inPreview) {
      broadcastChargeAmount(MoeMoney.fromAmount(amounts.rulesAmount ?? 0));
      if (amounts.rulesAmount) {
        handleChangeDepositValue({
          type: AmountCalcType.RULES,
          value: `${amounts.rulesAmount}`,
        });
      }
    }
  }, [inPreview, amounts.rulesAmount]);

  return {
    isLoading: inPreview,
    amounts,
    handleChangeDepositValue,
    isLoadingCVFee: handleChangeDepositValue.isBusy(),
  };
};

export const DepositSubpage = () => {
  const { sourceId, customer } = useDrawerScopeContext();
  const { previewOrder, inPreview, previewedResult } = usePreviewSalesOrder({
    afterPreview: noop,
  });
  const [business] = useSelector(selectCurrentBusiness);
  const { amounts, handleChangeDepositValue, isLoading } = useData({
    previewOrder,
    inPreview,
  });

  if (isLoading) {
    return (
      <div className="moe-flex moe-h-full moe-items-center moe-justify-center">
        <Spin />
      </div>
    );
  }

  return (
    <LayoutSubpage>
      <LeftColumnHeader title={'Take deposit'} />
      <LayoutSubpageScrollBody className="moe-pb-l">
        <ClientInfoSummaryCard
          client={customer}
          displayClientTags={false}
          className="moe-border moe-border-divider"
          style={{ boxShadow: 'none' }}
        />
        <LayoutCard className={'moe-mt-s moe-bg-neutral-sunken-0 moe-p-s moe-rounded-m'}>
          {/* 太骚了 */}
          <ItemLineStyleScoped />
          <PetServiceList
            className="moe-mt-0 mark-item-left-line-gray"
            previewedResult={previewedResult}
            appointmentId={sourceId}
            readonly={true}
          />
          <ServiceChargeList
            readonly
            previewedResult={previewedResult}
            appointmentId={sourceId}
            className="moe-mt-8px-150 mark-item-left-line-gray"
          />
          <LayoutDivider className="moe-my-s" />
          <LayoutSubTotal
            className="moe-mt-xs"
            title="Service subtotal"
            subtotal={business.formatAmount(amounts.serviceSubTotal)}
            originalSubtotal={business.formatAmount(amounts.preServiceSubtotal)}
          />
        </LayoutCard>
        <DepositAmount
          className={'moe-mt-xl'}
          subTotalAmount={amounts.serviceSubTotal}
          rulesAmount={amounts.rulesAmount}
          onChange={handleChangeDepositValue}
          value={{
            type: amounts.depositType,
            value: amounts.depositValue,
          }}
        />
      </LayoutSubpageScrollBody>
      <LeftColumnFooter
        amountPaid={0}
        amountToPay={amounts.chargeAmountWithConvenienceFee}
        amountToPayTitle={'Deposit amount due'}
      />
    </LayoutSubpage>
  );
};
