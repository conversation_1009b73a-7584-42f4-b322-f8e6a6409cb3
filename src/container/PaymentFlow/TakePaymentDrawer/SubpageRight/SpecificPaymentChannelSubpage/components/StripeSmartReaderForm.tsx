import { PaymentChannelType, PrefabPaymentChannel, RealmType } from '@moego/finance-web-kit';
import { Button, Empty, Spin, toast } from '@moego/ui';
import { type ErrorResponse, type Terminal } from '@stripe/terminal-js';
import { useDispatch, useSelector } from 'amos';
import { T_SECOND, noop } from 'monofile-utilities/lib/consts';
import React, { type MutableRefObject, memo, useEffect } from 'react';
import { useHistory } from 'react-router';
import { useAsync, useMount, useSetState, useUnmount } from 'react-use';
import type Stripe from 'stripe';
import { PATH_CREDIT_CARD_SETTING } from '../../../../../../router/paths';
import { FinanceKit } from '../../../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import {
  getStripeSmartReaderList,
  getStripeTerminalLocationList,
} from '../../../../../../store/stripe/stripeTerminal.actions';
import {
  currentStripeTerminalLocationIdBox,
  isStripeSmartReaderServerDrivenAvailableCountry,
  stripeSmartReaderMapBox,
  stripeSmartReaderSelectedBox,
} from '../../../../../../store/stripe/stripeTerminal.boxes';
import { selectBusinessStripeSmartReaderList } from '../../../../../../store/stripe/stripeTerminal.selectors';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { READER_USED_IN_PARALLEL_ERROR, useStripeTerminal } from '../../../../../Payment/components/useStripeTerminal';
import { useOrderContext } from '../../../../hooks/useOrderContext';
import { PaymentDrawerCloseType } from '../../../../interface';
import { StripeReaderItem } from './StripeReaderItem';

const LAST_CONNECTED_READER_KEY = 'last_connected_reader_key';

export type StripeReader = Stripe.Terminal.Reader;

/**
 * 收归组件的异步数据初始化逻辑
 */
const useSmartReaderInit = ({
  locationId,
  terminal,
  discoverReaders,
  useServerDriven,
  callback,
}: {
  locationId: string;
  terminal: MutableRefObject<Terminal | undefined>;
  discoverReaders: () => void;
  useServerDriven: boolean;
  callback: () => void;
}) => {
  const dispatch = useDispatch();
  const [smartReaderList] = useSelector(selectBusinessStripeSmartReaderList());

  const checkConnectionStatus = useLatestCallback(async () => {
    const status = terminal.current?.getConnectionStatus();
    return status;
  });

  const getSmartReaderList = useSerialCallback(async () => {
    await dispatch(getStripeSmartReaderList(locationId));
  });

  useEffect(() => {
    if (locationId) {
      getSmartReaderList();
    }
  }, [locationId]);

  useAsync(async () => {
    if (locationId && smartReaderList.count() && useServerDriven) {
      callback();
    }

    if (locationId && smartReaderList.count() && !useServerDriven && terminal.current) {
      const status = await checkConnectionStatus();
      if (status !== 'connected') {
        dispatch(stripeSmartReaderSelectedBox.setState(null));
        await discoverReaders();
      }
      callback();
    }
  }, [locationId, terminal.current, smartReaderList.count(), useServerDriven]);

  return {
    loading: getSmartReaderList.isBusy(),
  };
};

export const StripeSmartReaderForm = memo(() => {
  const { pay, closeDrawer, paymentChannels } = useOrderContext();
  const [state, setState] = useSetState({
    discoveredReaders: [] as StripeReader[],
    connectingReaderSN: '' as string,
  });
  const dispatch = useDispatch();
  const history = useHistory();
  const [smartReaderList, smartReaderMap, smartReaderSelected, business, locationId] = useSelector(
    selectBusinessStripeSmartReaderList(),
    stripeSmartReaderMapBox,
    stripeSmartReaderSelectedBox,
    selectCurrentBusiness(),
    currentStripeTerminalLocationIdBox,
  );

  const { loading: locationLoading } = useAsync(async () => {
    await dispatch(getStripeTerminalLocationList());
  }, []);

  const terminal = useStripeTerminal({
    onUnexpectedReaderDisconnect: (event) => {
      dispatch(stripeSmartReaderSelectedBox.setState(null));
      // READER_USED_IN_PARALLEL_ERROR should be handled in stripe terminal's API calls
      if (event.error?.message !== READER_USED_IN_PARALLEL_ERROR) {
        toast({
          title: event.error?.message || 'Reader disconnected unexpectedly.',
          type: 'error',
        });
      }
    },
  });

  const useServerDriven = isStripeSmartReaderServerDrivenAvailableCountry(business.country);

  const attachSmartReaderChannel = useLatestCallback((readerId: string) => {
    pay.set('stripeSmartReaderId', readerId);
    paymentChannels.attachPaymentChannel(
      PrefabPaymentChannel.StripeTerminal,
      FinanceKit.buildModel(RealmType.PaymentChannel, {
        type: PaymentChannelType.Prefab,
        id: PrefabPaymentChannel.StripeTerminal,
        methodId: PrefabPaymentChannel.StripeTerminal,
      }),
      {
        stripeSmartReaderId: readerId,
        readerLocationId: locationId,
      },
    );
  });

  const connectReader = useLatestCallback(async (readerId: string) => {
    const reader = smartReaderMap.mustGetItem(readerId);
    if (!reader) return;

    pay.attachStripeTerminal(terminal);
    if (useServerDriven) {
      dispatch(stripeSmartReaderSelectedBox.setState(reader));
      localStorage.setItem(LAST_CONNECTED_READER_KEY, reader.serialNumber);
      pay.attachStripeSmartReader(reader);
      attachSmartReaderChannel(readerId);
    } else {
      setState({ connectingReaderSN: reader.serialNumber });
      await terminal.current?.disconnectReader();
      const discoverReader = state.discoveredReaders.find((r) => r.id === readerId)!;
      const connectInstance = terminal.current?.connectReader(discoverReader, { fail_if_in_use: false });

      await Promise.race([
        connectInstance,
        new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              error: {
                message: 'Connection timed out error',
              },
            });
          }, 30 * T_SECOND);
        }),
      ])
        .then((connectResult) => {
          const errResult = connectResult as Partial<ErrorResponse>;
          if (errResult.error) {
            toast({
              title: errResult.error.message,
              type: 'error',
            });
          } else {
            dispatch(stripeSmartReaderSelectedBox.setState(reader));
            localStorage.setItem(LAST_CONNECTED_READER_KEY, reader.serialNumber);
            attachSmartReaderChannel(readerId);
          }
        })
        .catch((err) => {
          toast({
            title: err?.message || 'Network error',
            type: 'error',
          });
        })
        .finally(() => {
          setState({ connectingReaderSN: '' });
        });
    }
  });

  const discoverReaders = useLatestCallback(async () => {
    if (!terminal.current) return;
    await terminal.current
      ?.discoverReaders({
        simulated: false,
        location: locationId,
      })
      .then((result) => {
        if ('error' in result) {
          toast({
            title: result.error.message || 'Failed to discover readers',
            type: 'error',
          });
        } else if (result.discoveredReaders?.length === 0) {
          setState({ discoveredReaders: [] });
        } else {
          setState({ discoveredReaders: result.discoveredReaders });
        }
      });
  });

  const onSelectReader = useLatestCallback((readerId: string) => {
    const reader = smartReaderMap.mustGetItem(readerId);

    if (useServerDriven) {
      connectReader(readerId);
    } else {
      const haveReader = !!state.discoveredReaders.find((r) => r.serial_number === reader.serialNumber);
      haveReader && connectReader(readerId);
    }
  });

  const onSwitchAnotherReader = useLatestCallback(async () => {
    if (useServerDriven) {
      pay.attachStripeSmartReader(null);
    } else {
      await terminal.current?.disconnectReader();
      discoverReaders();
    }
    paymentChannels.detachPaymentChannel(PrefabPaymentChannel.StripeTerminal);
    dispatch(stripeSmartReaderSelectedBox.setState(null));
  });

  const autoConnectReader = useLatestCallback(() => {
    const prevSelectReaderSN = localStorage.getItem(LAST_CONNECTED_READER_KEY);
    let selectedReaderId = '';

    if (useServerDriven) {
      const readerList = smartReaderList.map((id) => smartReaderMap.mustGetItem(id));
      const reader = readerList.find((r) => r.serialNumber === prevSelectReaderSN);
      selectedReaderId = reader?.id || smartReaderList.get(0)!;
    } else {
      const discoveredReaders = state.discoveredReaders;
      const reader = discoveredReaders.find((r) => r.serial_number === prevSelectReaderSN);
      selectedReaderId = reader?.id || discoveredReaders[0].id;
    }

    selectedReaderId && onSelectReader(selectedReaderId);
  });

  const { loading: readerLoading } = useSmartReaderInit({
    locationId,
    terminal,
    discoverReaders,
    useServerDriven,
    callback: autoConnectReader,
  });

  useMount(() => {
    dispatch(getStripeTerminalLocationList());
  });

  useUnmount(() => {
    dispatch(stripeSmartReaderSelectedBox.setState(null));
    if (useServerDriven) {
      pay.attachStripeSmartReader(null);
    } else {
      terminal.current?.disconnectReader();
    }

    paymentChannels.detachPaymentChannel(PrefabPaymentChannel.StripeTerminal);
  });

  return (
    <Spin
      classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
      isLoading={readerLoading || locationLoading}
    >
      {smartReaderList.isEmpty() && (
        <section className="moe-flex moe-flex-col">
          <Empty
            classNames={{
              title: 'moe-text-xl',
            }}
            title="No available smart reader"
            description="Please complete set up first"
          />
          <Button
            className="moe-mt-[46px]"
            onPress={() => {
              history.push(PATH_CREDIT_CARD_SETTING.queried({ stripeModal: 'SMART_READER_MANAGEMENT' }));
              closeDrawer(PaymentDrawerCloseType.Cancel);
            }}
            variant="tertiary"
          >
            Go to smart reader management
          </Button>
        </section>
      )}

      {smartReaderList.count() > 0 && (
        <>
          <section className="moe-flex moe-flex-col moe-gap-s">
            {smartReaderList.map((id) => {
              const reader = smartReaderMap.mustGetItem(id);
              const serialNumber = reader.serialNumber;
              const disabled = !!state.connectingReaderSN && state.connectingReaderSN !== serialNumber;

              return (
                <StripeReaderItem
                  key={id}
                  reader={reader}
                  connecting={serialNumber === state.connectingReaderSN}
                  disabled={disabled}
                  onSelect={state.connectingReaderSN ? noop : onSelectReader}
                  selected={smartReaderSelected?.id === id}
                />
              );
            })}
          </section>
          <Button className="moe-mt-s moe-py-[10px]" variant="tertiary" onPress={onSwitchAnotherReader}>
            Change to another reader
          </Button>
        </>
      )}
    </Spin>
  );
});
