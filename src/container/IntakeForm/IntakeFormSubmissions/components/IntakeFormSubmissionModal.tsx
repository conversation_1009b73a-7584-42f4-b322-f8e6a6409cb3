/*
 * @since 2021-01-04 18:10:33
 * <AUTHOR> <<EMAIL>>
 */

import { useDispatch, useSelector } from 'amos';
import { Popconfirm } from 'antd';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { Fragment, memo, useEffect, useState, type ReactNode } from 'react';
import IconAlertTrianglePng from '../../../../assets/icon/icon-alert-triangle.png';
import IconIconCloseSvg from '../../../../assets/icon/icon-close.svg';
import IconIconSelectedGreenSvg from '../../../../assets/icon/icon-selected-green.svg';
import SvgIconClockSvg from '../../../../assets/svg/icon-clock.svg';
import SvgIconMessageSvg from '../../../../assets/svg/icon-message.svg';
import SvgIconQuestionSvg from '../../../../assets/svg/icon-question.svg';
import { Avatar } from '../../../../components/Avatar/Avatar';
import { Button } from '../../../../components/Button/Button';
import { WithPermission } from '../../../../components/GuardRoute/WithPermission';
import { ImgIcon, SvgIcon } from '../../../../components/Icon/Icon';
import { WithPricingEnableUpgrade } from '../../../../components/Pricing/WithPricingComponents';
import { toastApi } from '../../../../components/Toast/Toast';
import { PATH_CUSTOMER_OVERVIEW, PATH_LEADS_DETAIL } from '../../../../router/paths';
import { currentAccountIdBox } from '../../../../store/account/account.boxes';
import { getAgreementList } from '../../../../store/agreement/agreement.actions';
import { agreementMapBox } from '../../../../store/agreement/agreement.boxes';
import { selectBusinessAgreements } from '../../../../store/agreement/agreement.selectors';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { CustomerTypeEnum, customerMapBox, printFullName } from '../../../../store/customer/customer.boxes';
import { printAddress } from '../../../../store/customer/customerAddress.selectors';
import {
  acceptIntakeFormSubmission,
  getIntakeFormSubmission,
  removeIntakeFormSubmission,
} from '../../../../store/intakeForm/intakeForm.actions';
import {
  IntakeFormQuestionCategory,
  IntakeFormQuestionType,
  type IntakeFormQuestionSnapshotModel,
} from '../../../../store/intakeForm/intakeForm.boxes';
import {
  selectIntakeFormSubmission,
  selectIntakeFormSubmissionForm,
} from '../../../../store/intakeForm/intakeForm.selectors';
import { PetGender } from '../../../../store/pet/pet.boxes';
import { getPetTypeList } from '../../../../store/pet/petType.actions';
import { PetTypeRecord, petTypeMapBox } from '../../../../store/pet/petType.boxes';
import { selectBusinessPetTypes } from '../../../../store/pet/petType.selectors';
import { getPetVaccineList } from '../../../../store/pet/petVaccine.actions';
import { petVaccineMapBox } from '../../../../store/pet/petVaccine.boxes';
import { selectBusinessPetVaccines } from '../../../../store/pet/petVaccine.selectors';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { createNewWaitList } from '../../../../store/waitList/actions/public/waitList.actions';
import { c_primary } from '../../../../style/_variables';
import { getPetAvatarType, useRedirectMessage } from '../../../../utils/BusinessUtil';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { isImage, jsonParse } from '../../../../utils/utils';
import { type CreateWaitListState } from '../../../CreateWaitList/CreateWaitList.props';
import { useCreateWaitList } from '../../../CreateWaitList/hooks/useCreateWaitList';
import { ExpectDateType } from '../../../CreateWaitList/types/types';
import { waitlistFormValueTransformer } from '../../../CreateWaitList/utils/utils';
import { useGetCustomerWording } from '../../../Leads/hooks/useGetCustomerWording';
import { ClientLocation, isValidAddress } from '../../../OnlineBooking/components/ClientLocation/ClientLocation';
import { IntakeFormSubmissionMergeModal } from './IntakeFormSubmissionMergeModal';
import { IntakeFormSubmissionImgPreviewModal, IntakeFormSubmissionModalView } from './IntakeFormSubmissionModal.style';
import { OBClientQuestionKey } from '../../../../store/onlineBooking/models/OBQuestion.base';
export interface IntakeFormSubmissionModalProps {
  className?: string;
  id: number | undefined;
  onClose: (updated: boolean) => void;
}

export const IntakeFormSubmissionModal = memo<IntakeFormSubmissionModalProps>(({ className, id, onClose }) => {
  const [
    submission,
    form,
    business,
    petTypeMap,
    petTypeList,
    vaccineMap,
    petVaccineList,
    agreementMap,
    agreementList,
    currentAccountId,
    permissions,
  ] = useSelector(
    selectIntakeFormSubmission(id ?? ID_ANONYMOUS),
    selectIntakeFormSubmissionForm(id ?? ID_ANONYMOUS),
    selectCurrentBusiness,
    petTypeMapBox,
    selectBusinessPetTypes(),
    petVaccineMapBox,
    selectBusinessPetVaccines(),
    agreementMapBox,
    selectBusinessAgreements(),
    currentAccountIdBox,
    selectCurrentPermissions(),
  );
  const dispatch = useDispatch();
  const canViewClientProfile = permissions.has('viewIndividualClientProfile');
  const [merging, setMerging] = useState(id);
  const alertMsgFlag = useBool();
  const addToWaitListSuccessFlag = useBool();
  const [avatarModal, setAvatarModal] = useState({
    visible: false,
    avatarPath: '',
    petTypeId: 0,
  });
  const isExistCustomer = submission.isCreate > 0 && isNormal(submission.existingCustomerId);
  const [customer] = useSelector(customerMapBox.mustGetItem(submission.existingCustomerId ?? ID_ANONYMOUS));
  const isLead = customer?.type === CustomerTypeEnum.LEAD;
  const handleToMessage = useRedirectMessage(submission.existingCustomerId || ID_ANONYMOUS);
  const contactNum = business.formatPhoneNumber(submission.customer.phoneNumber);
  const openCreateWaitList = useCreateWaitList();
  const getCustomerWording = useGetCustomerWording();
  useEffect(() => {
    //set back to default state
    addToWaitListSuccessFlag.close();
    isNormal(id) && dispatch(getIntakeFormSubmission(id, true));
  }, [id, addToWaitListSuccessFlag, dispatch]);

  useEffect(() => {
    !petTypeList.size && dispatch(getPetTypeList());
  }, [petTypeList.size, dispatch]);

  useEffect(() => {
    !petVaccineList.size && dispatch(getPetVaccineList());
  }, [petVaccineList.size, dispatch]);

  useEffect(() => {
    !agreementList.size && dispatch(getAgreementList());
  }, [agreementList.size, dispatch]);

  let key = 0;
  const renderMeta = (label: ReactNode, value: ReactNode, _required: boolean, className = '') => (
    <div className={'meta ' + className} key={key++}>
      <div className="label">{label}</div>
      <div className="value">{value}</div>
    </div>
  );

  const renderName = () => {
    const { customer } = submission;
    const fullName = `${customer.firstName ?? ''}\u{20}${customer.lastName ?? ''}`;
    return (
      <div className="meta">
        <div className="label">{getCustomerWording({ capitalize: true })} name</div>
        <div className="value">
          {isExistCustomer ? (
            <a
              className={canViewClientProfile ? '' : 'moe-pointer-events-none'}
              href={
                !isLead
                  ? PATH_CUSTOMER_OVERVIEW.build({ customerId: submission.existingCustomerId! })
                  : PATH_LEADS_DETAIL.build({ id: submission.existingCustomerId!.toString() })
              }
              target="_blank"
              rel="noreferrer"
            >
              {fullName}
            </a>
          ) : (
            fullName
          )}
        </div>
      </div>
    );
  };

  function renderCustomMeta(q: IntakeFormQuestionSnapshotModel, answer: string | undefined) {
    if (q.questionType === IntakeFormQuestionType.Checkbox) {
      return renderMeta(
        q.question,
        jsonParse(answer, []).map((v, i) => <div key={i}>{v}</div>),
        q.isRequired > 0,
        'custom',
      );
    }
    return renderMeta(q.question, answer, q.isRequired > 0, 'custom');
  }

  function renderVaccineDocView(vaccineDocument?: string) {
    if (!vaccineDocument) return null;
    return isImage(vaccineDocument) ? (
      <span
        onClick={() => setAvatarModal({ visible: true, avatarPath: vaccineDocument, petTypeId: 0 })}
        className="vaccine-doc-view-button"
      >
        View
      </span>
    ) : (
      <a className="vaccine-doc-view-button" href={vaccineDocument} target="_blank" rel="noreferrer">
        View
      </a>
    );
  }

  const handleAdd = useSerialCallback(async () => {
    if (!submission.conflictCustomerId) {
      if (!id) return;
      await dispatch(acceptIntakeFormSubmission(id, false));
      await dispatch(getIntakeFormSubmission(id, true));
      toastApi.success('The profile is added successfully!');
      alertMsgFlag.close();
    } else {
      setMerging(id);
    }
  });

  const handleAddToWaitingList = useSerialCallback(async () => {
    const { existingCustomerId } = submission;
    if (!isNormal(existingCustomerId)) return;

    const onSave = async (result: CreateWaitListState) => {
      const { allPetsStartAtSameTime, petAndServices, clientId, form } = result;
      const extra = waitlistFormValueTransformer(form);
      await dispatch(
        createNewWaitList({
          customerId: clientId,
          allPetsStartAtSameTime,
          petServices: petAndServices,
          ...extra,
        }),
      );
    };

    openCreateWaitList({
      mask: true,
      clientId: existingCustomerId,
      form: {
        date: { type: ExpectDateType.Any },
      },
      onSave,
    });

    onClose(false);
  });
  const onMergeClose = async () => {
    if (!id) return;
    await dispatch(getIntakeFormSubmission(id, true));
    setMerging(void 0);
    alertMsgFlag.close();
  };

  return (
    <IntakeFormSubmissionModalView
      className={className}
      title={form.title}
      visible={id !== void 0}
      onClose={useLatestCallback(() => {
        alertMsgFlag.close();
        onClose(false);
      })}
      width="500px"
      height="calc(100% - 248px)"
    >
      <IntakeFormSubmissionMergeModal id={merging} onClose={onMergeClose} />

      <IntakeFormSubmissionImgPreviewModal
        visible={avatarModal.visible}
        onClose={() => setAvatarModal({ ...avatarModal, ['visible']: false })}
        width="500px"
      >
        <Avatar
          src={avatarModal.avatarPath}
          type={getPetAvatarType(avatarModal.petTypeId)}
          className="modal-avatar modal-preview-img"
          size="400px"
        />
      </IntakeFormSubmissionImgPreviewModal>
      <div className="content rr-mask">
        {renderMeta(
          <>
            <SvgIcon src={SvgIconClockSvg} size={16} className="mr-6 mt--4" />
            Submitted at
          </>,
          business.formatDateTime((submission.createTime as number) * T_SECOND),
          false,
          'gray',
        )}
        <div className="block">{getCustomerWording({ capitalize: true })} information</div>
        {renderName()}
        {renderMeta(
          'Contact',
          isExistCustomer ? (
            <WithPermission permissions={'viewMessageCenter'}>
              <div className="rr-mask">
                {contactNum}
                <SvgIcon
                  size={18}
                  src={SvgIconMessageSvg}
                  color={c_primary}
                  style={{ marginLeft: '8px', marginTop: -3 }}
                  onClick={handleToMessage}
                />
              </div>
            </WithPermission>
          ) : (
            contactNum
          ),
          true,
          '',
        )}
        {renderMeta('Email', submission.customer.email, true, '')}
        {submission.isCardShow
          ? renderMeta(
              'Credit card',
              submission.newCard
                ? `**** **** **** ${submission.newCard.card?.last4} (${submission.newCard.card?.brand})`
                : submission.squareCard
                  ? `**** **** **** ${submission.squareCard.last4} (${submission.squareCard.cardBrand})`
                  : '',
              !!submission.isCardRequired,
            )
          : ''}
        {submission.questionList.map((q) => {
          if (!q.isShow || q.type !== IntakeFormQuestionCategory.ForOwner) {
            return null;
          }
          switch (q.key) {
            case 'First_name':
            case 'Last_name':
            case 'Phone_number':
            case 'Email':
              return null;
            case 'Address':
              return (
                <>
                  {renderMeta('Address', printAddress(submission.customer), q.isRequired > 0)}
                  {business.isMobileGrooming() && isValidAddress(submission.customer) && (
                    <ClientLocation address={submission.customer} isOB={false} />
                  )}
                </>
              );
            case 'Birthday':
              return renderMeta('Birthday', business.formatDate(submission.customer.birthday), q.isRequired > 0);
            case OBClientQuestionKey.EmergencyContact:
              return renderMeta(
                'Emergency contact',
                [
                  printFullName(
                    submission.customer.emergencyContactFirstName || '',
                    submission.customer.emergencyContactLastName || '',
                  ),
                  business.formatPhoneNumber(submission.customer.emergencyContactPhone),
                ].join(' '),
                q.isRequired > 0,
              );
            case OBClientQuestionKey.PeopleAuthorizeToPickupPets: {
              const { pickupContactFirstName, pickupContactLastName, pickupContactPhone } = submission.customer;
              return renderMeta(
                'People authorized to pick up pet',
                [
                  printFullName(pickupContactFirstName || '', pickupContactLastName || ''),
                  business.formatPhoneNumber(pickupContactPhone),
                ].join(' '),
                q.isRequired > 0,
              );
            }
            default: {
              const answer = submission.customer.answersMap?.[q.key];
              return renderCustomMeta(q, answer);
            }
          }
        })}
        {submission.signatureList.map((s) => {
          return renderMeta(
            agreementMap.mustGetItem(s.agreementId).agreementHeader,
            s.inputs?.length > 0 ? (
              <div className="signature-view">
                <span className="moe-text-secondary">Custom agreement input value: {s.inputs.join(',')}</span>
                <img src={s.signature} />
              </div>
            ) : (
              <img src={s.signature} />
            ),
            false,
            'custom',
          );
        })}
        {submission.petList.map((p) => {
          return (
            <Fragment key={key++}>
              <div className="block border">Pet information</div>

              {submission.questionList.map((q) => {
                if (!q.isShow || q.type !== IntakeFormQuestionCategory.ForPet || q.key === 'Vaccine_document') {
                  return null;
                }
                const isRequired = q.isRequired > 0;
                switch (q.key) {
                  case 'Pet_name':
                    return renderMeta('Pet name', p.petName, isRequired);
                  case 'Pet_type':
                    return renderMeta(
                      'Pet type',
                      petTypeMap.mustGetItem(PetTypeRecord.ownId(p.petTypeId, currentAccountId)).typeName,
                      isRequired,
                    );
                  case 'Pet_breed':
                    return renderMeta('Pet breed', p.breed, isRequired);

                  case 'Pet_image':
                    return renderMeta(
                      'Pet image',
                      <Avatar
                        src={p.avatarPath}
                        type={getPetAvatarType(p.petTypeId)}
                        size="large"
                        className="remove-border-radius"
                        onClick={() =>
                          setAvatarModal({ visible: true, avatarPath: p.avatarPath, petTypeId: p.petTypeId })
                        }
                      />,
                      isRequired,
                    );
                  case 'Fixed':
                    return renderMeta('Fixed', p.fixed, isRequired);
                  case 'Coat_type':
                  case 'Hair_length':
                    return renderMeta('Coat type', p.hairLength, isRequired);
                  case 'Birthday':
                    return renderMeta('Birthday', business.formatFixedDate(p.birthday), isRequired);
                  case 'Gender':
                    return renderMeta('Gender', PetGender.mapLabels[p.gender], isRequired);
                  case 'Vaccine':
                    return renderMeta(
                      'Vaccine',
                      p.vaccineList?.map((v, i) => (
                        <div key={i} className="vaccine-row">
                          {`${vaccineMap.mustGetItem(v.vaccineId).name} (${business.formatFixedDate(
                            v.expirationDate,
                          )})`}
                          {renderVaccineDocView(v.vaccineDocument)}
                        </div>
                      )),
                      isRequired,
                    );
                  case 'Weight':
                    return renderMeta('Weight', p.weight ? `${p.weight} ${business.unitOfWeight}` : '', isRequired);
                  case 'Vet_name':
                    return renderMeta('Vet name', p.vetName, isRequired);
                  case 'Vet_phone_number':
                    return renderMeta('Vet phone', p.vetPhone, isRequired);
                  case 'Emergency_contact':
                    return renderMeta(
                      'Emergency\ncontact',
                      p.emergencyContactName && p.emergencyContactPhone
                        ? `${p.emergencyContactName} (${p.emergencyContactPhone})`
                        : p.emergencyContactName || p.emergencyContactPhone,
                      isRequired,
                    );
                  case 'Health_issues':
                    return renderMeta('Health issues', p.healthIssues, isRequired);
                  case 'Behavior':
                    return renderMeta('Behavior', p.behavior, isRequired);
                  case 'Vet_address':
                    return renderMeta('Vet address', p.vetAddress, isRequired);

                  default: {
                    const answer = p.petQuestionAnswers?.[q.key];
                    return renderCustomMeta(q, answer);
                  }
                }
              })}
            </Fragment>
          );
        })}
      </div>
      <div className="footer">
        {!submission.isCreate && submission.conflictCustomerId ? (
          <div className="additional-msg">
            <SvgIcon src={SvgIconQuestionSvg} size={14} color="#FAAD14" />
            <span>{`The ${
              submission.isPhoneConflict ? 'phone number' : 'email'
            } conflicts with an existing profile.`}</span>
          </div>
        ) : null}
        {alertMsgFlag.value && (
          <div className="add-wait-list-msg">
            <ImgIcon
              src={addToWaitListSuccessFlag.value ? IconIconSelectedGreenSvg : IconAlertTrianglePng}
              alt="Alert"
              width={addToWaitListSuccessFlag.value ? 14 : 18}
            />
            {addToWaitListSuccessFlag.value ? (
              <span>Successfully added to waitlist.</span>
            ) : (
              <span>You need to {submission.conflictCustomerId ? 'merge' : 'create'} a profile first</span>
            )}
            <ImgIcon src={IconIconCloseSvg} alt="X" onClick={alertMsgFlag.close} width={16} />
          </div>
        )}
        <div className="actions">
          <Popconfirm
            title="Are you sure to delete this submission?"
            okText="Yes"
            okType="danger"
            placement="topRight"
            onConfirm={async () => {
              await dispatch(removeIntakeFormSubmission(id!));
              onClose(true);
            }}
          >
            <Button btnType="danger" buttonRadius="circle" fill={false}>
              Delete
            </Button>
          </Popconfirm>
          {!isLead && (
            <WithPricingEnableUpgrade permission="waitingList">
              <Button
                btnType={isExistCustomer ? 'primary' : 'black'}
                buttonRadius="circle"
                className={isExistCustomer ? 'add-wait-list-btn' : 'add-wait-list-disabled-btn'}
                fill={false}
                loading={isExistCustomer ? handleAddToWaitingList.isBusy() : false}
                onClick={isExistCustomer ? handleAddToWaitingList : alertMsgFlag.open}
              >
                Add to waitlist
              </Button>
            </WithPricingEnableUpgrade>
          )}
          <WithPricingEnableUpgrade permission="intakeForm">
            <Button
              btnType="primary"
              buttonRadius="circle"
              disabled={isExistCustomer}
              onClick={handleAdd}
              loading={handleAdd.isBusy()}
            >
              {submission.isCreate
                ? 'Added'
                : submission.conflictCustomerId
                  ? 'Check existing profile'
                  : 'Create profile'}
            </Button>
          </WithPricingEnableUpgrade>
        </div>
      </div>
    </IntakeFormSubmissionModalView>
  );
});
