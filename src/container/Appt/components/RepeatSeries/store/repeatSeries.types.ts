import { type Optional } from 'utility-types';
import { type OpenApiDefinitions, type OpenApiModels } from '../../../../../openApi/schema';
import { FrequencyUnitKinds } from '../../../../../store/customer/customer.boxes';
import { createEnum } from '../../../../../store/utils/createEnum';

export const RepeatSettingType = createEnum({
  NO_REPEAT: [-1, "Doesn't repeat"],
  RULES: [1, 'Repeat on rules'],
  SELECTED_DAYS: [2, 'Repeat on selected days'],
});

// 重复类型
export const RepeatType = createEnum({
  UNSET: [0, 'Unset'],
  DAY: [1, 'Day'],
  WEEK: [2, 'Week'],
  MONTH: [3, 'Month'],
});

// 两个值不太一样，需要转换
export const PreferredFreqTypeToRepeatTypeMap = {
  [FrequencyUnitKinds.Day]: RepeatType.DAY,
  [FrequencyUnitKinds.Week]: RepeatType.WEEK,
  [FrequencyUnitKinds.Month]: RepeatType.MONTH,
};

// SS 参数默认值
export const SSDefaultBeforeAfterDayMap = {
  [RepeatType.DAY]: 0,
  [RepeatType.WEEK]: 2,
  [RepeatType.MONTH]: 3,
};

// 重复类型对应重复次数
export const RepeatEveryMap = {
  [RepeatType.DAY]: 30,
  [RepeatType.WEEK]: 12,
  [RepeatType.MONTH]: 12,
};

// 每周几，repeatType = 2 时必填，1-7 分别代表周一到周日
export const RepeatBy = createEnum({
  UNSET: [0, 'Unset'],
  MONDAY: [1, 'Monday'],
  TUESDAY: [2, 'Tuesday'],
  WEDNESDAY: [3, 'Wednesday'],
  THURSDAY: [4, 'Thursday'],
  FRIDAY: [5, 'Friday'],
  SATURDAY: [6, 'Saturday'],
  SUNDAY: [7, 'Sunday'],
});

// repeatType = 3 时月重复类型: 1-每月第几个的周几, 2-每月第几天
export const RepeatEveryType = createEnum({
  WEEK_DAY: [1, 'Month Week day'],
  MONTH_DAY: [2, 'Month day'],
});

// 重复规则类型，按次数还是按结束时间
export const RepeatRuleType = createEnum({
  COUNT: ['1', 'Count'],
  END_DATE: ['2', 'End days'],
});

export type RepeatApptInfoDto = OpenApiDefinitions['grooming']['com.moego.server.grooming.dto.RepeatAppointmentDto'];

export interface SaveRepeatAppointmentParams
  extends Omit<
    OpenApiDefinitions['grooming']['com.moego.server.grooming.params.SaveRepeatAppointmentParams'],
    'serviceList'
  > {
  serviceList: Optional<
    OpenApiDefinitions['grooming']['com.moego.server.grooming.params.SaveRepeatAppointmentParams']['serviceList'][number],
    'lodgingId' | 'priceOverrideType' | 'durationOverrideType' | 'quantityPerDay'
  >[];
}

export type SaveRepeatTicketParams = OpenApiModels['PUT/grooming/repeat/v2/appointment/list']['Req'];

export const RepeatScheduleType = createEnum({
  NORMAL: [1, 'Normal'],
  SMART_SCHEDULE: [2, 'Smart schedule'],
});
