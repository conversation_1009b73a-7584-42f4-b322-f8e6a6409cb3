import React from 'react';
import { AppliedDiscountCodePopover } from '../../../../../../components/Invoice/AppliedDiscountCodePopover';
import { ApptPayStatus } from '../../../../../Calendar/latest/ApptCalendar/components/ApptPayStatus';
import { TicketPaymentStatus } from '../../../../../TicketDetail/interfaces';
import { ApptTicketInfoRender } from '../../ApptTicketInfoRender';
import { ApptEditTicketStatusFooter } from './ApptEditTicketStatusFooter';
import { useIsApptPaymentProcessing } from '../../../../hooks/useIsApptPaymentProcessing';
import { Markup } from '@moego/ui';
import { MinorPaymentInProgress } from '@moego/icons-react';

export interface ApptInfoFooterProps {
  className?: string;
  ticketId?: number;
  scene?: string;
}

const footerStyle = { boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.12)' } as React.CSSProperties;

export function ApptInfoFooter(props: ApptInfoFooterProps) {
  const { isApptPaymentProcessing } = useIsApptPaymentProcessing({ apptId: props.ticketId?.toString() });

  return (
    <div className="moe-p-[20px] moe-flex moe-flex-col moe-gap-y-[16px]" style={footerStyle}>
      {
        <ApptTicketInfoRender ticketId={props.ticketId}>
          {({ ticket, business }) => {
            const { paidAmount, apptPayStatus } = ticket;
            const { estimatedTotal } = ticket;
            const isPartialPaid = apptPayStatus === TicketPaymentStatus.PartialPaid;
            const isPaid = apptPayStatus === TicketPaymentStatus.Paid;
            console.log('ticket', ticket.toJS(), apptPayStatus, paidAmount);

            return (
              <>
                <div className="moe-flex moe-items-start moe-justify-between">
                  <div className="moe-text-[#333] moe-text-[16px] moe-font-medium moe-leading-[18px]">
                    {isPaid ? 'Total paid' : 'Estimated total'}
                  </div>
                  <div className="moe-flex moe-flex-col moe-items-end">
                    <div className="moe-flex moe-items-center moe-text-[24px] meo-text-[#202020] moe-font-bold moe-leading-[28px]">
                      {business.formatAmount(isPaid ? paidAmount : estimatedTotal)}
                      <AppliedDiscountCodePopover invoiceId={+ticket.invoice.invoiceId} />
                    </div>
                    {isApptPaymentProcessing ? (
                      <div className="moe-min-w-0 moe-flex moe-items-center">
                        <MinorPaymentInProgress />
                        <Markup
                          variant="caption"
                          className="moe-min-w-0 moe-flex-1 moe-whitespace-nowrap moe-overflow-hidden"
                        >
                          Payment processing
                        </Markup>
                      </div>
                    ) : (
                      <ApptPayStatus
                        payStatus={apptPayStatus}
                        labelTxt={isPartialPaid ? `${business.formatAmount(paidAmount)} paid` : undefined}
                      />
                    )}
                  </div>
                </div>
                <ApptEditTicketStatusFooter ticketId={+ticket.appointment.id} scene={props.scene} />
              </>
            );
          }}
        </ApptTicketInfoRender>
      }
    </div>
  );
}
