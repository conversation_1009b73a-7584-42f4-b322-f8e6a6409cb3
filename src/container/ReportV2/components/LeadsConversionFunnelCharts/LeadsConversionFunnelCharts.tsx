import { TransformArrow, IndicatorItem, ReportFetch } from '@moego/reporting';
import React, { memo } from 'react';

export interface LeadsConversionFunnelChartsProps {
  diagramId: string;
}

export const LeadsConversionFunnelCharts = memo<LeadsConversionFunnelChartsProps>(
  function LeadsConversionFunnelCharts(props) {
    const { diagramId } = props;

    return (
      <ReportFetch loadingClassName="moe-flex-1 moe-min-0" diagramId={[diagramId]}>
        {(...[, [data]]) => {
          const steps = data?.procedureNumberData?.steps || [];
          return (
            <div className="moe-flex moe-min-w-0 moe-flex-1 moe-items-center moe-gap-x-xs">
              <div className="moe-flex moe-min-h-0 moe-min-w-0 moe-flex-1 moe-items-end moe-justify-between moe-gap-x-s">
                {steps.map((step, index) => {
                  const isFirst = index === 0;
                  const isFirstEmpty = isFirst && step?.stepData?.value?.int64 === '0';
                  let barHeight = 175;
                  if (isFirstEmpty) {
                    barHeight = 0;
                  } else if (!isFirst) {
                    barHeight =
                      ((Number(step?.stepData?.value?.int64) || 0) / (Number(steps[0]?.stepData?.value?.int64) || 1)) *
                      175;
                  }
                  return (
                    <React.Fragment key={index}>
                      {!isFirst && (
                        <TransformArrow
                          className="moe-flex moe-min-w-[100px] moe-flex-1 moe-flex-col moe-items-center"
                          value={step.leadData}
                          lineClassName="moe-self-stretch"
                          descriptionClassName="moe-items-start moe-flex-col moe-mb-xs"
                        />
                      )}
                      <IndicatorItem
                        className="moe-min-w-0 moe-gap-y-0 moe-px-0 moe-py-0 hover:moe-bg-white"
                        value={step.stepData}
                        type="raw"
                        hideCompare
                        showDot={false}
                        barConfig={{ showBar: true, barHeight }}
                      />
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          );
        }}
      </ReportFetch>
    );
  },
);
