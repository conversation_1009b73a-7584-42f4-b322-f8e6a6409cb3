import { MinorInfoOutlined } from '@moego/icons-react';
import {
  ExcludeUnfinishedAppointmentKey,
  FilterByPreviousType,
  PreviousType,
  type ReportFilterParams,
  calcPreDates,
  useDashboardFilterContext,
  useDashboardMeta,
  usePresetDates,
} from '@moego/reporting';
import { Checkbox, DateRangePicker, Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { SingleLocationSelector } from '../../../components/Business/SingleLocationSelector';
import { WithMultiLocation } from '../../../components/WithFeature/WithMultiLocation';
import { ALL_BUSINESS } from '../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useFilterSticky } from '../hooks/useFilterSticky';

interface InnerFilterBarProps {
  className?: string;
  showExcludeUnfinishedAppt: boolean;
  showPreviousType: boolean;
  showBusiness?: boolean;
  tooltipConfig?: {
    show: boolean;
    content: string;
  };
}

export function InnerFilterBar({
  className,
  showBusiness = true,
  showExcludeUnfinishedAppt,
  showPreviousType,
  tooltipConfig,
}: InnerFilterBarProps) {
  const [business] = useSelector(selectCurrentBusiness);
  const [meta] = useDashboardMeta();
  const excludeUnFinishedApptDescription = useMemo(() => {
    if (!meta) return '';
    for (const page of meta.dashboardPages) {
      for (const group of page.groups) {
        for (const diagram of group.diagrams) {
          for (const filter of diagram.filters || []) {
            if (filter.fieldKey === ExcludeUnfinishedAppointmentKey) {
              return filter.name || '';
            }
          }
        }
      }
    }
    return '';
  }, [meta]);

  const { tenants, dates, onChange, previousType, excludeUnfinishedAppt } = useDashboardFilterContext();
  const presetDates = usePresetDates();
  const { ref, isSticky } = useFilterSticky();

  return (
    <div
      ref={ref}
      className={cn(
        'moe-flex moe-flex-row moe-items-center moe-gap-x-xs  moe-bg-neutral-sunken-0 moe-py-[15px] moe-px-s moe-rounded-m',
        // 这里z-index=3是因为页面中有moego/ui下的table 内部有个z-index=2写死了(不能customized)，需要这里覆盖一下
        'moe-sticky moe-top-[-25px] moe-z-[3] moe-border moe-border-[transparent] moe-transition-all',
        isSticky && 'moe-mx-[-32px] moe-rounded-none moe-border-b-divider',
        className,
      )}
    >
      {showBusiness && (
        <WithMultiLocation scene="accessReport">
          <SingleLocationSelector
            showAll
            isSearchable={false}
            scene="accessReport"
            value={tenants.isAll ? ALL_BUSINESS : tenants.ids[0]}
            onChange={(locationId) => {
              const isAll = locationId === ALL_BUSINESS;
              onChange({
                tenants: {
                  ...tenants,
                  isAll,
                  ids: isAll ? [] : [locationId],
                },
              });
            }}
            menuPosition="fixed"
            className="moe-w-[260px]"
          />
        </WithMultiLocation>
      )}
      <DateRangePicker
        className="moe-w-[312px]"
        value={dates}
        onChange={(dates) => {
          const nextState: Partial<ReportFilterParams> = { dates: dates as ReportFilterParams['dates'], previousType };
          // if previousType is CUSTOM_PERIOD, then reset to PREVIOUS_PERIOD
          if (previousType === PreviousType.CUSTOM_PERIOD) {
            nextState.previousType = PreviousType.PREVIOUS_PERIOD;
          }
          const nextPreviousType = nextState.previousType!;
          if ([PreviousType.PREVIOUS_PERIOD, PreviousType.PREVIOUS_YEAR].includes(nextPreviousType)) {
            nextState.prePeriodDates = calcPreDates(nextState.dates!, { previousType: nextPreviousType });
          }
          onChange(nextState);
        }}
        presets={presetDates}
        isClearable={false}
        format={business.dateFormat}
      />
      {tooltipConfig?.show && (
        <Tooltip content={tooltipConfig.content} side="top">
          <MinorInfoOutlined className="moe-cursor-pointer" />
        </Tooltip>
      )}
      {showPreviousType && <FilterByPreviousType className="moe-w-[200px]" />}
      {showExcludeUnfinishedAppt && (
        <div className="moe-flex moe-items-center moe-gap-xxs">
          <Checkbox
            isSelected={excludeUnfinishedAppt}
            onChange={(excludeUnfinishedAppt) => {
              onChange({ excludeUnfinishedAppt });
            }}
            classNames={{ contentText: 'moe-gap-xxs' }}
          >
            Exclude unfinished appointment
          </Checkbox>
          {excludeUnFinishedApptDescription ? (
            <Tooltip content={excludeUnFinishedApptDescription} side="top">
              <MinorInfoOutlined className="moe-cursor-pointer moe-text-icon-surface" />
            </Tooltip>
          ) : null}
        </div>
      )}
    </div>
  );
}
