import { ExcludeUnfinishedAppointment<PERSON><PERSON>, useDashboardMetaCtx } from '@moego/reporting';
import { Tabs } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { Route, Switch, useHistory } from 'react-router';
import { PATH_REPORT_INSIGHTS_DASHBOARD, ReportTabKey, type ReportDashboardParams } from '../../../router/paths';
import { type PricingPermissionKey } from '../../../store/company/company.boxes';
import { useRouteParams } from '../../../utils/RoutePath';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { InnerFilterBar } from './InnerFilterBar';
import { DashboardPanes } from './ReportDashboard.routes';
import { UpgradePanelPlaceholder } from './ReportDashboard.utils';
import { PermissionInterceptor } from './components/PermissionInterceptor';

export interface ReportTabsProps {
  className?: string;
}

export const ReportTabs = memo<ReportTabsProps>(function ReportTabs({ className }) {
  const { panel } = useRouteParams(PATH_REPORT_INSIGHTS_DASHBOARD);
  const history = useHistory();
  const { dashboardPages } = useDashboardMetaCtx();
  const validateGroupList = dashboardPages.filter((groupItem) => !!DashboardPanes.find((i) => i.tab === groupItem.tab));
  const go2SubPanel = useLatestCallback((panel: ReportDashboardParams['panel']) => {
    history.push(PATH_REPORT_INSIGHTS_DASHBOARD.build({ panel: panel }));
  });
  const showExcludeUnfinishedAppt = useMemo(() => {
    const target = DashboardPanes.find((i) => i.key === panel);
    if (!target) return false;
    const { tab } = target;
    const pages = dashboardPages.find((page) => page.tab === tab);
    if (!pages) return false;
    const { groups } = pages;
    return groups.some((group) => {
      const { diagrams } = group;
      return (diagrams || []).some((diagram) => {
        const { filters = [] } = diagram;
        return filters.some((filter) => filter.fieldKey === ExcludeUnfinishedAppointmentKey);
      });
    });
  }, [panel, dashboardPages]);

  const showPreviousType = useMemo(() => {
    const isDailyRevenue = panel === ReportTabKey.DailyRevenue;
    const isLeads = panel === ReportTabKey.Leads;
    return !isLeads && !isDailyRevenue;
  }, [panel]);

  const showBusiness = useMemo(() => {
    const isLeads = panel === ReportTabKey.Leads;
    return !isLeads;
  }, [panel]);

  const tooltipConfig = useMemo(() => {
    const isLeads = panel === ReportTabKey.Leads;
    return {
      show: isLeads,
      content: 'Metrics are updated with a 2-day delay.',
    };
  }, [panel]);

  return (
    <>
      <Tabs
        disableAnimation
        className={className}
        selectedKey={panel}
        classNames={{ panel: 'moe-pt-0' }}
        onChange={(key) => go2SubPanel(key as ReportDashboardParams['panel'])}
      >
        {validateGroupList.map((groupItem) => {
          const { tab, title } = groupItem;
          const key = DashboardPanes.find((i) => i.tab === tab)!.key;
          return <Tabs.Item key={key} label={title} />;
        })}
      </Tabs>
      <InnerFilterBar
        showExcludeUnfinishedAppt={showExcludeUnfinishedAppt}
        showPreviousType={showPreviousType}
        showBusiness={showBusiness}
        tooltipConfig={tooltipConfig}
      />
      <Switch>
        {validateGroupList.map((groupItem) => {
          const item = DashboardPanes.find((i) => i.tab === groupItem.tab)!;
          const { key: panel, component } = item;
          const path = PATH_REPORT_INSIGHTS_DASHBOARD.build({ panel });
          return (
            <Route
              key={panel}
              path={path}
              render={() => (
                <PermissionInterceptor
                  panelInfo={UpgradePanelPlaceholder[panel]}
                  component={component}
                  permissionCode={groupItem.permissionCode as PricingPermissionKey}
                />
              )}
            />
          );
        })}
        {/* don't need <Redirect /> because Tabs will validate selectedKey, and auto trigger onChange, then jump location */}
      </Switch>
    </>
  );
});
