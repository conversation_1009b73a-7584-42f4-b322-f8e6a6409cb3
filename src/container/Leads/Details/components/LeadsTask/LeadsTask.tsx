import { type z } from '@moego/bff-openapi';
import { type Task, type Task_State, type schemas } from '@moego/bff-openapi/clients/client.leads';
import { MajorPlusOutlined } from '@moego/icons-react';
import { useSerialCallback } from '@moego/tools';
import { Button, Empty, Heading, Spin, cn } from '@moego/ui';
import { useBoolean, useMount } from 'ahooks';
import React, { type HTMLAttributes, memo, useState, useMemo } from 'react';
import { ScrollerProvider } from '../../../../../layout/components/ScrollerProvider';
import { BffLeadsClient } from '../../../../../middleware/bff';
import { dayjsToGoogleTimestamp } from '../../../../../utils/googleTimestamp';
import { type CustomerWithFullName } from '../../hooks/useGetCustomerDetail';
import { CreateOrEditTaskModal, type TaskFormParams } from './CreateOrEditTaskModal';
import { TaskItem } from './TaskItem';

interface LeadsTaskProps extends HTMLAttributes<HTMLDivElement> {
  customer: CustomerWithFullName;
  refreshLog: () => void;
}

export const LeadsTask = memo<LeadsTaskProps>(function LeadsTask(props) {
  const { className, customer, refreshLog, ...rest } = props;

  const [data, setData] = useState<z.infer<typeof schemas.ListCustomerTaskResSchema> | undefined>();
  const [showFinishedList, showFinishedListAction] = useBoolean(false);

  const isEmpty = useMemo(() => data?.new?.length === 0 && data?.finish?.length === 0, [data]);

  const getTasks = useSerialCallback(async () => {
    const res = await BffLeadsClient.getCustomerTaskList({
      customerId: customer.id,
    });
    setData(res);
  });

  useMount(() => {
    getTasks();
  });

  const [isModalVisible, isModalVisibleAction] = useBoolean(false);
  const [editingTask, setEditingTask] = useState<z.infer<typeof schemas.Task> | undefined>(undefined);

  const onCreateTask = () => {
    setEditingTask(undefined);
    isModalVisibleAction.setTrue();
  };

  const onClose = () => {
    setEditingTask(undefined);
    isModalVisibleAction.setFalse();
  };

  const onEditTask = (task: z.infer<typeof schemas.Task>) => {
    setEditingTask(task);
    isModalVisibleAction.setTrue();
  };

  const onDelete = useSerialCallback(async (task: z.infer<typeof schemas.Task>) => {
    await BffLeadsClient.deleteTask({
      task,
      customerId: customer.id,
    });
    getTasks();
    refreshLog();
  });

  const onChangeState = useSerialCallback(async (task: Task, newState: Task_State) => {
    await BffLeadsClient.finishTask({
      customerId: customer.id,
      taskId: task.id,
      state: newState,
      name: task.name,
    });
    getTasks();
    refreshLog();
  });

  const handleUpdateTask = useSerialCallback(async (value: TaskFormParams) => {
    const { name, allocateStaffId, completeTime } = value;
    const formattedCompleteTime = dayjsToGoogleTimestamp(completeTime || null) || undefined;
    await BffLeadsClient.createOrUpdateTask({
      taskId: editingTask?.id,
      name,
      customerId: customer.id,
      allocateStaffId,
      completeTime: formattedCompleteTime,
    });
    getTasks();
    refreshLog();
    setEditingTask(undefined);
    isModalVisibleAction.setFalse();
  });

  const isLoading = getTasks.isBusy() || onChangeState.isBusy() || onDelete.isBusy();

  return (
    <div {...rest} className={cn('moe-flex moe-flex-col moe-gap-[8px]', className)}>
      <div className="moe-flex moe-justify-between moe-items-center">
        <Heading className="moe-pb-0" size={3}>
          Tasks
        </Heading>
        <Button variant="tertiary" icon={<MajorPlusOutlined />} onPress={onCreateTask}>
          Add Task
        </Button>
      </div>
      <Spin
        isLoading={isLoading}
        classNames={{
          base: 'moe-flex-auto moe-overflow-hidden',
          container: 'moe-flex moe-flex-col moe-h-full moe-w-full moe-overflow-hidden',
        }}
      >
        <ScrollerProvider className="moe-flex moe-flex-col moe-gap-[8px] moe-flex-auto">
          {isEmpty ? (
            <Empty className="moe-m-auto" />
          ) : (
            <>
              <div className="moe-flex moe-flex-col">
                {data?.new?.map((task) => (
                  <TaskItem
                    key={task.id}
                    task={task}
                    onEdit={onEditTask}
                    onDelete={onDelete}
                    onChangeState={onChangeState}
                  />
                ))}
              </div>
              {!!data?.finish?.length && (
                <>
                  <Button className="moe-self-start" variant="tertiary" onPress={showFinishedListAction.toggle}>
                    {showFinishedList ? 'Hide' : 'View'} {data?.finish?.length ?? 0} completed tasks
                  </Button>
                  {showFinishedList && (
                    <div className="moe-flex moe-flex-col">
                      {data?.finish?.map((task) => (
                        <TaskItem
                          key={task.id}
                          task={task}
                          onEdit={onEditTask}
                          onDelete={onDelete}
                          onChangeState={onChangeState}
                        />
                      ))}
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </ScrollerProvider>
      </Spin>
      {isModalVisible && (
        <CreateOrEditTaskModal
          confirmButtonProps={{
            isLoading: handleUpdateTask.isBusy(),
          }}
          task={editingTask}
          isOpen={isModalVisible}
          onClose={onClose}
          onSubmit={handleUpdateTask}
        />
      )}
    </div>
  );
});
