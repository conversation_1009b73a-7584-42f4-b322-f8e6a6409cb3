import { type Task, Task_State } from '@moego/bff-openapi/clients/client.leads';
import { MinorMoreOutlined } from '@moego/icons-react';
import { Checkbox, Dropdown, IconButton, Text, cn } from '@moego/ui';
import { useStore } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectCurrentCompany } from '../../../../../store/company/company.selectors';
import { selectStaff } from '../../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { googleTimestampToDayjs } from '../../../../../utils/googleTimestamp';
import { LeadsTestIds } from '../../../../../config/testIds/leads';

const ActionEnum = {
  EDIT: 'Edit',
  DELETE: 'Delete',
};

interface TaskItemProps extends React.HTMLAttributes<HTMLDivElement> {
  task: Task;
  onEdit: (task: Task) => void;
  onDelete: (task: Task) => void;
  onChangeState: (task: Task, newState: Task_State) => void;
}

export const TaskItem = memo<TaskItemProps>(function TaskItem(props) {
  const { task, className, onEdit, onDelete, onChangeState, ...rest } = props;

  const store = useStore();
  const currentCompany = store.select(selectCurrentCompany);

  const staff = useMemo(() => {
    if (!task.allocateStaffId) return undefined;
    const staff = store.select(selectStaff(Number(task.allocateStaffId)));
    if (isNormal(staff.id)) return staff;
    return undefined;
  }, [task.allocateStaffId, store]);
  const isFinished = task.state === Task_State.FINISH;

  const onAction = (action: keyof typeof ActionEnum) => {
    switch (action) {
      case ActionEnum.EDIT:
        onEdit(task);
        break;
      case ActionEnum.DELETE:
        onDelete(task);
        break;
      default:
        break;
    }
  };

  const handleChangeStateChange = (v: boolean) => {
    onChangeState(task, v ? Task_State.FINISH : Task_State.NEW);
  };

  return (
    <div
      {...rest}
      className={cn(
        'moe-flex moe-gap-[8px] moe-flex-col moe-p-[12px] hover:moe-bg-neutral-sunken-0 moe-group moe-rounded-[12px]',
        className,
      )}
    >
      <div className="moe-flex moe-justify-between moe-overflow-hidden moe-gap-[8px]">
        <Checkbox
          classNames={{
            base: 'moe-flex-auto moe-overflow-hidden',
            content: 'moe-overflow-hidden',
          }}
          isSelected={isFinished}
          onChange={handleChangeStateChange}
        >
          <Text variant="regular-short" ellipsis>
            {task.name}
          </Text>
        </Checkbox>
        <Dropdown>
          <Dropdown.Trigger>
            <IconButton
              variant="primary"
              size="xs"
              icon={<MinorMoreOutlined />}
              color="transparent"
              data-testid={LeadsTestIds.LeadsTaskMoreActionIconButton}
              className="moe-flex-none moe-invisible group-hover:moe-visible"
            />
          </Dropdown.Trigger>
          <Dropdown.Menu onAction={(key) => onAction(key as keyof typeof ActionEnum)}>
            <Dropdown.Item key={ActionEnum.EDIT}>Edit task</Dropdown.Item>
            <Dropdown.Item key={ActionEnum.DELETE} isDestructive>
              Delete task
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </div>
      {(task.completeTime || staff) && (
        <div className="moe-flex moe-flex-col moe-mx-[28px] moe-gap-[4px]">
          {task.completeTime && (
            <Text variant="caption" className="moe-text-tertiary">
              {currentCompany.formatDateTime(googleTimestampToDayjs(task.completeTime))}
            </Text>
          )}
          {staff && (
            <Text variant="caption" className="moe-text-tertiary">
              {staff.fullName()}
            </Text>
          )}
        </div>
      )}
    </div>
  );
});
