import {
  HistoryLog_Message_Direction,
  HistoryLog_Message_State,
  type HistoryLog_Action,
} from '@moego/bff-openapi/clients/client.leads';
import { useLatestCallback } from '@moego/finance-utils';
import { useSerialCallback } from '@moego/tools';
import { useDispatch, useStore } from 'amos';
import { useParams } from 'react-router';
import { type PicInfo } from '../../../../../../components/MessageSendBox/components/SendImageModal/types';
import { type SendMessageOptions } from '../../../../../../components/MessageSendBox/type';
import { type LeadsDetailParams } from '../../../../../../router/paths';
import {
  addThread,
  sendMultipleImagesToCustomer,
  sendSingleMessage,
} from '../../../../../../store/message/message.actions';
import { MessageDetailType } from '../../../../../../store/message/message.boxes';
import { selectBusinessMessageThread } from '../../../../../../store/message/message.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { type CustomerWithFullName } from '../../../hooks/useGetCustomerDetail';

export const useLeadsActivities = (
  customer: CustomerWithFullName,
  createLog: (action: HistoryLog_Action) => Promise<void>,
): {
  handleSendText: (text: string, options: SendMessageOptions) => Promise<void>;
  handleSendImage: (url: string, options: SendMessageOptions) => Promise<void>;
  handleSendMultipleImages: (list: PicInfo[], options: SendMessageOptions) => Promise<number>;
} => {
  const { id } = useParams<LeadsDetailParams>();
  const dispatch = useDispatch();
  const store = useStore();

  const thread = store.select(selectBusinessMessageThread(+id));

  const handleSendImage = useSerialCallback((url: string, options: SendMessageOptions) =>
    handleSendMessage(url, MessageDetailType.Picture, options),
  );
  const handleSendMultipleImages = useLatestCallback(async (list: PicInfo[], options: SendMessageOptions) => {
    const groupCount = await dispatch(
      sendMultipleImagesToCustomer(
        {
          customer: {
            customerId: Number(customer.id),
            contactName: customer.fullName,
            customerName: customer.fullName,
            customerNumber: customer.phoneNumber,
            customerEmail: customer.email,
            contactId: Number(customer.contact?.id),
          },
          fileInfoList: list,
        },
        {
          method: options.method,
          needGroup: true,
        },
      ),
    );
    return groupCount || 0;
  });
  const handleSendMessage = useSerialCallback(
    async (text: string, type: MessageDetailType, options: SendMessageOptions) => {
      let threadId = thread.id;
      if (!isNormal(threadId)) {
        threadId = (await dispatch(addThread(Number(customer.id)))).id;
      }
      const res = await dispatch(
        sendSingleMessage({
          targetId: threadId,
          messageBody: text,
          messageType: type,
          customer: {
            customerId: Number(customer.id),
            contactName: customer.fullName,
            customerNumber: customer.phoneNumber,
            contactId: Number(customer.contact?.id),
          },
          method: options.method,
        }),
      );
      await createLog({
        action: {
          case: 'message',
          value: {
            messageId: String(res.id),
            text: res.messageText,
            state: HistoryLog_Message_State.SUCCEEDED,
            direction: HistoryLog_Message_Direction.SEND,
            failReason: '',
          },
        },
      });
    },
  );

  const handleSendText = useSerialCallback((text: string, options: SendMessageOptions) =>
    handleSendMessage(text, MessageDetailType.Text, options),
  );

  return {
    handleSendText,
    handleSendImage,
    handleSendMultipleImages,
  };
};
