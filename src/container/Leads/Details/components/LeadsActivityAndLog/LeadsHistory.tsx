import { type z } from '@moego/bff-openapi';
import { type schemas, HistoryLog_Type as HistoryLogType } from '@moego/bff-openapi/clients/client.leads';
import {
  MinorCallOutboundOutlined,
  MinorMessageOutlined,
  MinorNoteOutlined,
  MinorTaskOutlined,
} from '@moego/icons-react';
import { useSerialCallback } from '@moego/tools';
import { Empty, Heading, SegmentControl, Spin, cn } from '@moego/ui';
import { useInfiniteScroll, useMemoizedFn } from 'ahooks';
import React, {
  type HTMLAttributes,
  memo,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
  type ReactNode,
} from 'react';
import { ScrollerProvider } from '../../../../../layout/components/ScrollerProvider';
import { BffLeadsClient } from '../../../../../middleware/bff';
import { META_DATA_KEY_LIST } from '../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../store/metadata/metadata.hooks';
import { HistoryItem } from './HistoryItem/HistoryItem';
import { isUndefined } from 'lodash/fp';

const ALL_FILTER_VALUE = '_internal_all_';

interface LeadsHistoryProps extends HTMLAttributes<HTMLDivElement> {
  customerId: string;
  header?: ReactNode;
}

export interface LeadsHistoryRef {
  reload: () => void;
}

export const LeadsHistory = memo(
  forwardRef<LeadsHistoryRef, LeadsHistoryProps>(function LeadsHistory(props, ref) {
    const { customerId, className, header, ...rest } = props;
    const [filterValue, setFilterValue] = useState(ALL_FILTER_VALUE);
    const [engagementCenter] = useMetaData<boolean>(META_DATA_KEY_LIST.EngagementCenter);

    const LOG_OPTIONS = [
      {
        label: 'All',
        value: ALL_FILTER_VALUE,
      },
      {
        label: 'Messages',
        value: HistoryLogType.MESSAGE,
        icon: <MinorMessageOutlined />,
      },
      ...(engagementCenter
        ? [
            {
              label: 'Calls',
              value: HistoryLogType.CALL,
              icon: <MinorCallOutboundOutlined />,
            },
          ]
        : []),
      {
        label: 'Notes',
        value: HistoryLogType.NOTE,
        icon: <MinorNoteOutlined />,
      },
      {
        label: 'Tasks',
        value: HistoryLogType.TASK,
        icon: <MinorTaskOutlined />,
      },
    ];

    const scrollContainerRef = useRef<HTMLDivElement>(null);

    const { data, loading, reloadAsync, mutate } = useInfiniteScroll<
      z.infer<typeof schemas.listHistoryLogResponseSchema>
    >(
      async (d) => {
        if (!d) {
          scrollContainerRef.current?.scrollTo({ top: 0 });
        }
        return BffLeadsClient.listHistoryLog({
          customerId,
          filter: {
            type: filterValue === ALL_FILTER_VALUE ? undefined : (Number(filterValue) as HistoryLogType),
          },
          pageNum: d?.nextPage ?? 1,
        });
      },
      {
        target: scrollContainerRef.current,
        reloadDeps: [customerId, filterValue],
        isNoMore: (d) => !(d?.hasMore ?? true),
      },
    );

    const onReload = useSerialCallback(async () => {
      await reloadAsync();
      scrollContainerRef.current?.scrollTo({ top: 0 });
    });

    useImperativeHandle(
      ref,
      () => ({
        reload: onReload,
      }),
      [onReload],
    );

    const temporaryUpdateLog = useMemoizedFn((history: z.infer<typeof schemas.HistoryLog>) => {
      if (!data?.list) return;
      const newList = data?.list?.map((item) => {
        if (item.id === history.id) {
          return history;
        }
        return item;
      });
      mutate({
        ...data,
        list: newList,
      });
    });

    const headerNode = isUndefined(header) ? (
      <Heading className="moe-flex-none" size={3}>
        History
      </Heading>
    ) : (
      header
    );

    return (
      <div {...rest} className={cn('moe-flex moe-flex-col moe-gap-m moe-overflow-hidden', className)}>
        {headerNode}
        <div className="moe-flex-none moe-flex moe-justify-start">
          <SegmentControl onChange={setFilterValue} value={filterValue} className="moe-flex-none">
            {LOG_OPTIONS.map((option) => (
              <SegmentControl.Item
                classNames={{
                  base: 'moe-whitespace-nowrap',
                }}
                className="moe-h-[28px]"
                key={option.value}
                value={option.value as string}
                label={option.label}
                icon={option.icon}
              />
            ))}
          </SegmentControl>
        </div>
        <Spin
          isLoading={loading}
          classNames={{
            container: ' moe-gap-[8px] moe-flex-shrink moe-h-full moe-w-full moe-overflow-hidden',
          }}
          className="moe-h-full moe-w-full  moe-overflow-hidden"
        >
          <ScrollerProvider
            ref={scrollContainerRef}
            className="moe-w-full moe-h-full moe-flex moe-flex-col moe-gap-[8px]"
          >
            {data?.list?.length ? (
              data?.list?.map((item) => (
                <HistoryItem
                  temporaryUpdateLog={temporaryUpdateLog}
                  className="moe-flex-none"
                  history={item}
                  key={item.id}
                  isLast={!data.hasMore && item.id === data.list[data.list.length - 1].id}
                />
              ))
            ) : (
              <Empty />
            )}
          </ScrollerProvider>
        </Spin>
      </div>
    );
  }),
);
