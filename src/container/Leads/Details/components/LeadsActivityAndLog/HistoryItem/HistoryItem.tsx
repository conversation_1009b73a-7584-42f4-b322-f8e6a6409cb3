import { HistoryLog_Type as HistoryLogType } from '@moego/bff-openapi/clients/client.leads';
import { type Timestamp } from '@moego/api-web/google/protobuf/timestamp';
import { type z } from '@moego/bff-openapi';
import { type HistoryLog, type schemas } from '@moego/bff-openapi/clients/client.leads';
import { useMemoizedFn } from 'ahooks';
import { useStore } from 'amos';
import React, { type HTMLAttributes, memo } from 'react';
import { BffLeadsClient } from '../../../../../../middleware/bff';
import { selectCurrentCompany } from '../../../../../../store/company/company.selectors';
import { googleTimestampToDayjs } from '../../../../../../utils/googleTimestamp';
import { CallHistory } from './CallHistory/CallHistory';
import { MessageHistory } from './MessageHistory/MessageHistory';
import { NoteHistory } from './NoteHistory/NoteHistory';
import { TaskHistory } from './TaskHistory/TaskHistory';
import { LeadConvert } from './LeadConvert';
import { LeadCreate } from './LeadCreate';
import {
  type HistoryActionAction,
  type HistoryAction,
  type HistoryValue,
} from '../../../../LeadsConvertHistory/constants';

export interface HistoryItemParams<Value extends HistoryValue> extends HistoryLog {
  dateTime: string;
  value: Value;
  updateLog: (value: Value) => void;
  /** @default false */
  isLast?: boolean;
}

interface HistoryItemProps extends HTMLAttributes<HTMLDivElement> {
  history: z.infer<typeof schemas.HistoryLog>;
  /** 更新log后不刷新列表，只在内存里更新一下，避免刷新导致滚动位置丢失 */
  temporaryUpdateLog: (history: z.infer<typeof schemas.HistoryLog>) => void;
  /** @default false */
  isLast?: boolean;
}

export const HistoryItem = memo<HistoryItemProps>(function HistoryItem(props) {
  const { history, temporaryUpdateLog, isLast } = props;

  const { id, createTime, type } = history;
  const action = history.action as HistoryAction;
  const store = useStore();
  const currentCompany = store.select(selectCurrentCompany);

  const updateLog = useMemoizedFn(async (value: HistoryAction['action']['value']) => {
    if (!value || !action.action.case) {
      return;
    }

    const newAction: HistoryAction = {
      action: {
        case: action.action.case,
        value,
      } as HistoryActionAction,
    };
    await BffLeadsClient.updateHistoryLog({
      logId: id,
      action: newAction,
    });
    temporaryUpdateLog({
      ...history,
      action: newAction,
    });
  });

  if (!action.action.value || !action.action.case) {
    console.error('action.action.value or action.action.case is undefined', action);
    return null;
  }
  const params: HistoryItemParams<any> = {
    ...history,
    isLast,
    value: action.action.value,
    dateTime: currentCompany.formatDateTime(googleTimestampToDayjs(createTime as unknown as Timestamp)),
    updateLog,
  };
  switch (type) {
    case HistoryLogType.CALL:
      return <CallHistory {...params} />;
    case HistoryLogType.MESSAGE:
      return <MessageHistory {...params} />;
    case HistoryLogType.NOTE:
      return <NoteHistory {...params} />;
    case HistoryLogType.TASK:
      return <TaskHistory {...params} />;
    case HistoryLogType.CREATE:
      return <LeadCreate {...params} />;
    case HistoryLogType.CONVERT:
      return <LeadConvert {...params} />;
    case HistoryLogType.TYPE_UNSPECIFIED:
    default:
      return null;
  }
});
