import { Pet_PetType as PetPetType } from '@moego/bff-openapi/clients/client.leads';
import { type z } from '@moego/bff-openapi';
import { type schemas } from '@moego/bff-openapi/clients/client.leads';
import { CompressedAvatar } from '@moego/business-components';
import { MinorMoreOutlined } from '@moego/icons-react';
import { Dropdown, IconButton, Text, cn } from '@moego/ui';
import React, { type Key, memo, useMemo } from 'react';

enum ActionKey {
  EDIT = 'edit',
  DELETE = 'delete',
}

interface PetCardProps extends React.HTMLAttributes<HTMLDivElement> {
  pet: z.infer<typeof schemas.Pet>;
  onDelete?: (pet: z.infer<typeof schemas.Pet>) => void;
  onEdit?: (pet: z.infer<typeof schemas.Pet>) => void;
}

export const PetCard = memo<PetCardProps>(function PetCard(props) {
  const { pet, onDelete, onEdit, className, ...rest } = props;

  const avatarType = useMemo(() => {
    switch (pet.petType) {
      case PetPetType.DOG:
        return 'dog';
      case PetPetType.CAT:
        return 'cat';
      default:
        return 'other';
    }
  }, [pet]);

  const onAction = (key: Key) => {
    switch (key) {
      case ActionKey.EDIT:
        onEdit?.(pet);
        break;
      case ActionKey.DELETE:
        onDelete?.(pet);
        break;
      default:
        break;
    }
  };

  return (
    <div
      {...rest}
      className={cn('moe-flex moe-px-[12px] moe-py-[8px] moe-gap-[8px] moe-group moe-rounded-[8px]', className)}
    >
      {/* 只用默认头像 */}
      <CompressedAvatar.Pet type={avatarType} name={pet.name} className="moe-flex-none" />
      <div className="moe-flex-auto moe-flex moe-flex-col">
        <Text variant="regular-short" className="moe-text-primary">
          {pet.name}
        </Text>
        <Text variant="small" className="moe-text-tertiary">
          {pet.breed}
        </Text>
      </div>
      <div className="moe-flex-none moe-invisible moe-flex group-hover:moe-visible moe-justify-center moe-items-center">
        <Dropdown>
          <Dropdown.Trigger>
            <IconButton color="transparent" size="s" variant="primary" icon={<MinorMoreOutlined />} />
          </Dropdown.Trigger>
          <Dropdown.Menu onAction={onAction}>
            <Dropdown.Item key={ActionKey.EDIT}>Edit pet</Dropdown.Item>
            <Dropdown.Item key={ActionKey.DELETE} isDestructive>
              Delete pet
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </div>
    </div>
  );
});
