import { Customer_Type } from '@moego/bff-openapi/clients/client.leads';
import { type PetFormFields } from '../../../components/AddClientWithPets/components/PetFormModal/usePetForm';
import { type AddressFormFields } from '../../../components/AddressForm/AddressFormV2';

export interface AddLeadFormBasicFields {
  preferredBusinessId?: string;
  phoneNumber: string;
  type: Customer_Type;
  avatarPath?: string;
  givenName?: string;
  familyName?: string;
  email?: string;
  customizeLifeCycleId: string;
  customizeActionStateId: string;
  source?: string;
  allocateStaffId?: string;
}

export type LeadsFields = AddLeadFormBasicFields &
  AddressFormFields & {
    petList: PetFormFields[];
  };

export const dirtyCheckConfig = {
  title: 'Lead has unsaved changes',
  content: 'Would you like to save your changes before exiting?',
  confirmText: 'Back to edit',
  cancelText: 'Discard changes',
  className: 'moe-w-[480px]',
  isMaskCloseable: false,
};

export const AddLeadFormAnchorMap = {
  LeadInfo: 'Lead Info',
  Address: 'Address',
  Pet: 'Pet',
};
export const AddLeadFormAnchorItem = [
  {
    key: 'profile',
    href: `#${AddLeadFormAnchorMap.LeadInfo}`,
    title: AddLeadFormAnchorMap.LeadInfo,
  },
  {
    key: 'address',
    href: `#${AddLeadFormAnchorMap.Address}`,
    title: AddLeadFormAnchorMap.Address,
  },
  {
    key: 'pet',
    href: `#${AddLeadFormAnchorMap.Pet}`,
    title: AddLeadFormAnchorMap.Pet,
  },
];

export const defaultLeadDetail = {
  businessId: undefined,
  phoneNumber: '',
  type: Customer_Type.LEAD,
  avatarPath: '',
  givenName: '',
  familyName: '',
  email: '',
  customizeLifeCycleId: undefined,
  customizeActionStateId: undefined,
  source: undefined,
  assignStaffId: undefined,
};
