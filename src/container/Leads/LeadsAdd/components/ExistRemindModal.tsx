import { type Customer, Customer_Type } from '@moego/bff-openapi/clients/client.leads';
import { Link, Modal, Text } from '@moego/ui';
import React from 'react';
import { PATH_CUSTOMER_DETAIL, PATH_LEADS_DETAIL } from '../../../../router/paths';
interface ExistRemindModalProps {
  customer?: Customer;
  isOpen: boolean;
  handleClose: () => void;
}

export const ExistRemindModal = ({ isOpen, customer, handleClose }: ExistRemindModalProps) => {
  if (!customer?.id) return null;
  const isLead = customer?.type === Customer_Type.LEAD;
  return (
    <Modal
      title="Convert lead"
      onConfirm={handleClose}
      size="s"
      isOpen={isOpen}
      onClose={handleClose}
      onCancel={handleClose}
      showCancelButton={false}
    >
      {/* 当前用户已经存在过, 详情见  */}
      <Text variant="regular">
        {isLead ? 'Lead' : 'Customer'}&nbsp;
        <Link
          href={
            isLead
              ? PATH_LEADS_DETAIL.build({ id: customer?.id })
              : PATH_CUSTOMER_DETAIL.build({ customerId: Number(customer?.id || '') })
          }
          variant="regular"
        >
          {customer?.givenName || ''} {customer?.familyName || ''}
        </Link>
        &nbsp;has already existed.
      </Text>
    </Modal>
  );
};
