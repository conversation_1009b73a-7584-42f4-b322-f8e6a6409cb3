import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../../../../store/business/role.selectors';
import { printFullName } from '../../../../../../../store/customer/customer.boxes';
import { printAddress } from '../../../../../../../store/customer/customerAddress.selectors';
import {
  OBClientQuestionKey,
  OnlineBookingQuestionCategory,
  OnlineBookingQuestionType,
} from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { onlineBookingLatestRequestMapBox } from '../../../../../../../store/onlineBooking/onlineBooking.boxes';
import { ID_ANONYMOUS } from '../../../../../../../store/utils/identifier';
import { jsonParse } from '../../../../../../../utils/utils';
import {
  type EnumQuestions,
  getLabelValueList,
  replaceUnderscoreWithSpace,
  useClientInfoContactValue,
  usePreferenceValues,
} from '../../../../../components/BookingRequestModal/BookingRequestModal.hooks';
import { ClientLocation, isValidAddress } from '../../../../../components/ClientLocation/ClientLocation';
import { useGetQuestionList } from './useGetQuestionList';

const clientOmitKeyList = ['First_name', 'Last_name', 'Email', 'Phone_number', 'Address', 'Agreement'];
export const useBookingClientInfoList = (bookingRequestId?: number) => {
  const [business, request] = useSelector(
    selectCurrentBusiness,
    onlineBookingLatestRequestMapBox.mustGetItem(bookingRequestId ?? ID_ANONYMOUS),
  );
  const [permissions] = useSelector(selectCurrentPermissions);

  const { customer, address } = request;
  const { customerId, phoneNumber, firstName, lastName, email, questionAnswerList, emergencyContact, pickupContact } =
    customer || {};
  const { preferredDayText, preferredTimeText, preferredFrequency, referralSourceName, preferredGroomerName } =
    usePreferenceValues(customer);
  const contactValue = useClientInfoContactValue(customerId, phoneNumber);

  const questionList = useGetQuestionList(OnlineBookingQuestionCategory.ForOwner, clientOmitKeyList);

  return useMemo(() => {
    const enumQuestions: EnumQuestions = {
      clientName: printFullName(firstName, lastName),
      phoneNumber: contactValue,
      email: email,
      address: (
        <div className="rr-mask">
          {printAddress(address)}
          {isValidAddress(address) && business.isMobileGrooming() && (
            <ClientLocation className="moe-ml-[-140px]" address={address} />
          )}
        </div>
      ),
    };

    const dynamicQuestionsList = questionList.map(({ questionType, key, question }) => {
      const keyWithoutUnderscore = replaceUnderscoreWithSpace(key);
      const answer = questionAnswerList.find((item) => item.key === key)?.answer;
      switch (key) {
        case OBClientQuestionKey.EmergencyContact: {
          const { firstName, lastName, phoneNumber } = emergencyContact || {};
          const value = emergencyContact
            ? `${printFullName(firstName, lastName)} ${business.formatPhoneNumber(phoneNumber)}`
            : '';
          return { label: keyWithoutUnderscore, value };
        }
        case OBClientQuestionKey.PeopleAuthorizeToPickupPets: {
          const { firstName, lastName, phoneNumber } = pickupContact || {};
          const value = pickupContact
            ? `${printFullName(firstName, lastName)} ${business.formatPhoneNumber(phoneNumber)}`
            : '';
          return { label: question, value };
        }
        case OBClientQuestionKey.ReferralSource:
          return { label: keyWithoutUnderscore, value: referralSourceName };
        case OBClientQuestionKey.PreferredGroomer:
          return { label: keyWithoutUnderscore, value: preferredGroomerName };
        case OBClientQuestionKey.PreferredFrequency:
          return { label: keyWithoutUnderscore, value: preferredFrequency };
        case OBClientQuestionKey.PreferredDayOfTheWeek:
          return {
            label: keyWithoutUnderscore,
            value: preferredDayText,
          };
        case keyWithoutUnderscore:
          return { label: 'Preferred time of the day', value: preferredTimeText };
        default:
          if (questionType === OnlineBookingQuestionType.Checkbox) {
            return {
              isCustom: true,
              label: question,
              value: jsonParse(answer, []).join('\n'),
            };
          }
          return { isCustom: true, label: question, value: answer };
      }
    });

    return getLabelValueList(enumQuestions, dynamicQuestionsList);
  }, [business, request, permissions, questionList]);
};
