import { type PetToLodging } from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import { Avatar } from '../../../../../../../components/Avatar/Avatar';
import { useFeedingInstructions } from '../../../../../../../components/ServiceApplicablePicker/hooks/useFeedingInstructions';
import { useMedicationInstructions } from '../../../../../../../components/ServiceApplicablePicker/hooks/useMedicationInstructions';
import { currentAccountIdBox } from '../../../../../../../store/account/account.boxes';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { useBusinessApplicableEvaluation } from '../../../../../../../store/evaluation/evaluation.hooks';
import {
  OBPetQuestionKey,
  OnlineBookingQuestionCategory,
  OnlineBookingQuestionType,
} from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { onlineBookingLatestRequestMapBox } from '../../../../../../../store/onlineBooking/onlineBooking.boxes';
import { PetGender } from '../../../../../../../store/pet/pet.boxes';
import { PetTypeRecord, petTypeMapBox } from '../../../../../../../store/pet/petType.boxes';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import { Scene } from '../../../../../../../store/service/scene.enum';
import { ID_ANONYMOUS, isNormal } from '../../../../../../../store/utils/identifier';
import { getPetAvatarType } from '../../../../../../../utils/BusinessUtil';
import { jsonParse } from '../../../../../../../utils/utils';
import {
  transformMedicationDetailToApptMedicationDetail,
  transformOBFeedingDetailToApptFeedingDetail,
} from '../../../../../../Appt/store/appt.transform';
import { appointmentDateTimeFormat } from '../../../../../../Appt/utils/appointmentDateTimeFormat';
import {
  type EnumQuestions,
  getLabelValueList,
  replaceUnderscoreWithSpace,
} from '../../../../../components/BookingRequestModal/BookingRequestModal.hooks';
import { PetVaccine } from '../../../../../components/BookingRequestModal/PetVaccine';
import { SelectRequestEvaluation } from '../components/SelectRequestEvaluation';
import { SelectRequestEvaluationStaff } from '../components/SelectRequestEvaluationStaff';
import { SelectRequestLodging } from '../components/SelectRequestLodging';
import { useBuildServiceAddOnDesc } from './useBuildServiceAddOnDesc';
import { useGetQuestionList } from './useGetQuestionList';
import { selectSceneCareType } from '../../../../../../../store/careType/careType.selectors';

const petOmitKeyList = ['Pet_name', 'Pet_image', 'Vaccine_document'];

export const useBookingPetInfoList = (
  bookingRequestId?: number,
  draftInputOptions?: {
    draftPetToLodging: {
      [petId: string]: PetToLodging;
    };
    onUpdateDraftToLodging?: (petToLodging: PetToLodging) => void;
    draftPetToEvaluation?: {
      [petId: string]: string;
    };
    onUpdateDraftToEvaluation?: (petId: string, evaluationId: string) => void;
    draftEvaluationPetToStaff: {
      [petId: string]: {
        petId: string;
        staffId: string;
      };
    };
    onUpdateEvaluationPetToStaff: (petId: string, staffId: string | undefined) => void;
  },
) => {
  const [business, request, petTypeMap, currentAccountId, onlyBDCareTypes] = useSelector(
    selectCurrentBusiness,
    onlineBookingLatestRequestMapBox.mustGetItem(bookingRequestId ?? ID_ANONYMOUS),
    petTypeMapBox,
    currentAccountIdBox,
    selectSceneCareType(Scene.OnlyBD),
  );
  const questionList = useGetQuestionList(OnlineBookingQuestionCategory.ForPet, petOmitKeyList);
  const fmtFeeding = useFeedingInstructions();
  const fmtMedication = useMedicationInstructions();

  const {
    pets = [],
    services = [],
    feedings = [],
    medications = [],
    serviceItemTypes,
    appointmentDate,
    appointmentEndDate,
    specificDates,
    isGroupClass,
    isHybridEvaluationRequest,
    noStartTime,
  } = request;
  const specificDayJsDates = specificDates?.map((date) => dayjs(date));
  const maxSpecificDate = specificDayJsDates?.reduce((prev, curr) => (prev.isAfter(curr) ? prev : curr), dayjs());
  const minSpecificDate = specificDayJsDates?.reduce((prev, curr) => (prev.isBefore(curr) ? prev : curr), dayjs());
  const availableRange = {
    startDate: appointmentDate ? dayjs(appointmentDate) : (minSpecificDate ?? dayjs()),
    endDate: appointmentEndDate ? dayjs(appointmentEndDate) : (maxSpecificDate ?? dayjs()),
  };

  const { isMultipleEvaluationMode } = useBusinessApplicableEvaluation();
  const originEvaluationId = request.services?.[0]?.evaluationId;

  const { buildAddOnDescList, buildServiceDescList } = useBuildServiceAddOnDesc(bookingRequestId);

  const isBoardingRequest = serviceItemTypes?.includes(ServiceItemType.BOARDING);
  return useMemo(
    () =>
      pets.map((petInfo) => {
        const {
          petName,
          avatarPath,
          petTypeId,
          breed,
          fixed,
          birthday,
          gender,
          hairLength,
          weight,
          vetName,
          vetPhoneNumber,
          emergencyContactName,
          emergencyContactPhone,
          vaccineList,
          healthIssues,
          behavior,
          vetAddress,
          questionAnswerList,
          serviceId,
          evaluationId,
          petId,
        } = petInfo;
        const origServiceList = services.filter(
          (s) => s.serviceType === ServiceType.Service && Number(s.petId) === petId,
        );
        const petMainService = origServiceList.find((s) =>
          s.serviceId && s.serviceItemType === ServiceItemType.EVALUATION
            ? s.evaluationId === evaluationId
            : +s.serviceId === serviceId,
        )!;
        const {
          serviceItemType,
          startDate = '',
          startTime = 0,
          duration = 0,
          endDate = '',
          endTime = 0,
          specificDates: petMainServiceSpecificDates,
        } = petMainService || {};

        const isEvaluation = serviceItemType === ServiceItemType.EVALUATION;
        const isShowEvaluationSelect = isEvaluation && isMultipleEvaluationMode;

        const showLodging = serviceItemType && onlyBDCareTypes.includes(serviceItemType);

        const serviceList = [
          petMainService,
          ...origServiceList.filter((s) => s.serviceId && +s.serviceId !== serviceId),
        ].filter(Boolean);
        const addons = request?.addons?.filter((item) => Number(item?.petId) === petId);

        const feedingList = fmtFeeding(
          feedings
            ?.filter((feed) => isNormal(feed.id))
            /**
             * 过滤出与当前 pet 相关的 feed
             */
            .filter((feed) => serviceList.some((service) => service.serviceDetailId === feed.serviceDetailId))
            .map(transformOBFeedingDetailToApptFeedingDetail),
        );
        const medicationList = fmtMedication(
          medications
            ?.filter((med) => isNormal(med.id))
            /**
             * 过滤出与当前 pet 相关的 medication
             */
            .filter((med) => serviceList.some((service) => service.serviceDetailId === med.serviceDetailId))
            .map(transformMedicationDetailToApptMedicationDetail),
          { showDate: serviceList.some((service) => service.serviceItemType === ServiceItemType.BOARDING) },
        );
        const {
          draftPetToLodging,
          onUpdateDraftToLodging,
          draftPetToEvaluation,
          onUpdateDraftToEvaluation,
          draftEvaluationPetToStaff,
          onUpdateEvaluationPetToStaff,
        } = draftInputOptions || {};
        const currentEvaluationId = draftPetToEvaluation?.[petId] || originEvaluationId;
        const currentEvaluationStaffId = Number(draftEvaluationPetToStaff?.[petId]?.staffId ?? 0);

        const enumQuestions: EnumQuestions = {
          petName,
          petImage: {
            className: '!moe-my-[35px]',
            renderValue: (onClick) => (
              <Avatar
                src={avatarPath}
                type={getPetAvatarType(petTypeId)}
                size="large"
                onClick={() => onClick({ visible: true, avatarPath, petTypeId })}
              />
            ),
          },
          appointmentDate: isHybridEvaluationRequest ? (
            <span className="moe-flex-1 moe-text-[#f15a2b] moe-whitespace-pre-line moe-min-w-[calc(100%-124px)">
              {appointmentDateTimeFormat(business, {
                appointmentDate: startDate,
                appointmentStartTime: startTime,
                noStartTime,
                appointmentEndDate: endDate,
                appointmentEndTime: endTime,
                specificDates: petMainServiceSpecificDates,
                serviceItemTypes: serviceItemType ? [serviceItemType] : [],
                services,
              })}
            </span>
          ) : null,
          services: !isShowEvaluationSelect ? (
            buildServiceDescList(serviceList).join('\n')
          ) : (
            <SelectRequestEvaluation
              value={currentEvaluationId}
              onChange={(evaluationId) => {
                onUpdateDraftToEvaluation?.(String(petId), evaluationId || '');
              }}
            />
          ),
          addons:
            !isEvaluation && !isGroupClass
              ? buildAddOnDescList(serviceItemType || ServiceItemType.GROOMING, addons || []).join('\n')
              : undefined,
          staff:
            isEvaluation && evaluationId ? (
              <SelectRequestEvaluationStaff
                appointmentDate={appointmentDate}
                evaluationId={evaluationId}
                serviceStartTime={startTime}
                serviceTime={duration}
                value={currentEvaluationStaffId}
                onChange={(staffId) => {
                  onUpdateEvaluationPetToStaff?.(String(petId), staffId ? String(staffId) : undefined);
                }}
              />
            ) : null,
          lodging: showLodging ? (
            <div className="moe-flex moe-gap-xs">
              <SelectRequestLodging
                petId={String(petId)}
                range={availableRange}
                serviceId={String(petMainService.serviceId)}
                value={draftPetToLodging?.[petId]?.lodgingUnitId}
                onChange={(id) => {
                  onUpdateDraftToLodging?.({ petId: String(petId), lodgingUnitId: id || '' });
                }}
                hideRemove={isBoardingRequest}
              />
            </div>
          ) : undefined,
          feedings: feedingList.length ? (
            <div className="moe-flex moe-flex-col moe-gap-[8px]">
              {feedingList.map((item) => (
                <div key={item}>{item}</div>
              ))}
            </div>
          ) : undefined,
          medications: medicationList.length ? (
            <div className="moe-flex moe-flex-col moe-gap-[8px]">
              {medicationList.map((item) => (
                <div key={item}>{item}</div>
              ))}
            </div>
          ) : undefined,
        };

        const dynamicQuestionsList = questionList.map(({ key, questionType, question }) => {
          const keyWithoutUnderscore = replaceUnderscoreWithSpace(key);
          switch (key) {
            case OBPetQuestionKey.PetType:
              return {
                className: '!moe-mt-[8px]',
                label: keyWithoutUnderscore,
                value: petTypeMap.mustGetItem(PetTypeRecord.ownId(petTypeId, currentAccountId)).typeName,
              };
            case OBPetQuestionKey.Breed:
              return { label: keyWithoutUnderscore, value: breed };
            case OBPetQuestionKey.Fixed:
              return { label: keyWithoutUnderscore, value: fixed };
            case OBPetQuestionKey.CoatType:
            case OBPetQuestionKey.HairLength:
              return { label: 'Coat type', value: hairLength };
            case OBPetQuestionKey.Birthday:
              return { label: keyWithoutUnderscore, value: business.formatFixedDate(birthday) };
            case OBPetQuestionKey.Gender:
              return { label: keyWithoutUnderscore, value: PetGender.mapLabels[+gender] };
            case OBPetQuestionKey.VaccineList:
              return {
                label: keyWithoutUnderscore,
                value: vaccineList ? () => <PetVaccine vaccineBindings={vaccineList} /> : null,
              };
            case OBPetQuestionKey.Weight:
              return { label: keyWithoutUnderscore, value: `${weight} ${business.unitOfWeight}` };
            case OBPetQuestionKey.VetName:
              return { label: keyWithoutUnderscore, value: vetName };
            case OBPetQuestionKey.VetPhone:
              return { label: 'Vet phone', value: vetPhoneNumber };
            case OBPetQuestionKey.EmergencyContact:
              return {
                label: keyWithoutUnderscore,
                value:
                  emergencyContactName && emergencyContactPhone
                    ? `${emergencyContactName} (${emergencyContactPhone})`
                    : emergencyContactName || emergencyContactPhone,
              };
            case OBPetQuestionKey.HealthIssues:
              return { label: keyWithoutUnderscore, value: healthIssues };
            case OBPetQuestionKey.Behavior:
              return { label: keyWithoutUnderscore, value: behavior };
            case OBPetQuestionKey.VetAddress:
              return { label: keyWithoutUnderscore, value: vetAddress };
            default: {
              const answer = questionAnswerList.find((item) => item.key === key)?.answer;
              if (questionType === OnlineBookingQuestionType.Checkbox) {
                return {
                  isCustom: true,
                  label: question,
                  value: jsonParse(answer, []).join('\n'),
                };
              }
              return { isCustom: true, label: question, value: answer };
            }
          }
        });

        return getLabelValueList(enumQuestions, dynamicQuestionsList);
      }),
    [business, request, petTypeMap, questionList, draftInputOptions],
  );
};
