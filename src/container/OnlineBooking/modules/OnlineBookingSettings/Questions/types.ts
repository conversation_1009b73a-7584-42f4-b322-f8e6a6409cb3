import { createEnum } from '../../../../../store/utils/createEnum';

export const QuestionTabType = createEnum({
  Client: ['Client', { needBDEnable: false }],
  Pet: ['Pet', { needBDEnable: false }],
  Service: ['Service', { needBDEnable: true }],
} as const);

export type QuestionTabKeyType = (typeof QuestionTabType.keys)[number];

export const TabDescriptionType = {
  Client:
    'Selected questions will appear when clients create or edit their profiles. The responses will be recorded in the client info for future use. When a question is in view-only setting, a response is not required.',
  Pet: 'Selected questions will be appeared when creating or editing pet profiles during online booking. The responses will be recorded in the pet profile for future use. When a question is in view-only setting, a response is not required.',
  Service:
    'Selected questions will be appeared when a client selects a relevant service. The responses will be saved in the corresponding appointment details.',
};
