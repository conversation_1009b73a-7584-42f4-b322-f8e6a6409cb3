import { Tabs } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useHistory } from 'react-router';
import { PATH_ONLINE_BOOKING_NEW_QUESTIONS } from '../../../../../router/paths';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { useRouteQueryV2 } from '../../../../../utils/RoutePath';
import { SettingsTitle } from '../components/SettingsTitle';
import { OnlineBookingNav } from '../types';
import { ClientQuestions } from './modules/ClientQuestions/ClientQuestions';
import { PetQuestions } from './modules/PetQuestions/PetQuestions';
import { ServiceQuestions } from './modules/ServiceQuestions';
import { type QuestionTabKeyType, QuestionTabType } from './types';

const TabsComponent = {
  [QuestionTabType.Client]: <ClientQuestions />,
  [QuestionTabType.Pet]: <PetQuestions />,
  [QuestionTabType.Service]: <ServiceQuestions />,
};

export const Questions = memo(function Questions() {
  const history = useHistory();
  const { panel = QuestionTabType.Client } = useRouteQueryV2(PATH_ONLINE_BOOKING_NEW_QUESTIONS);
  const [enableBD] = useSelector(selectBDFeatureEnable);

  const tabList = useMemo(() => {
    return QuestionTabType.keys
      .filter((key) => {
        return !(QuestionTabType.mapLabels[key].needBDEnable && !enableBD);
      })
      .map((key) => {
        return {
          key,
          label: key,
          component: TabsComponent[key],
        };
      });
  }, [enableBD]);

  return (
    <div className="moe-w-full moe-max-w-[1140px]">
      <SettingsTitle title={OnlineBookingNav.Questions} />
      <div className="moe-flex moe-flex-col moe-gap-y-xl moe-w-full">
        <Tabs
          selectedKey={panel}
          onChange={(key) => {
            history.push(
              PATH_ONLINE_BOOKING_NEW_QUESTIONS.queried({
                panel: key as QuestionTabKeyType,
              }),
            );
          }}
        >
          {tabList.map((item) => {
            return (
              <Tabs.Item key={item.key} label={item.label}>
                {item.component}
              </Tabs.Item>
            );
          })}
        </Tabs>
      </div>
    </div>
  );
});
