import { Drawer, Form, Input, Switch, stringToNumber, useForm, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { isEqual } from 'lodash';
import React, { memo, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { setDraftQuestion } from '../../../../../../../../store/onlineBooking/actions/private/questions.actions';
import {
  OBPetQuestionKey,
  OnlineBookingQuestionCategory,
} from '../../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { useBool } from '../../../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import { useToggleIntercom } from '../../../../../../../../utils/hooks/useToggleIntercom';
import { unSavedDoubleConfirmV2 } from '../../../../../../../../utils/hooks/useUnsavedConfirmV2';
import { numberToBoolean } from '../../../../../../../../utils/inputTransformer';
import { useQuestion } from '../../../hooks/useQuestion';
import { type CommonQuestionProps } from '../CommonQuestion';
import { type DynamicQuestion } from './types';
import { PetCommonQuestion } from '../../../modules/PetQuestions/PetQuestionItem/PetCommonQuestion';

export interface QuestionPetWeightProps extends CommonQuestionProps {}

interface FormValues {
  weightLimitNotify: number;
  weightLimit: string;
  overLimitTips: string;
}

export const QuestionPetWeight: DynamicQuestion = memo(function QuestionPetWeight(props: QuestionPetWeightProps) {
  const { questionId } = props;
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness);
  const showDrawer = useBool(false);
  useToggleIntercom(showDrawer.value);
  const question = useQuestion(questionId);
  const form = useForm<FormValues>({
    mode: 'all',
    defaultValues: {
      weightLimitNotify: question?.petWeightLimitNotify,
      weightLimit: question?.petWeightLimit,
      overLimitTips: question?.petWeightOverTips,
    },
  });
  const [weightLimitNotify, weightLimit, overLimitTips] = useWatch({
    control: form.control,
    name: ['weightLimitNotify', 'weightLimit', 'overLimitTips'],
  });
  const handleShowDrawer = useLatestCallback(() => {
    form.reset(
      {
        weightLimitNotify: question?.petWeightLimitNotify,
        weightLimit: question?.petWeightLimit,
        overLimitTips: question?.petWeightOverTips,
      },
      { keepDirty: false },
    );
    showDrawer.open();
  });

  // const { isDirty: originDirty, dirtyFields } = form.formState;

  const isDirty = useMemo(() => {
    const compareA = {
      weightLimit: question?.petWeightLimit,
      weightLimitNotify: question?.petWeightLimitNotify,
      overLimitTips: question?.petWeightOverTips,
    };
    const compareB = {
      weightLimit,
      weightLimitNotify,
      overLimitTips,
    };
    return !isEqual(compareA, compareB);
  }, [weightLimitNotify, weightLimit, overLimitTips, question]);

  // isDisabled 的时候 dirty 有点问题
  // const isDirty = originDirty && dirtyFields && Object.keys(dirtyFields).length > 0;

  const onConfirm = async () => {
    await form.handleSubmit((values) => {
      const { weightLimit, weightLimitNotify, overLimitTips } = values;
      dispatch(
        setDraftQuestion(questionId, {
          petWeightLimitNotify: weightLimitNotify,
          petWeightLimit: weightLimit,
          petWeightOverTips: overLimitTips,
        }),
      );
    })();
    form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
    setTimeout(() => {
      showDrawer.close();
    }, 0);
  };

  const handleDiscardChanges = () => {
    form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
    showDrawer.close();
  };

  const handleClose = useLatestCallback(async () => {
    if (isDirty) {
      const needClose = await unSavedDoubleConfirmV2(onConfirm, handleDiscardChanges);
      if (!needClose) {
        return;
      }
    }
    showDrawer.close();
  });

  return (
    <>
      <PetCommonQuestion
        questionId={questionId}
        type={OnlineBookingQuestionCategory.ForPet}
        isEditable
        onEdit={handleShowDrawer}
      />
      <Drawer
        size="s"
        isOpen={showDrawer.value}
        onClose={handleClose}
        title="Weight limit"
        autoCloseOnConfirm={false}
        onConfirm={onConfirm}
      >
        <Form form={form} footer={null}>
          <Form.Item name="weightLimitNotify" label="Weight limit" transformer={numberToBoolean}>
            <Switch isSelected={weightLimitNotify === 1} isRequired>
              {weightLimitNotify === 1 ? 'Has' : 'No'} limit
            </Switch>
          </Form.Item>
          <Form.Item name="weightLimit" label="Max weight" transformer={stringToNumber}>
            <Input.Number suffix={business.unitOfWeight} isDisabled={!weightLimitNotify} minValue={0}></Input.Number>
          </Form.Item>
          <Form.Item name="overLimitTips" label="Notice message">
            <Input.TextArea isDisabled={!weightLimitNotify} />
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
});

QuestionPetWeight.questionName = 'Weight';

QuestionPetWeight.questionKey = OBPetQuestionKey.Weight;
