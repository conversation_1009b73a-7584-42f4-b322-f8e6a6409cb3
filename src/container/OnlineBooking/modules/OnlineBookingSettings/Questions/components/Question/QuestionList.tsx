import { Empty } from '@moego/ui';
import React, { memo } from 'react';
import { SortableList } from '../../../../../../../components/SortableList/SortableList';
import { Switch } from '../../../../../../../components/SwitchCase';
import { type OnlineBookingQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { isServiceQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.utils';
import { useCustomQuestionEdit } from '../../hooks/useCustomQuestionEdit';
import { useQuestionEditList } from '../../hooks/useQuestionEditList';
import { useQuestionSortMove } from '../../hooks/useQuestionSortMove';
import { TargetQuestion } from './SpecialQuestions/TargetQuestion';

export interface QuestionListProps {
  type: OnlineBookingQuestionCategory;
  onSelectQuestions?: () => void;
}

export const QuestionList = memo(function QuestionList(props: QuestionListProps) {
  const { type, onSelectQuestions } = props;
  const { additionalList, preservedList } = useQuestionEditList(type);
  const handleMove = useQuestionSortMove(type);
  const { renderCustomQuestionModal, handleEditCustomQuestion, handleRemoveCustomQuestion } = useCustomQuestionEdit({
    type,
  });
  const isEmptyList = preservedList.length === 0 && additionalList.length === 0;
  return (
    <Switch shortCircuit>
      <Switch.Case if={!isEmptyList}>
        <div>
          {preservedList.map((questionId) => {
            return <TargetQuestion key={questionId} type={type} questionId={questionId} />;
          })}
          <SortableList
            items={additionalList.map((id) => ({ id }))}
            onChange={(_items, formIndex, toIndex) => {
              handleMove(formIndex, toIndex);
            }}
            renderItem={(item) => {
              return (
                <SortableList.Item id={item.id}>
                  <TargetQuestion
                    key={item.id}
                    type={type}
                    questionId={item.id}
                    onEdit={handleEditCustomQuestion}
                    onRemoveCustomQuestion={handleRemoveCustomQuestion}
                  />
                </SortableList.Item>
              );
            }}
          />
          {renderCustomQuestionModal()}
        </div>
      </Switch.Case>
      <Switch.Case else>
        <Empty
          className="moe-bg-neutral-sunken-0 moe-py-[24px] moe-rounded-m"
          title=""
          illustration={null}
          description="No questions has been set up yet"
          actionButtonVariant="tertiary"
          actionText={isServiceQuestionCategory(type) ? 'Select questions' : 'Add question'}
          actionButtonProps={{
            size: 'l',
          }}
          onAction={onSelectQuestions}
        />
      </Switch.Case>
    </Switch>
  );
});
