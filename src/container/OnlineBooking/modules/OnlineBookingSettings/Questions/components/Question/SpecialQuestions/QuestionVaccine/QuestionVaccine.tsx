import { Checkbox, CheckboxGroup, Drawer, Form, Radio, RadioGroup, useForm, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { isEqual } from 'lodash';
import React, { memo, useEffect, useMemo, useRef } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../../../../../../components/Condition';
import { toastApi } from '../../../../../../../../../components/Toast/Toast';
import { setDraftQuestion } from '../../../../../../../../../store/onlineBooking/actions/private/questions.actions';
import {
  OBPetQuestionKey,
  OnlineBookingQuestionCategory,
} from '../../../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { selectDraftVaccineQuestionFields } from '../../../../../../../../../store/onlineBooking/settings/questions.selectors';
import { petVaccineMapBox } from '../../../../../../../../../store/pet/petVaccine.boxes';
import { selectBusinessPetVaccines } from '../../../../../../../../../store/pet/petVaccine.selectors';
import { NormalBool } from '../../../../../../../../../store/utils/createEnum';
import { ID_ANONYMOUS } from '../../../../../../../../../store/utils/identifier';
import { truly } from '../../../../../../../../../store/utils/utils';
import { useBool } from '../../../../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { useToggleIntercom } from '../../../../../../../../../utils/hooks/useToggleIntercom';
import { useUnsavedConfirmV2 } from '../../../../../../../../../utils/hooks/useUnsavedConfirmV2';
import { useQuestion } from '../../../../hooks/useQuestion';
import { useSetQuestionState } from '../../../../hooks/useSetQuestionState';
import { type CommonQuestionProps } from '../../CommonQuestion';
import { SelectVaccine } from '../SelectVaccine';
import { type DynamicQuestion } from '../types';
import { VaccineSettingsInfo } from './VaccineSettingsInfo';
import { useVaccineSettingsInfoData } from './useVaccineSettingsInfoData';
import { PetCommonQuestion } from '../../../../modules/PetQuestions/PetQuestionItem/PetCommonQuestion';
import { QuestionTableV2ExtraQuestionBox } from '../../../QuestionTableV2/QuestionTableV2ExtraQuestionBox';

export interface QuestionVaccineProps extends CommonQuestionProps {}

interface QuestionVaccineState {
  vaccineType: 'specific' | 'settings';
  vaccineList: number[];
}

export type VaccineFieldsType = Array<
  'showExpirationDate' | 'requireExpirationDate' | 'showVaccineDocument' | 'requireVaccineDocument'
>;

export const QuestionVaccine: DynamicQuestion = memo(function QuestionVaccine(props: QuestionVaccineProps) {
  const { questionId } = props;
  const dispatch = useDispatch();

  const question = useQuestion(questionId);
  const setQuestionState = useSetQuestionState(questionId);
  const showDrawer = useBool(false);
  const [petVaccineMap, draftVaccineQuestionFields, settingVaccineList] = useSelector(
    petVaccineMapBox,
    selectDraftVaccineQuestionFields(),
    selectBusinessPetVaccines(),
  );

  useToggleIntercom(showDrawer.value);

  const { extraInfo } = question;
  const { basedOnVaccineSettings, specificVaccineIds } = extraInfo.vaccineTypeInfo;
  const specificVaccineList = specificVaccineIds
    .map((id) => {
      const petVaccine = petVaccineMap.mustGetItem(+id);
      return {
        label: petVaccine.name,
        value: petVaccine.id,
      };
    })
    .filter((i) => i.value !== ID_ANONYMOUS);
  /**
   * 需注意 specific vaccine 在 basedOnVaccineSettings 为 true 时值仍然保留，
   * https://moegoworkspace.slack.com/archives/C06AXC3T4CB/p1729586316020089
   */
  const hasVaccineList = specificVaccineList.length > 0;
  const requiredSettingVaccineList = useMemo(() => {
    return settingVaccineList
      .map((item) => petVaccineMap.mustGetItem(item).toJSON())
      .toJSON()
      .filter((item) => item.isRequiredForScheduling);
  }, [settingVaccineList, petVaccineMap]);
  const hasRequiredSettingVaccineList = requiredSettingVaccineList.length > 0;

  const [state, setState] = useSetState<QuestionVaccineState>({
    vaccineType: basedOnVaccineSettings ? 'settings' : 'specific',
    vaccineList: [],
  });

  const getDefaultState = useLatestCallback(() => {
    const vaccineList = [...specificVaccineIds].map(Number);
    return {
      vaccineType: (basedOnVaccineSettings ? 'settings' : 'specific') as QuestionVaccineState['vaccineType'],
      vaccineList,
    };
  });

  const isDirty = useMemo(() => {
    const compareA = { ...state };
    const compareB = getDefaultState();
    // ignore sort
    compareA.vaccineList.sort();
    compareB.vaccineList.sort();
    return !isEqual(compareA, compareB);
  }, [state, getDefaultState]);

  const isValidationOpen = useBool(false);
  const checkValid = useLatestCallback(() => {
    return !(state.vaccineType === 'specific' && state.vaccineList.length === 0);
  });

  // 是否是字段必填模式，如果外层是必填模式，则这里需要两个字段至少一个必填
  const requiredMode = useRef<boolean>(false);
  const form = useForm<{ vaccineFields: VaccineFieldsType }>({
    mode: 'all',
    defaultValues: {
      vaccineFields: (
        [
          draftVaccineQuestionFields.showExpirationDate && 'showExpirationDate',
          draftVaccineQuestionFields.requireExpirationDate && 'requireExpirationDate',
          draftVaccineQuestionFields.showVaccineDocument && 'showVaccineDocument',
          draftVaccineQuestionFields.requireVaccineDocument && 'requireVaccineDocument',
        ] as const
      ).filter(truly),
    },
  });

  const [vaccineFields] = useWatch({
    control: form.control,
    name: ['vaccineFields'],
  });

  const onConfirm = async () => {
    isValidationOpen.open();
    if (!checkValid()) return;
    await form.handleSubmit((data) => {
      const { vaccineType, vaccineList } = state;
      dispatch([
        setDraftQuestion(questionId, {
          extraInfo: extraInfo.updateVaccineTypeInfo({
            specificVaccineIds: vaccineList,
            basedOnVaccineSettings: vaccineType === 'settings',
            showExpirationDate: data.vaccineFields.includes('showExpirationDate'),
            requireExpirationDate: data.vaccineFields.includes('requireExpirationDate'),
            showVaccineDocument: data.vaccineFields.includes('showVaccineDocument'),
            requireVaccineDocument: data.vaccineFields.includes('requireVaccineDocument'),
          }),
        }),
      ]);
      if (requiredMode.current) {
        setQuestionState({
          isRequired: NormalBool.get(true),
        });
      }
      form.reset({ vaccineFields: data.vaccineFields });
      showDrawer.close();
      requiredMode.current = false;
    })();
  };

  const closeDrawer = useLatestCallback(() => {
    form.reset();
    showDrawer.close();
    requiredMode.current = false;
  });

  const handleClose = useUnsavedConfirmV2(isDirty, closeDrawer, onConfirm);
  const handleShowDrawer = () => {
    if (question.isRequired) {
      requiredMode.current = true;
    }
    showDrawer.open();
  };

  const { getData } = useVaccineSettingsInfoData();
  useEffect(() => {
    setState(getDefaultState());
    getData();
  }, []);

  const isNeedAtLeastOneRequired = (vaccineFields: VaccineFieldsType) => {
    const requireExpirationDate =
      vaccineFields.includes('showExpirationDate') && vaccineFields.includes('requireExpirationDate');
    const requireVaccineDocument =
      vaccineFields.includes('showVaccineDocument') && vaccineFields.includes('requireVaccineDocument');

    return !requireExpirationDate && !requireVaccineDocument;
  };

  const shouldStopBeforeHandleRequired = useLatestCallback(() => {
    const { vaccineFields } = form.getValues();
    if (isNeedAtLeastOneRequired(vaccineFields)) {
      toastApi.neutral('Please select at least one vaccine field to be required.');
      requiredMode.current = true;
      handleShowDrawer();
      requestAnimationFrame(() => {
        form.trigger(['vaccineFields']);
      });
      return true;
    }
    return false;
  });

  return (
    <>
      <PetCommonQuestion
        type={OnlineBookingQuestionCategory.ForPet}
        questionId={questionId}
        shouldStopBeforeHandleRequired={shouldStopBeforeHandleRequired}
        isEditable
        onEdit={handleShowDrawer}
        extra={
          <QuestionTableV2ExtraQuestionBox className="moe-mt-[16px] moe-flex moe-flex-col moe-gap-[8px]">
            <Condition if={!basedOnVaccineSettings && hasVaccineList}>
              <div className="moe-text-sm-20 moe-text-secondary moe-font-regular">
                Specific vaccine: {specificVaccineList.map((i) => i.label).join(', ')}
              </div>
            </Condition>
            <Condition if={basedOnVaccineSettings && hasRequiredSettingVaccineList}>
              <div className="moe-text-sm-20 moe-text-secondary moe-font-regular">
                {requiredSettingVaccineList.map((i) => i.name).join(', ')}
              </div>
            </Condition>
          </QuestionTableV2ExtraQuestionBox>
        }
      />
      <Drawer
        isOpen={showDrawer.value}
        title="Vaccine requirement"
        size="m"
        onClose={handleClose}
        onConfirm={onConfirm}
        autoCloseOnConfirm={false}
      >
        <div className="moe-flex moe-flex-col moe-gap-y-m">
          <div className="moe-flex moe-flex-col moe-gap-s">
            <RadioGroup
              label="Vaccine type"
              value={state.vaccineType}
              onChange={(val) => setState({ vaccineType: val })}
            >
              <Radio value={'specific'}>Specific vaccine</Radio>
              <Condition if={state.vaccineType === 'specific'}>
                <div className="moe-ml-[29px]">
                  <SelectVaccine
                    isInvalid={isValidationOpen.value && !checkValid()}
                    errorMessage={isValidationOpen.value && !checkValid() ? 'Please select at least one vaccine' : ''}
                    value={state.vaccineList}
                    onChange={(e) => setState({ vaccineList: e })}
                    isDisabled={false}
                  />
                </div>
              </Condition>
              <Radio value={'settings'}>Based on vaccine settings</Radio>
            </RadioGroup>
            <Condition if={state.vaccineType === 'settings'}>
              <VaccineSettingsInfo />
            </Condition>
          </div>
          <div className="moe-flex moe-flex-col moe-gap-s">
            <Form form={form} footer={null}>
              <Form.Item
                label="Vaccine fields"
                name="vaccineFields"
                rules={{
                  required: 'Please select at least one field.',
                  validate: (value) => {
                    if (requiredMode.current && isNeedAtLeastOneRequired(value)) {
                      return 'Since the entire vaccine is marked as required, you must set at least one required field in either the expiration date or vaccine document.';
                    }
                    if (
                      !value.length ||
                      (!value.includes('showExpirationDate') && !value.includes('showVaccineDocument'))
                    ) {
                      return 'Please select at least one field.';
                    }
                    return true;
                  },
                }}
              >
                <CheckboxGroup isRequired>
                  <Checkbox value="showExpirationDate">Vaccine expiration date</Checkbox>
                  <Condition if={vaccineFields.includes('showExpirationDate')}>
                    <Checkbox value="requireExpirationDate" className="moe-ml-[28px]">
                      Required
                    </Checkbox>
                  </Condition>
                  <Checkbox value="showVaccineDocument">Vaccine document</Checkbox>

                  <Condition if={vaccineFields.includes('showVaccineDocument')}>
                    <Checkbox value="requireVaccineDocument" className="moe-ml-[28px]">
                      Required
                    </Checkbox>
                  </Condition>
                </CheckboxGroup>
              </Form.Item>
            </Form>
          </div>
        </div>
      </Drawer>
    </>
  );
});

QuestionVaccine.questionName = 'Vaccine';

QuestionVaccine.questionKey = OBPetQuestionKey.VaccineList;
