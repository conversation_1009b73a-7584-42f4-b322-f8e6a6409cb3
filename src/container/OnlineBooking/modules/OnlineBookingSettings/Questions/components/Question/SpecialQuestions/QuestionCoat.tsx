import { Drawer } from '@moego/ui';
import { useDispatch } from 'amos';
import { Form } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { isEqual } from 'lodash';
import React, { memo, useState } from 'react';
import { type UploadStatus as LegacyUploadStatus } from '../../../../../../../../components/Upload/Upload';
import { setDraftQuestion } from '../../../../../../../../store/onlineBooking/actions/private/questions.actions';
import {
  OBPetQuestionKey,
  OnlineBookingQuestionCategory,
} from '../../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { OBQuestionExtraRecord } from '../../../../../../../../store/onlineBooking/models/OBQuestionExtra';
import { useBool } from '../../../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import { unSavedDoubleConfirmV2 } from '../../../../../../../../utils/hooks/useUnsavedConfirmV2';
import { useQuestion } from '../../../hooks/useQuestion';
import { ReferencePictureUpload } from './ReferencePictureUpload';
import { type DynamicQuestion } from './types';
import { PetCommonQuestion } from '../../../modules/PetQuestions/PetQuestionItem/PetCommonQuestion';

export interface QuestionCoatProps {
  questionId: number;
}

const FormItem = Form.Item;

interface FormValues {
  referencePicture?: string;
}

export const QuestionCoat: DynamicQuestion = memo(function QuestionCoat(props: QuestionCoatProps) {
  const { questionId } = props;
  const dispatch = useDispatch();
  const showDrawer = useBool(false);
  const question = useQuestion(questionId);
  // 用老的 form 主要是来不及改 dirty 逻辑了，还有上传组件也还是用的旧的，先不动了
  // 后续再改
  const [form] = useForm<FormValues>();

  // 避免 uploading 未完成时 confirm
  const [uploadStatus, setUploadStatus] = useState<LegacyUploadStatus | undefined>();

  const checkIsDirty = useLatestCallback(() => {
    const origin = question.extraInfo.coatTypeInfo?.referencePicture;
    return !isEqual(form.getFieldsValue()?.referencePicture, origin);
  });

  const onConfirm = useLatestCallback(() => {
    const { referencePicture } = form.getFieldsValue();
    // 如果删掉就是 undefined
    dispatch(
      setDraftQuestion(question.id, {
        extraInfo: OBQuestionExtraRecord.fromCoatTypeInfo({
          referencePicture,
        }),
      }),
    );
    // 不这样会触发 dirty check，导致弹窗
    setTimeout(() => {
      showDrawer.close();
    }, 0);
  });

  const handleDiscardChanges = () => {
    form.resetFields();
    showDrawer.close();
  };

  const handleClose = async () => {
    if (checkIsDirty()) {
      const needClose = await unSavedDoubleConfirmV2(onConfirm, handleDiscardChanges);
      if (!needClose) {
        return;
      }
    }
    showDrawer.close();
  };

  const handleShowDrawer = () => {
    form.setFieldsValue({
      referencePicture: question.extraInfo.coatTypeInfo?.referencePicture,
    });
    // 不这样会触发 dirty check
    setTimeout(() => {
      showDrawer.open();
    }, 0);
  };

  return (
    <>
      <PetCommonQuestion
        questionId={questionId}
        type={OnlineBookingQuestionCategory.ForPet}
        isEditable
        onEdit={handleShowDrawer}
      />
      <Drawer
        size="s"
        title={
          <div>
            Reference sample
            <p className="!moe-text-xs moe-text-tertiary moe-font-regular moe-mt-[4px]">
              Help your client to select the right coat type.
            </p>
          </div>
        }
        className="online-booking-drawer"
        isOpen={showDrawer.value}
        onClose={handleClose}
        onConfirm={onConfirm}
        confirmButtonProps={{
          isDisabled: uploadStatus === 'uploading',
        }}
      >
        <Form form={form} style={{ display: 'flex', flex: 1, flexDirection: 'column' }}>
          <div className="moe-flex-1">
            <FormItem noStyle name="referencePicture">
              <ReferencePictureUpload onUploadStatusChange={(status) => setUploadStatus(status)} />
            </FormItem>
          </div>
        </Form>
      </Drawer>
    </>
  );
});

QuestionCoat.questionName = 'Coat type';

QuestionCoat.questionKey = OBPetQuestionKey.CoatType;
