import {
  type AcceptCustomerType,
  type AcceptPetEntryType,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import React, { memo } from 'react';
import { type OnlineBookingQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { NormalBool } from '../../../../../../../store/utils/createEnum';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useQuestion } from '../../hooks/useQuestion';
import { useQuestionRemove } from '../../hooks/useQuestionRemove';
import { useSetQuestionState } from '../../hooks/useSetQuestionState';
import { QuestionBasedRow } from './QuestionBasedRow';

export interface CommonQuestionProps {
  type: OnlineBookingQuestionCategory;
  questionId: number;
  onEdit?: (id: number) => void;
  onRemoveCustomQuestion?: (id: number) => void;
  /**
   * 额外信息展示
   */
  extra?: React.ReactNode;
  /**
   * 处理是否必填
   * @param required 是否必填
   * @returns 是否需要阻断变更
   */
  shouldStopBeforeHandleRequired?: (required: boolean) => boolean;
}

export const CommonQuestion = memo(function CommonQuestion(props: CommonQuestionProps) {
  const { questionId, onEdit, onRemoveCustomQuestion, extra, type } = props;
  const question = useQuestion(questionId);
  const onRemoveSystemQuestion = useQuestionRemove(questionId);
  const handleRemove = useLatestCallback(() => {
    if (question.isCustomQuestion) {
      onRemoveCustomQuestion?.(questionId);
    } else {
      onRemoveSystemQuestion();
    }
  });
  const setQuestionState = useSetQuestionState(questionId);
  const handleRequired = (e: boolean) => {
    if (props.shouldStopBeforeHandleRequired && NormalBool.get(e)) {
      const shouldStop = props.shouldStopBeforeHandleRequired(e);
      if (shouldStop) {
        return;
      }
    }

    setQuestionState({ isRequired: NormalBool.get(e) });
  };
  const handleAcceptCustomerTypeChange = (acceptCustomerType: AcceptCustomerType) =>
    setQuestionState({ acceptCustomerType });
  const handleAcceptPetEntryTypeChange = (acceptPetEntryType: AcceptPetEntryType) =>
    setQuestionState({ acceptPetEntryType });
  return (
    <QuestionBasedRow
      id={questionId}
      isDisabled={question.disabled}
      label={question.question}
      isDraggable={!question.isPreservedQuestion(type)}
      isRequired={NormalBool.truly(question.isRequired)}
      isRemovable={!question.disabled}
      onRemove={handleRemove}
      acceptCustomerType={question.acceptCustomerType}
      acceptPetEntryType={question.acceptPetEntryType}
      hasAcceptCustomerType={question.isAcceptCustomerTypeAvailable}
      hasAcceptPetEntryType={question.isAcceptPetEntryTypeAvailable}
      onAcceptCustomerTypeChange={handleAcceptCustomerTypeChange}
      onAcceptPetEntryTypeChange={handleAcceptPetEntryTypeChange}
      isEditable={question.isCustomQuestion}
      onRequired={handleRequired}
      onEdit={onEdit}
      extra={extra}
    />
  );
});
