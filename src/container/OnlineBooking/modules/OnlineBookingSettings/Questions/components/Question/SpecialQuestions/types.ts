import {
  type OBClientQuestionKey,
  type OBPetQuestionKey,
} from '../../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { type CommonQuestionProps } from '../CommonQuestion';

export interface DynamicQuestion<T = CommonQuestionProps> extends React.FunctionComponent<T> {
  /**
   * @deprecated use `questionKey` instead. `questionKey` is now required for all new implementations.
   *
   * The rationale is to use a unified key (`questionKey`) to identify a question. Using `questionName` introduces dependencies on question names, which reduces maintainability.
   */
  questionName?:
    | 'Pet name'
    | 'Pet type'
    | 'Pet breed'
    | 'Weight'
    | 'Vaccine'
    | 'Agreement'
    | 'Coat type'
    | 'Feeding schedule'
    | 'Vet info'
    | 'Medication schedule';

  questionKey?: OBPetQuestionKey | OBClientQuestionKey;
}
