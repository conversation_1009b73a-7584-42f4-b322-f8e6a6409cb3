import { Alert, Modal, useForm } from '@moego/ui';
import { useDispatch } from 'amos';
import { pick } from 'lodash';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import {
  addNewCustomQuestion,
  removeDraftQuestion,
  setDraftQuestion,
} from '../../../../../../../store/onlineBooking/actions/private/questions.actions';
import { type OnlineBookingQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { OBQuestionExtraRecord } from '../../../../../../../store/onlineBooking/models/OBQuestionExtra';
import { ADD_QUESTION_ID } from '../../../../../../../store/onlineBooking/settings/questions.boxes';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { useQuestion } from '../../hooks/useQuestion';
import { CustomQuestionForm } from './CustomQuestionForm/CustomQuestionForm';

export interface CustomQuestionModalProps {
  id: number;
  type: OnlineBookingQuestionCategory;
  isOpen: boolean;
  onClose: () => void;
}

export const CustomQuestionModal = memo(function CustomQuestionModal(props: CustomQuestionModalProps) {
  const { id, isOpen, onClose, type } = props;
  const dispatch = useDispatch();
  const isEdit = id !== ADD_QUESTION_ID;
  const question = useQuestion(id || ADD_QUESTION_ID);
  const form = useForm<CustomQuestionForm>({
    mode: 'all',
  });
  // 取消的话，需要还原数据。如果是新增，则去掉新增的数据，如果是编辑，则还原数据
  const handleCancel = useLatestCallback(() => {
    if (!isEdit) {
      dispatch(removeDraftQuestion(ADD_QUESTION_ID, type));
    }
    onClose();
  });

  const handleConfirm = useLatestCallback(async () => {
    await form.handleSubmit(({ candidateValues, ...others }) => {
      const values = {
        ...others,
        extraInfo: OBQuestionExtraRecord.fromCandidateValues(candidateValues),
      };
      if (isEdit) {
        dispatch(setDraftQuestion(id, values));
      } else {
        dispatch(addNewCustomQuestion(values, type));
      }
      onClose();
    })();
  });

  useEffect(() => {
    if (question || isOpen) {
      form.reset(
        {
          ...pick(question.toJSON(), ['question', 'questionType', 'placeholder', 'isRequired', 'id']),
          candidateValues: question.extraInfo.candidateValues,
        },
        { keepDefaultValues: false, keepDirty: false },
      );
    }
  }, [question, isOpen]);

  return (
    <Modal
      size="s"
      title={isEdit ? 'Edit customized question' : 'Add customized question'}
      isOpen={isOpen}
      onClose={handleCancel}
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      autoCloseOnConfirm={false}
      autoCloseOnTertiary={false}
      isDismissable={true}
    >
      <Condition if={isOpen}>
        <div className="moe-flex moe-flex-col moe-gap-y-m">
          <Alert
            color="information"
            isCloseable
            isBordered
            description="Customized questions are currently only accessible in the booking flow."
          />
          <CustomQuestionForm form={form} type={type} />
        </div>
      </Condition>
    </Modal>
  );
});
