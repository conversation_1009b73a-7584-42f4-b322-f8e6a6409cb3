import {
  type AcceptCustomerType,
  type AcceptPetEntryType,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { MinorChevronDownOutlined, MinorChevronUpOutlined } from '@moego/icons-react';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { Switch } from '../../../../../../../components/SwitchCase';
import { type OnlineBookingQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { NormalBool } from '../../../../../../../store/utils/createEnum';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { useQuestion } from '../../hooks/useQuestion';
import { useQuestionRemove } from '../../hooks/useQuestionRemove';
import { useSetQuestionState } from '../../hooks/useSetQuestionState';
import { QuestionBasedRow } from './QuestionBasedRow';

export interface HasMoreQuestionProps {
  type: OnlineBookingQuestionCategory;
  questionId: number;
  onEdit?: (id: number) => void;
  /**
   * 额外信息展示
   */
  extra?: React.ReactNode;
  /**
   * 统一的 required
   */
  onRequired?: (e: boolean) => void;
}

export const HasMoreQuestion = memo(function HasMoreQuestion(props: HasMoreQuestionProps) {
  const { questionId, onEdit, extra, type, onRequired } = props;
  const question = useQuestion(questionId);
  const showMoreQuestion = useBool();
  const onRemove = useQuestionRemove(questionId);
  const setQuestionState = useSetQuestionState(questionId);
  const handleRequired = onRequired ? onRequired : (e: boolean) => setQuestionState({ isRequired: NormalBool.get(e) });
  const handleAcceptCustomerTypeChange = (acceptCustomerType: AcceptCustomerType) =>
    setQuestionState({ acceptCustomerType });
  const handleAcceptPetEntryTypeChange = (acceptPetEntryType: AcceptPetEntryType) =>
    setQuestionState({ acceptPetEntryType });

  const extraInfo = question.extraInfo;
  const visibleSubQuestions = extraInfo.subQuestions.filter((q) => q.isShow);

  // 如果有子问题勾选的同时，又不是所有子问题都勾选了，那么展示 isIndeterminate 状态
  const isIndeterminate =
    visibleSubQuestions?.some((q) => !q.isRequired) &&
    visibleSubQuestions?.some((q) => q.isRequired) &&
    NormalBool.truly(question.isRequired);

  useEffect(() => {
    if (!question.isRequired) {
      setQuestionState({
        extraInfo: extraInfo.updateSubQuestions(
          visibleSubQuestions.map((item) => {
            return {
              ...item,
              isRequired: false,
            };
          }),
        ),
      });
    }
  }, [question.isRequired]);

  return (
    <QuestionBasedRow
      id={questionId}
      isDisabled={question.disabled}
      label={question.question}
      isDraggable={!question.isPreservedQuestion(type)}
      isIndeterminate={isIndeterminate}
      isRequired={NormalBool.truly(question.isRequired)}
      isRemovable={!question.disabled}
      onRemove={onRemove}
      isEditable={question.isCustomQuestion}
      onRequired={handleRequired}
      onEdit={onEdit}
      acceptCustomerType={question.acceptCustomerType}
      acceptPetEntryType={question.acceptPetEntryType}
      hasAcceptCustomerType={question.isAcceptCustomerTypeAvailable}
      hasAcceptPetEntryType={question.isAcceptPetEntryTypeAvailable}
      isHasSubQuestion={visibleSubQuestions.length > 0}
      onAcceptCustomerTypeChange={handleAcceptCustomerTypeChange}
      onAcceptPetEntryTypeChange={handleAcceptPetEntryTypeChange}
      extra={showMoreQuestion.value ? extra : null}
      description={
        <Condition if={visibleSubQuestions.length}>
          <div
            className="moe-flex moe-items-center moe-text-sm-20 moe-text-tertiary moe-font-regular moe-cursor-pointer moe-mt-[4px]"
            onClick={showMoreQuestion.toggle}
          >
            <div>{`Question(${visibleSubQuestions.length})`}</div>
            <Switch shortCircuit>
              <Switch.Case if={showMoreQuestion.value}>
                <MinorChevronUpOutlined />
              </Switch.Case>
              <Switch.Case else>
                <MinorChevronDownOutlined />
              </Switch.Case>
            </Switch>
          </div>
        </Condition>
      }
    />
  );
});
