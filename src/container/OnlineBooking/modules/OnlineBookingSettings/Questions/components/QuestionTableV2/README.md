# QuestionTableV2

定位：针对 layout 和共性功能抽象的纯 UI 组件（业务逻辑 client / pet 各自封装）

背景是新的设计在 `QuestionTable` 的布局上有改造，但只覆盖了 Client 和 Pet Question，未覆盖 service question，所以这里 `QuestionTable` 增加一个新的 `QuestionTableV2` 系列组件。

核心组件：

1. `QuestionTableV2`：表格主 layout
2. `QuestionTableV2Heading`：表头组件
3. `QuestionTableV2CommonBasedRow`：表行组件
4. `QuestionTableV2ExtraQuestionBox`：主组件

列：配置参见 `QuestionTableV2.config.tsx` 文件，需要统一

共性的参数概念：`addingNew` / `managingExisting`，其余可以写死
