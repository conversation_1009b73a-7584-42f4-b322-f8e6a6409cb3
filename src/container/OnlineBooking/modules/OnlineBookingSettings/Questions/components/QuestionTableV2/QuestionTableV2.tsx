import React from 'react';
import { Empty } from '@moego/ui';
import { QuestionTableV2Heading, type QuestionTableV2HeadingProps } from './QuestionTableV2Heading';
import { Condition } from '../../../../../../../components/Condition';

export interface QuestionTableV2Props {
  headingConfig: QuestionTableV2HeadingProps;
  isEmpty?: boolean;
  children?: React.ReactNode;
}

export const QuestionTableV2: React.FC<QuestionTableV2Props> = (props) => {
  const { isEmpty = false } = props;
  return (
    <div className="moe-w-[1140px]">
      <Condition if={!isEmpty}>
        <QuestionTableV2Heading {...props.headingConfig} />
        {props.children}
      </Condition>
      <Condition if={isEmpty}>
        <Empty
          className="moe-bg-neutral-sunken-0 moe-py-[24px] moe-rounded-m"
          title=""
          illustration={null}
          description="No questions have been set up yet"
          actionButtonVariant="tertiary"
          actionText="Add question"
          actionButtonProps={{
            size: 'l',
          }}
        />
      </Condition>
    </div>
  );
};
