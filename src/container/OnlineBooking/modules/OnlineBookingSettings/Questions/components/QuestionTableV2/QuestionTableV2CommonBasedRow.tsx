import React from 'react';
import { SortableList } from '../../../../../../../components/SortableList/SortableList';
import { Condition } from '../../../../../../../components/Condition';
import { Checkbox, cn, IconButton } from '@moego/ui';
import { MinorCloseOutlined, MinorEditOutlined } from '@moego/icons-react';
import { QuestionTableRowWidth } from './QuestionTableV2.config';

export interface QuestionTableV2CommonBasedRowProps {
  id: number;
  label: string;
  onRemove?: () => void;
  onRequired?: (required: boolean) => void;
  onEdit?: (id: number) => void;
  isDraggable?: boolean;
  isRequired?: boolean;
  isRequiredDisabled?: boolean;
  isEditable?: boolean;
  isRemovable?: boolean;
  description?: React.ReactNode;
  extra?: React.ReactNode;
  contentAddingNew?: React.ReactNode;
  contentManagingExisting?: React.ReactNode;
}

/**
 * Question Table 的行组件，通用的 UI 和交互逻辑
 */
export const QuestionTableV2CommonBasedRow: React.FC<QuestionTableV2CommonBasedRowProps> = (props) => {
  const {
    isDraggable,
    label,
    isRequired,
    isRequiredDisabled,
    isEditable,
    isRemovable,
    onEdit,
    onRemove,
    onRequired,
    id,
    description,
    extra,
    contentAddingNew,
    contentManagingExisting,
  } = props;

  return (
    <div className="moe-flex moe-flex-col moe-py-spacing-s moe-border-b moe-border-b-divider">
      <div className="moe-flex moe-items-center moe-h-[40px]">
        {isDraggable ? (
          <SortableList.DragHandle className={cn('moe-flex-shrink-0', QuestionTableRowWidth.dragHandle)} />
        ) : (
          <div className={cn('moe-h-[24px] moe-flex-shrink-0', QuestionTableRowWidth.dragHandle)} />
        )}
        <div className={cn('moe-flex moe-flex-1 moe-items-center')}>
          <div className={cn('moe-flex-shrink-0 moe-p-s', QuestionTableRowWidth.question)}>
            <div className="moe-text-regular-short moe-text-primary">{label}</div>
            <Condition if={description}>{description}</Condition>
          </div>
          <div className={cn('moe-flex-shrink-0 moe-p-s', QuestionTableRowWidth.addingNew)}>{contentAddingNew}</div>
          <div className={cn('moe-flex-shrink-0 moe-p-s', QuestionTableRowWidth.managingExisting)}>
            {contentManagingExisting}
          </div>
          <div className={cn('moe-flex-shrink-0 moe-p-s', QuestionTableRowWidth.requiredToAnswer)}>
            <Checkbox isSelected={isRequired} isDisabled={isRequiredDisabled} onChange={onRequired} />
          </div>
          <div className={cn('moe-p-s moe-pr-xs moe-w-full moe-items-center moe-flex', QuestionTableRowWidth.actions)}>
            <div className="moe-flex-1 moe-flex moe-items-center moe-justify-end moe-gap-x-[16px]">
              <Condition if={isEditable}>
                <IconButton icon={<MinorEditOutlined />} onPress={() => onEdit?.(id)} />
              </Condition>
              <Condition if={isRemovable}>
                <IconButton icon={<MinorCloseOutlined />} onPress={onRemove} />
              </Condition>
            </div>
          </div>
        </div>
      </div>
      {extra}
    </div>
  );
};
