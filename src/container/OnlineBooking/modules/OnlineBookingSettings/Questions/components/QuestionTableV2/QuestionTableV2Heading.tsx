import React from 'react';
import { cn, Heading } from '@moego/ui';
import { QuestionTableRowWidth } from './QuestionTableV2.config';

const CLASS_TABLE_HEADING = 'moe-text-secondary moe-px-[16px] moe-py-[12px]';

export interface QuestionTableV2HeadingProps {
  addingNewTitle: string;
  managingExistingTitle: string;
}

/**
 * 纯布局用的组件，管理好宽度 + 与 table based row 对齐
 */
export const QuestionTableV2Heading: React.FC<QuestionTableV2HeadingProps> = (props) => {
  const { addingNewTitle, managingExistingTitle } = props;
  return (
    <div
      className={cn(
        'moe-h-[48px]',
        'moe-flex moe-items-center',
        'moe-border-b moe-border-b-divider moe-border-solid',
        'moe-whitespace-nowrap',
      )}
    >
      <div className={QuestionTableRowWidth.dragHandle} />
      <Heading size="6" className={cn(CLASS_TABLE_HEADING, QuestionTableRowWidth.question)}>
        Question
      </Heading>
      <Heading size="6" className={cn(CLASS_TABLE_HEADING, QuestionTableRowWidth.addingNew)}>
        {addingNewTitle}
      </Heading>
      <Heading size="6" className={cn(CLASS_TABLE_HEADING, QuestionTableRowWidth.managingExisting)}>
        {managingExistingTitle}
      </Heading>
      <Heading size="6" className={cn(CLASS_TABLE_HEADING, QuestionTableRowWidth.requiredToAnswer)}>
        Required to answer
      </Heading>
      <div className={QuestionTableRowWidth.actions} />
    </div>
  );
};
