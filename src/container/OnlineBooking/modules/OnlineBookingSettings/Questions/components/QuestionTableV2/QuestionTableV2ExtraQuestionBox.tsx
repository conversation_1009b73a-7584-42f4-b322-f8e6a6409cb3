import { cn } from '@moego/ui';
import React, { memo } from 'react';

export interface QuestionTableV2ExtraQuestionBoxProps {
  children: React.ReactNode | React.ReactNode[];
  className?: string;
}

export const QuestionTableV2ExtraQuestionBox = memo<QuestionTableV2ExtraQuestionBoxProps>((props) => {
  const { children, className } = props;
  return (
    <div
      className={cn(
        'moe-ml-[52px] moe-w-[calc(100%-52px)] moe-p-s',
        'moe-rounded-m moe-bg-neutral-sunken-light moe-font-manrope',
        className,
      )}
    >
      {children}
    </div>
  );
});
