import { Alert } from '@moego/ui';
import React from 'react';
import { useEmergencyContactMetaInfo } from '../../../../../Client/ClientInfo/ClientDetail/components/hooks/useEmergencyContactMetaInfo';

export function MovedEmgNotification() {
  const [{ showOBPetEmergencyMovedNotification }, updateMetaData] = useEmergencyContactMetaInfo();

  if (!showOBPetEmergencyMovedNotification) {
    return null;
  }

  return (
    <Alert
      color="information"
      className="moe-rounded-s moe-mb-s"
      onClose={() => updateMetaData({ showOBPetEmergencyMovedNotification: false })}
    >
      The emergency contact question has been moved from &quot;Pet Questions&quot; to &quot;Client Questions&quot;.
    </Alert>
  );
}
