import React from 'react';
import { Checkbox } from '@moego/ui';
import { ExistingPetAccessModeSelect, type ExistingPetAccessModeSelectProps } from './ExistingPetAccessModeSelect';
import { NewPetAccessMode } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { isExistingPetAccessModeEnabled } from '../../../../../../../../store/onlineBooking/models/OBQuestion.utils';
import {
  QuestionTableV2CommonBasedRow,
  type QuestionTableV2CommonBasedRowProps,
} from '../../../components/QuestionTableV2/QuestionTableV2CommonBasedRow';

export interface PetQuestionBasedRowProps extends QuestionTableV2CommonBasedRowProps {
  isDisabled?: boolean;
  newPetConfig?: {
    newPetAccessMode?: NewPetAccessMode;
    onNewPetAccessModeChange?: (newPetAccessMode: NewPetAccessMode) => void;
  };
  existingPetConfig?: ExistingPetAccessModeSelectProps;
}

export const PetQuestionBasedRow: React.FC<PetQuestionBasedRowProps> = (props) => {
  const { isDisabled, newPetConfig, existingPetConfig } = props;

  /**
   * require disabled 相关
   */
  const isNewPetEnabled = newPetConfig?.newPetAccessMode === NewPetAccessMode.ENABLED;
  const isExistingPetEnabled = isExistingPetAccessModeEnabled(existingPetConfig?.value);
  const isRequiredDisabled = isDisabled || (!isNewPetEnabled && !isExistingPetEnabled);

  return (
    <QuestionTableV2CommonBasedRow
      {...props}
      isRequiredDisabled={isRequiredDisabled}
      contentAddingNew={
        <Checkbox
          isSelected={isNewPetEnabled}
          isDisabled={isDisabled}
          onChange={(newVal) => {
            newPetConfig?.onNewPetAccessModeChange?.(newVal ? NewPetAccessMode.ENABLED : NewPetAccessMode.DISABLED);
          }}
        />
      }
      contentManagingExisting={<ExistingPetAccessModeSelect isDisabled={isDisabled} {...existingPetConfig} />}
    />
  );
};
