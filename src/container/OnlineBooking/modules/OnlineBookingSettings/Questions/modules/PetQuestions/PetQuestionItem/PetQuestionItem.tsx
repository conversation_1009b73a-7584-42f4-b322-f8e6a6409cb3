import React, { memo, useMemo } from 'react';
import { PetCommonQuestion, type PetCommonQuestionProps } from './PetCommonQuestion';
import { type DynamicQuestion } from '../../../components/Question/SpecialQuestions/types';
import { Question<PERSON>oat } from '../../../components/Question/SpecialQuestions/QuestionCoat';
import { QuestionPetWeight } from '../../../components/Question/SpecialQuestions/QuestionPetWeight';
import { QuestionVaccine } from '../../../components/Question/SpecialQuestions/QuestionVaccine/QuestionVaccine';
import { useSelector } from 'amos';
import { draftOnlineBookingQuestionMapBox } from '../../../../../../../../store/onlineBooking/settings/questions.boxes';

const PetQuestionEntryList: DynamicQuestion[] = [QuestionCoat, QuestionPetWeight, QuestionVaccine];

export interface PetQuestionItemProps extends PetCommonQuestionProps {}

export const PetQuestionItem = memo(function PetQuestionItem(props: PetQuestionItemProps) {
  const { questionId, ...rest } = props;
  const [question] = useSelector(draftOnlineBookingQuestionMapBox.mustGetItem(questionId));
  const DynamicQuestion = useMemo(() => {
    const TargetQuestion = PetQuestionEntryList.find((i) => i.questionKey === question.key);
    return TargetQuestion;
  }, [question.key]);

  const RenderQuestion = DynamicQuestion || PetCommonQuestion;
  return <RenderQuestion questionId={questionId} {...rest} />;
});
