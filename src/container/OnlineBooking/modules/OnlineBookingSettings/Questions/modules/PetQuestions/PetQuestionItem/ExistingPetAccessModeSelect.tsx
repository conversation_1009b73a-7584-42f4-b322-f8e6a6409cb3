import React, { useCallback } from 'react';
import { Checkbox, Select, Text } from '@moego/ui';
import {
  ExistingPetAccessModeEnum,
  ExistingPetAccessModeOptions,
} from '../../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { ExistingPetAccessMode } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { Condition } from '../../../../../../../../components/Condition';

export interface ExistingPetAccessModeSelectProps {
  /** 是否支持改变 */
  isModeChangeable?: boolean;
  /** checkbox 启用时，赋的默认值 */
  defaultEnableValue?: ExistingPetAccessMode;
  value?: ExistingPetAccessMode;
  onModeChange?: (value: ExistingPetAccessMode) => void;
  isDisabled?: boolean;
}

export const ExistingPetAccessModeSelect: React.FC<ExistingPetAccessModeSelectProps> = (props) => {
  const {
    value: existingPetAccessMode,
    isModeChangeable,
    defaultEnableValue = ExistingPetAccessMode.VIEW_AND_EDIT,
    isDisabled,
    onModeChange: onModeChange,
  } = props;

  const isManagingExistingPetEnabled =
    existingPetAccessMode === ExistingPetAccessMode.VIEW ||
    existingPetAccessMode === ExistingPetAccessMode.VIEW_AND_EDIT;

  const handleCheckboxChange = useCallback(
    (isSelected: boolean) => {
      if (isSelected) {
        onModeChange?.(defaultEnableValue);
      } else {
        onModeChange?.(ExistingPetAccessMode.DISABLED);
      }
    },
    [onModeChange, defaultEnableValue],
  );

  return (
    <div className="moe-flex moe-justify-start moe-items-center moe-gap-xs">
      <Checkbox isSelected={isManagingExistingPetEnabled} onChange={handleCheckboxChange} isDisabled={isDisabled}>
        <Condition if={!isModeChangeable}>
          <Text variant="regular-short" className="moe-ml-xs">
            {existingPetAccessMode ? ExistingPetAccessModeEnum.mapLabels[existingPetAccessMode].label : ''}
          </Text>
        </Condition>
      </Checkbox>
      <Condition if={isModeChangeable && isManagingExistingPetEnabled}>
        <Select
          value={existingPetAccessMode + ''}
          className="moe-w-[200px] moe-h-[40px]"
          onChange={(val) => {
            val && onModeChange?.(+val);
          }}
          isDisabled={isDisabled}
        >
          {ExistingPetAccessModeOptions.map((option) => (
            <Select.Item key={option.value} value={option.value + ''} title={option.label} />
          ))}
        </Select>
      </Condition>
    </div>
  );
};
