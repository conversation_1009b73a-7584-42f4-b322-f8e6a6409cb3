import React from 'react';
import { useGetQuestionListIsEmpty } from '../../hooks/useGetQuestionListIsEmpty';
import { OnlineBookingQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { useQuestionEditList } from '../../hooks/useQuestionEditList';
import { useQuestionSortMove } from '../../hooks/useQuestionSortMove';
import { SortableList } from '../../../../../../../components/SortableList/SortableList';
import { useCustomQuestionEdit } from '../../hooks/useCustomQuestionEdit';
import { PetQuestionItem } from './PetQuestionItem/PetQuestionItem';
import { QuestionTableV2 } from '../../components/QuestionTableV2/QuestionTableV2';

export interface PetQuestionTableProps {}

const CURRENT_CATEGORY_TYPE = OnlineBookingQuestionCategory.ForPet;

export const PetQuestionTable: React.FC<PetQuestionTableProps> = () => {
  const isQuestionListEmpty = useGetQuestionListIsEmpty(CURRENT_CATEGORY_TYPE);
  const { additionalList, preservedList } = useQuestionEditList(CURRENT_CATEGORY_TYPE);
  const handleMove = useQuestionSortMove(CURRENT_CATEGORY_TYPE);
  const { renderCustomQuestionModal, handleEditCustomQuestion, handleRemoveCustomQuestion } = useCustomQuestionEdit({
    type: CURRENT_CATEGORY_TYPE,
  });

  return (
    <QuestionTableV2
      isEmpty={isQuestionListEmpty}
      headingConfig={{
        addingNewTitle: 'New pets',
        managingExistingTitle: 'Existing pets',
      }}
    >
      <div>
        {preservedList.map((questionId) => {
          return <PetQuestionItem key={questionId} type={CURRENT_CATEGORY_TYPE} questionId={questionId} />;
        })}
        <SortableList
          items={additionalList.map((id) => ({ id }))}
          onChange={(_items, formIndex, toIndex) => {
            handleMove(formIndex, toIndex);
          }}
          renderItem={(item) => {
            return (
              <SortableList.Item id={item.id}>
                <PetQuestionItem
                  key={item.id}
                  type={CURRENT_CATEGORY_TYPE}
                  questionId={item.id}
                  onEditCustomQuestion={handleEditCustomQuestion}
                  onRemoveCustomQuestion={handleRemoveCustomQuestion}
                />
              </SortableList.Item>
            );
          }}
        />
      </div>
      {renderCustomQuestionModal()}
    </QuestionTableV2>
  );
};
