import React, { memo } from 'react';
import { useSetQuestionState } from '../../../hooks/useSetQuestionState';
import { NormalBool } from '../../../../../../../../store/utils/createEnum';
import { useQuestion } from '../../../hooks/useQuestion';
import { useQuestionRemove } from '../../../hooks/useQuestionRemove';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import { type OnlineBookingQuestionCategory } from '../../../../../../../../store/onlineBooking/models/OBQuestion.base';
import {
  ExistingPetAccessMode,
  NewPetAccessMode,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { getExistingPetAccessModeConfig } from '../../../../../../../../store/onlineBooking/models/OBQuestion.utils';
import { PetQuestionBasedRow } from './PetQuestionBasedRow';
import { type OBQuestionRecord } from '../../../../../../../../store/onlineBooking/models/OBQuestion';

export interface PetCommonQuestionProps {
  type: OnlineBookingQuestionCategory;
  questionId: number;
  onEditCustomQuestion?: (id: number) => void;
  onRemoveCustomQuestion?: (id: number) => void;
  isEditable?: boolean;
  onEdit?: (id: number) => void;
  /**
   * 额外信息展示
   */
  extra?: React.ReactNode;
  /**
   * 处理是否必填
   * @param required 是否必填
   * @returns 是否需要阻断变更
   */
  shouldStopBeforeHandleRequired?: (required: boolean) => boolean;
}

export const PetCommonQuestion = memo(function CommonQuestion(props: PetCommonQuestionProps) {
  const { questionId, onEditCustomQuestion, onRemoveCustomQuestion, extra, type, isEditable, onEdit } = props;
  const question = useQuestion(questionId);
  const onRemoveSystemQuestion = useQuestionRemove(questionId);
  const handleRemove = useLatestCallback(() => {
    if (question.isCustomQuestion) {
      onRemoveCustomQuestion?.(questionId);
    } else {
      onRemoveSystemQuestion();
    }
  });
  const setQuestionState = useSetQuestionState(questionId);
  const handleRequired = (e: boolean) => {
    if (props.shouldStopBeforeHandleRequired && NormalBool.get(e)) {
      const shouldStop = props.shouldStopBeforeHandleRequired(e);
      if (shouldStop) {
        return;
      }
    }

    setQuestionState({ isRequired: NormalBool.get(e) });
  };
  const handleCheckRequiredDisable = useLatestCallback(
    (param: Pick<OBQuestionRecord, 'newPetAccessMode' | 'existingPetAccessMode'>) => {
      if (
        param.newPetAccessMode === NewPetAccessMode.DISABLED &&
        param.existingPetAccessMode === ExistingPetAccessMode.DISABLED
      ) {
        handleRequired(false);
      }
    },
  );
  const handleNewPetAccessModeChange = useLatestCallback((newPetAccessMode: NewPetAccessMode) => {
    setQuestionState({ newPetAccessMode });
    handleCheckRequiredDisable({ newPetAccessMode, existingPetAccessMode: question.existingPetAccessMode });
  });
  const handleExistingPetAccessModeChange = useLatestCallback((existingPetAccessMode: ExistingPetAccessMode) => {
    setQuestionState({ existingPetAccessMode });
    handleCheckRequiredDisable({ newPetAccessMode: question.newPetAccessMode, existingPetAccessMode });
  });

  const existingPetDefaultConfig = getExistingPetAccessModeConfig(question.key);

  return (
    <PetQuestionBasedRow
      id={questionId}
      isDisabled={question.disabled}
      label={question.question}
      isDraggable={!question.isPreservedQuestion(type)}
      isRequired={NormalBool.truly(question.isRequired)}
      isRemovable={!question.disabled}
      onRemove={handleRemove}
      isEditable={question.isCustomQuestion || isEditable}
      onEdit={question.isCustomQuestion ? onEditCustomQuestion : onEdit}
      onRequired={handleRequired}
      extra={extra}
      newPetConfig={{
        newPetAccessMode: question.newPetAccessMode,
        onNewPetAccessModeChange: handleNewPetAccessModeChange,
      }}
      existingPetConfig={{
        ...existingPetDefaultConfig,
        value: question.existingPetAccessMode,
        onModeChange: handleExistingPetAccessModeChange,
      }}
    />
  );
});
