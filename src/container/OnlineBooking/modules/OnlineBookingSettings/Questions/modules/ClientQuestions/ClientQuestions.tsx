import { Spin } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { toast<PERSON><PERSON> } from '../../../../../../../components/Toast/Toast';
import { updateQuestionList } from '../../../../../../../store/onlineBooking/actions/private/questions.actions';
import { OnlineBookingQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { selectDraftQuestionDirty } from '../../../../../../../store/onlineBooking/settings/questions.selectors';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { Update } from '../../../components/Update';
import { MovedEmgNotification } from '../../components/MovedEmgNotification';
import { QuestionDrawer } from '../../components/QuestionDrawer';
import { QuestionTitle } from '../../components/QuestionTitle';
import { TabDescription } from '../../components/TabDescription';
import { useCustomQuestionEdit } from '../../hooks/useCustomQuestionEdit';
import { usePreloadingQuestions } from '../../hooks/usePreloadingQuestions';
import { TabDescriptionType } from '../../types';
import { ClientQuestionTable } from './ClientQuestionTable';

export interface ClientQuestionsProps {}

const type = OnlineBookingQuestionCategory.ForOwner;

export const ClientQuestions = memo<ClientQuestionsProps>(() => {
  const isLoading = usePreloadingQuestions(type);

  const dispatch = useDispatch();
  const showQuestionsDrawer = useBool();
  const [isDirty] = useSelector(selectDraftQuestionDirty(type));

  const handleSubmit = useSerialCallback(async () => {
    await dispatch(updateQuestionList(type));
    toastApi.success('Questions saved');
  });

  const { renderCustomQuestionModal, handleAddCustomQuestion } = useCustomQuestionEdit({ type });

  return (
    <div>
      <TabDescription description={TabDescriptionType.Client} />
      <Spin
        classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
        isLoading={isLoading || handleSubmit.isBusy()}
      >
        <QuestionTitle type={type} onSelectQuestions={showQuestionsDrawer.open} />
        <MovedEmgNotification />
        <ClientQuestionTable />
        <QuestionDrawer
          type={type}
          isOpen={showQuestionsDrawer.value}
          onAddCustomQuestion={() => {
            handleAddCustomQuestion();
            showQuestionsDrawer.close();
          }}
          onClose={showQuestionsDrawer.close}
        />
      </Spin>
      <Update
        updateTxt="Save"
        active={isDirty}
        onSubmit={handleSubmit}
        buttonVisible={!showQuestionsDrawer.value && isDirty}
      />
      {renderCustomQuestionModal()}
    </div>
  );
});
