import React from 'react';
import { Checkbox } from '@moego/ui';
import { NewClientAccessMode } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { isExistingClientAccessModeEnabled } from '../../../../../../../store/onlineBooking/models/OBQuestion.utils';
import {
  QuestionTableV2CommonBasedRow,
  type QuestionTableV2CommonBasedRowProps,
} from '../../components/QuestionTableV2/QuestionTableV2CommonBasedRow';
import {
  ExistingClientAccessModeSelect,
  type ExistingClientAccessModeSelectProps,
} from './ExistingClientAccessModeSelect';

export interface ClientQuestionBasedRowProps extends QuestionTableV2CommonBasedRowProps {
  isDisabled?: boolean;
  newClientConfig?: {
    newClientAccessMode?: NewClientAccessMode;
    onNewClientAccessModeChange?: (newClientAccessMode: NewClientAccessMode) => void;
  };
  existingClientConfig?: ExistingClientAccessModeSelectProps;
}

export const ClientQuestionBasedRow: React.FC<ClientQuestionBasedRowProps> = (props) => {
  const { isDisabled, newClientConfig, existingClientConfig } = props;

  /**
   * require disabled 相关
   */
  const isNewClientEnabled = newClientConfig?.newClientAccessMode === NewClientAccessMode.ENABLED;
  const isExistingClientEnabled = isExistingClientAccessModeEnabled(existingClientConfig?.value);
  const isRequiredDisabled = isDisabled || (!isNewClientEnabled && !isExistingClientEnabled);

  return (
    <QuestionTableV2CommonBasedRow
      {...props}
      isRequiredDisabled={isRequiredDisabled}
      contentAddingNew={
        <Checkbox
          isSelected={isNewClientEnabled}
          isDisabled={isDisabled}
          onChange={(newVal) => {
            newClientConfig?.onNewClientAccessModeChange?.(
              newVal ? NewClientAccessMode.ENABLED : NewClientAccessMode.DISABLED,
            );
          }}
        />
      }
      contentManagingExisting={<ExistingClientAccessModeSelect isDisabled={isDisabled} {...existingClientConfig} />}
    />
  );
};
