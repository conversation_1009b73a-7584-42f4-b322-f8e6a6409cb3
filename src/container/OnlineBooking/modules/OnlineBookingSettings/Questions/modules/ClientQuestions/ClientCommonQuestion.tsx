import React, { memo } from 'react';
import { useSetQuestionState } from '../../hooks/useSetQuestionState';
import { NormalBool } from '../../../../../../../store/utils/createEnum';
import { useQuestion } from '../../hooks/useQuestion';
import { useQuestionRemove } from '../../hooks/useQuestionRemove';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { type OnlineBookingQuestionCategory } from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import {
  ExistingClientAccessMode,
  NewClientAccessMode,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { getExistingClientAccessModeConfig } from '../../../../../../../store/onlineBooking/models/OBQuestion.utils';
import { ClientQuestionBasedRow } from './ClientQuestionBasedRow';
import { type OBQuestionRecord } from '../../../../../../../store/onlineBooking/models/OBQuestion';

export interface ClientCommonQuestionProps {
  type: OnlineBookingQuestionCategory;
  questionId: number;
  onEditCustomQuestion?: (id: number) => void;
  onRemoveCustomQuestion?: (id: number) => void;
  isEditable?: boolean;
  onEdit?: (id: number) => void;
  /**
   * 额外信息展示
   */
  extra?: React.ReactNode;
}

export const ClientCommonQuestion = memo(function CommonQuestion(props: ClientCommonQuestionProps) {
  const { questionId, onEditCustomQuestion, onRemoveCustomQuestion, extra, type, isEditable, onEdit } = props;
  const question = useQuestion(questionId);
  const onRemoveSystemQuestion = useQuestionRemove(questionId);
  const handleRemove = useLatestCallback(() => {
    if (question.isCustomQuestion) {
      onRemoveCustomQuestion?.(questionId);
    } else {
      onRemoveSystemQuestion();
    }
  });
  const setQuestionState = useSetQuestionState(questionId);
  const handleRequired = (e: boolean) => setQuestionState({ isRequired: NormalBool.get(e) });
  const handleCheckRequiredDisable = useLatestCallback(
    (param: Pick<OBQuestionRecord, 'newClientAccessMode' | 'existingClientAccessMode'>) => {
      if (
        param.newClientAccessMode === NewClientAccessMode.DISABLED &&
        param.existingClientAccessMode === ExistingClientAccessMode.DISABLED
      ) {
        handleRequired(false);
      }
    },
  );
  const handleNewClientAccessModeChange = useLatestCallback((newClientAccessMode: NewClientAccessMode) => {
    setQuestionState({ newClientAccessMode });
    handleCheckRequiredDisable({
      newClientAccessMode,
      existingClientAccessMode: question.existingClientAccessMode,
    });
  });
  const handleExistingClientAccessModeChange = useLatestCallback(
    (existingClientAccessMode: ExistingClientAccessMode) => {
      setQuestionState({ existingClientAccessMode });
      handleCheckRequiredDisable({
        newClientAccessMode: question.newClientAccessMode,
        existingClientAccessMode,
      });
    },
  );

  const existingClientDefaultConfig = getExistingClientAccessModeConfig(question.key);

  return (
    <ClientQuestionBasedRow
      id={questionId}
      isDisabled={question.disabled}
      label={question.question}
      isDraggable={!question.isPreservedQuestion(type)}
      isRequired={NormalBool.truly(question.isRequired)}
      isRemovable={!question.disabled}
      onRemove={handleRemove}
      isEditable={question.isCustomQuestion || isEditable}
      onEdit={question.isCustomQuestion ? onEditCustomQuestion : onEdit}
      onRequired={handleRequired}
      extra={extra}
      newClientConfig={{
        newClientAccessMode: question.newClientAccessMode,
        onNewClientAccessModeChange: handleNewClientAccessModeChange,
      }}
      existingClientConfig={{
        ...existingClientDefaultConfig,
        value: question.existingClientAccessMode,
        onModeChange: handleExistingClientAccessModeChange,
      }}
    />
  );
});
