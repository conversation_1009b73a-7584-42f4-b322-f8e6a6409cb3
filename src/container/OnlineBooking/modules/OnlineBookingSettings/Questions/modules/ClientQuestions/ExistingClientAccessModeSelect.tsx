import React, { useCallback } from 'react';
import { Checkbox, Select, Text } from '@moego/ui';
import {
  ExistingClientAccessModeEnum,
  ExistingClientAccessModeOptions,
} from '../../../../../../../store/onlineBooking/models/OBQuestion.base';
import { ExistingClientAccessMode } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { Condition } from '../../../../../../../components/Condition';

export interface ExistingClientAccessModeSelectProps {
  /** 是否支持改变 */
  isModeChangeable?: boolean;
  /** checkbox 启用时，赋的默认值 */
  defaultEnableValue?: ExistingClientAccessMode;
  value?: ExistingClientAccessMode;
  onModeChange?: (value: ExistingClientAccessMode) => void;
  isDisabled?: boolean;
}

export const ExistingClientAccessModeSelect: React.FC<ExistingClientAccessModeSelectProps> = (props) => {
  const {
    value,
    isModeChangeable,
    defaultEnableValue = ExistingClientAccessMode.VIEW_AND_EDIT,
    isDisabled,
    onModeChange,
  } = props;

  const isManagingExistingClientEnabled =
    value === ExistingClientAccessMode.VIEW || value === ExistingClientAccessMode.VIEW_AND_EDIT;

  const handleCheckboxChange = useCallback(
    (isSelected: boolean) => {
      if (isSelected) {
        onModeChange?.(defaultEnableValue);
      } else {
        onModeChange?.(ExistingClientAccessMode.DISABLED);
      }
    },
    [onModeChange, defaultEnableValue],
  );

  return (
    <div className="moe-flex moe-justify-start moe-items-center moe-gap-xs">
      <Checkbox isSelected={isManagingExistingClientEnabled} onChange={handleCheckboxChange} isDisabled={isDisabled}>
        <Condition if={!isModeChangeable}>
          <Text variant="regular-short" className="moe-ml-xs">
            {value ? ExistingClientAccessModeEnum.mapLabels[value].label : ''}
          </Text>
        </Condition>
      </Checkbox>
      <Condition if={isModeChangeable && isManagingExistingClientEnabled}>
        <Select
          value={value + ''}
          className="moe-w-[200px] moe-h-[40px]"
          onChange={(val) => {
            val && onModeChange?.(+val);
          }}
          isDisabled={isDisabled}
        >
          {ExistingClientAccessModeOptions.map((option) => (
            <Select.Item key={option.value} value={option.value + ''} title={option.label} />
          ))}
        </Select>
      </Condition>
    </div>
  );
};
