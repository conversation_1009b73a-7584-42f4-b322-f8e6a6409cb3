import { useSelector } from 'amos';
import { groupBy } from 'lodash';
import { OnlineBookingQuestionCategory } from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { draftOnlineBookingQuestionMapBox } from '../../../../../../store/onlineBooking/settings/questions.boxes';
import { selectDraftOnlineBookingQuestionIdList } from '../../../../../../store/onlineBooking/settings/questions.selectors';

export function useQuestionEditList(type: OnlineBookingQuestionCategory) {
  const [list, draftQuestionMapBox] = useSelector(
    selectDraftOnlineBookingQuestionIdList(type),
    draftOnlineBookingQuestionMapBox,
  );
  if (type === OnlineBookingQuestionCategory.ForBoarding || type === OnlineBookingQuestionCategory.ForDaycare) {
    return {
      preservedList: [],
      additionalList: list.toArray().filter((item) => {
        const draftItem = draftQuestionMapBox.mustGetItem(item);
        return draftItem.isAvailableInEditor;
      }),
    };
  }
  const result = groupBy(list.toArray(), (questionId) => {
    const item = draftQuestionMapBox.mustGetItem(questionId);
    if (!item.isAvailableInEditor) {
      return 'default';
    }
    return item.isPreservedQuestion(type) ? 'preservedList' : 'additionalList';
  });

  return {
    preservedList: result?.preservedList ?? [],
    additionalList: result?.additionalList ?? [],
  };
}
