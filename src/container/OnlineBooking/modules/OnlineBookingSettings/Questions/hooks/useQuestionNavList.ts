import { useSelector, useStore } from 'amos';
import { groupBy, isNumber } from 'lodash';
import { useMemo } from 'react';
import {
  ClientAdditionFixedOrder,
  OnlineBookingQuestionCategory,
  PetAdditionQuestionFixedOrder,
} from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { draftOnlineBookingQuestionMapBox } from '../../../../../../store/onlineBooking/settings/questions.boxes';
import { selectDraftQuestionIdInFixedSort } from '../../../../../../store/onlineBooking/settings/questions.selectors';

export function useQuestionNavList(type: OnlineBookingQuestionCategory) {
  const store = useStore();
  const [ids] = useSelector(selectDraftQuestionIdInFixedSort(type));
  return useMemo(() => {
    const isPet = type === OnlineBookingQuestionCategory.ForPet;
    const result = groupBy(ids.toArray(), (id) => {
      const q = store.select(draftOnlineBookingQuestionMapBox.mustGetItem(id));
      if (q.isNotAvailable) {
        return 'notAvailable';
      }
      if (q.isPreservedQuestion(type)) {
        return 'preservedList';
      }
      return q.isCustomQuestion ? 'customList' : 'systemList';
    });
    const systemList = (result.systemList ?? []).sort((a, b) => {
      const sortMap: Record<string, number> = isPet ? PetAdditionQuestionFixedOrder : ClientAdditionFixedOrder;
      const qa = store.select(draftOnlineBookingQuestionMapBox.mustGetItem(a)).question;
      const qb = store.select(draftOnlineBookingQuestionMapBox.mustGetItem(b)).question;
      if (isNumber(sortMap[qa]) && isNumber(sortMap[qb])) {
        return sortMap[qa] - sortMap[qb];
      }
      return 0;
    });

    return {
      preservedList: result.preservedList ?? [],
      systemList,
      customList: result.customList ?? [],
    };
  }, [ids, type]);
}
