import { useSelector } from 'amos';
import { useMemo } from 'react';
import { draftOnlineBookingQuestionMapBox } from '../../../../../../store/onlineBooking/settings/questions.boxes';
import { QuestionFeeding } from '../components/Question/SpecialQuestions/QuestionFeeding';
import { QuestionMedication } from '../components/Question/SpecialQuestions/QuestionMedication';
import { type DynamicQuestion } from '../components/Question/SpecialQuestions/types';

/**
 * Pet 相关的 question 已抽提到 PetQuestions Module 下
 */
const QuestionEntryList: DynamicQuestion[] = [QuestionFeeding, QuestionMedication];

export const useRenderDynamicQuestion = (questionId: number) => {
  const [question] = useSelector(draftOnlineBookingQuestionMapBox.mustGetItem(questionId));
  const RenderQuestion = useMemo(() => {
    const TargetQuestion = QuestionEntryList.find((i) => i.questionName === question.question);
    return TargetQuestion;
  }, [question.question]);
  return RenderQuestion;
};
