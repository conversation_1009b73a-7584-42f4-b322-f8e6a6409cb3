import { useSelector } from 'amos';
import { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { OnlineBookingNav, OnlineBookingNavList, OnlineBookingNavType } from '../types';

export const useGetOBNavList = () => {
  const [business] = useSelector(selectCurrentBusiness());

  return useMemo(() => {
    if (business.isMobileGrooming()) {
      const targetIndex = OnlineBookingNavList.findIndex((item) => item.id === OnlineBookingNavType.Questions) + 1;
      const item = {
        title: OnlineBookingNav.MobileGrooming,
        id: OnlineBookingNavType[OnlineBookingNav.MobileGrooming],
        path: OnlineBookingNavType[OnlineBookingNav.MobileGrooming],
      };
      return [...OnlineBookingNavList.slice(0, targetIndex), item, ...OnlineBookingNavList.slice(targetIndex)];
    }

    return OnlineBookingNavList;
  }, [business.isMobileGrooming()]);
};
