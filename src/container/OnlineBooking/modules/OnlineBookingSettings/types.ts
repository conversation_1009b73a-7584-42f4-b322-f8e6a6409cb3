import { createEnum } from '../../../../store/utils/createEnum';
import { type SettingsLeftNavItem } from '../../../settings/Settings/types';

// title
export enum OnlineBookingNav {
  Overview = 'Overview',
  BookingSite = 'Booking site',

  Availability = 'Availability',
  Services = 'Services',
  Questions = 'Questions',
  MobileGrooming = 'Mobile grooming',

  Agreement = 'Agreement',
  Payments = 'Payment',
  Notification = 'Notification',
}

// route params
export enum OnlineBookingPanel {
  Overview = 'overview',
  BookingSite = 'booking_site',
  Availability = 'availability',
  Services = 'service',
  Questions = 'questions',
  MobileGrooming = 'mobile_grooming',
  Agreement = 'agreement',
  Payments = 'payments',
  Notification = 'notification',
}

export type OnlineBookingPanelType = keyof typeof OnlineBookingPanel;

export const OnlineBookingNavType = createEnum({
  [OnlineBookingNav.Overview]: [OnlineBookingPanel.Overview, 'overview'],
  [OnlineBookingNav.BookingSite]: [OnlineBookingPanel.BookingSite, 'booking site'],
  [OnlineBookingNav.Availability]: [OnlineBookingPanel.Availability, 'availability'],
  [OnlineBookingNav.Services]: [OnlineBookingPanel.Services, 'services'],
  [OnlineBookingNav.Questions]: [OnlineBookingPanel.Questions, 'questions'],
  [OnlineBookingNav.MobileGrooming]: [OnlineBookingPanel.MobileGrooming, 'mobile grooming'],
  [OnlineBookingNav.Agreement]: [OnlineBookingPanel.Agreement, 'agreement'],
  [OnlineBookingNav.Payments]: [OnlineBookingPanel.Payments, 'payments'],
  [OnlineBookingNav.Notification]: [OnlineBookingPanel.Notification, 'notification'],
} as const);

export const OnlineBookingNavList: SettingsLeftNavItem<OnlineBookingPanel | OnlineBookingNav>[] = [
  {
    title: OnlineBookingNav.Overview,
    id: OnlineBookingNavType[OnlineBookingNav.Overview],
    path: OnlineBookingNavType[OnlineBookingNav.Overview],
  },
  {
    title: OnlineBookingNav.BookingSite,
    id: OnlineBookingNavType[OnlineBookingNav.BookingSite],
    path: OnlineBookingNavType[OnlineBookingNav.BookingSite],
  },
  {
    title: OnlineBookingNav.Availability,
    id: OnlineBookingNavType[OnlineBookingNav.Availability],
    path: OnlineBookingNavType[OnlineBookingNav.Availability],
  },
  {
    title: OnlineBookingNav.Services,
    id: OnlineBookingNavType[OnlineBookingNav.Services],
    path: OnlineBookingNavType[OnlineBookingNav.Services],
  },
  {
    title: OnlineBookingNav.Questions,
    id: OnlineBookingNavType[OnlineBookingNav.Questions],
    path: OnlineBookingNavType[OnlineBookingNav.Questions],
  },
  {
    title: OnlineBookingNav.Agreement,
    id: OnlineBookingNavType[OnlineBookingNav.Agreement],
    path: OnlineBookingNavType[OnlineBookingNav.Agreement],
  },
  {
    title: OnlineBookingNav.Payments,
    id: OnlineBookingNavType[OnlineBookingNav.Payments],
    path: OnlineBookingNavType[OnlineBookingNav.Payments],
  },
  {
    title: OnlineBookingNav.Notification,
    id: OnlineBookingNavType[OnlineBookingNav.Notification],
    path: OnlineBookingNavType[OnlineBookingNav.Notification],
  },
];
