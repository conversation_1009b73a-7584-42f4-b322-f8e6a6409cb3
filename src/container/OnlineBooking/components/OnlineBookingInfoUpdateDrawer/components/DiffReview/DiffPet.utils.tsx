import { pick } from 'lodash';
import { OBPetQuestionKey } from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { type PetUpdateParams, type PetDetailType } from '../../type';

export const PetIgnoreKeys = [OBPetQuestionKey.PetType, OBPetQuestionKey.Breed];

/**
 * Question Key 与 pet detail 相关字段的映射关系
 * 原则是业务依赖的场景都需要显式在代码定义清楚，避免依赖了可能会变的 question key 而埋下坑，导致不可预测的 bug 出现
 */
const questionKeyToFieldMap: Record<
  OBPetQuestionKey,
  // 交集部分可以直接赋值，非交集部分，需要通过函数来做映射
  (keyof PetDetailType & keyof PetUpdateParams)[] | ((detail: PetDetailType) => Partial<PetUpdateParams>)
> = {
  // Pet Type 和 Breed 会被 Ignore 掉，在（PetIgnoreKeys），可以被忽略，但映射关系保留
  [OBPetQuestionKey.PetType]: ['petTypeId'],
  [OBPetQuestionKey.Breed]: ['breed'],
  // 以下的字段只需要考虑 value 的映射
  [OBPetQuestionKey.PetName]: ['petName'],
  [OBPetQuestionKey.Behavior]: ['behavior'],
  [OBPetQuestionKey.Birthday]: ['birthday'],
  // CoatType 的前身是 hairLength
  [OBPetQuestionKey.CoatType]: ['hairLength'],
  [OBPetQuestionKey.Fixed]: ['fixed'],
  [OBPetQuestionKey.Gender]: ['gender'],
  [OBPetQuestionKey.HealthIssues]: ['healthIssues'],
  [OBPetQuestionKey.PetImage]: ['avatarPath'],
  [OBPetQuestionKey.VetAddress]: ['vetAddress'],
  [OBPetQuestionKey.VetName]: ['vetName'],
  // VetPhone 最终提交用的字段是 vetPhone，但返回的 detail 只有 vetPhoneNumber，所以需要一个函数来映射
  [OBPetQuestionKey.VetPhone]: (detail: PetDetailType) => ({ vetPhone: detail.vetPhoneNumber }),
  [OBPetQuestionKey.Weight]: ['weight'],
  /**
   * Vaccine 和 VaccineDocument 单独处理，不需要 value 返回
   */
  [OBPetQuestionKey.VaccineList]: [],
  [OBPetQuestionKey.VaccineDocument]: [],
  // 下面这俩在 setting 没有定义，但在 emergency contact 的代码上有定义，保留原映射关系
  [OBPetQuestionKey.EmergencyContact]: ['emergencyContactName', 'emergencyContactPhone'],
  // 单独的 HairLength 看起来一个废弃掉的 question？后续被 coatType 代替了
  [OBPetQuestionKey.HairLength]: [],
};

/**
 * 获取 diff 数据对应的改动数据的字段
 */
export const getDiffPartialPetDetailByQuestionKey = (
  petDetail: PetDetailType,
  questionKey: OBPetQuestionKey | string,
): Partial<PetUpdateParams> => {
  const fieldsToPick = questionKeyToFieldMap[questionKey as OBPetQuestionKey];
  if (typeof fieldsToPick === 'function') {
    return fieldsToPick(petDetail);
  } else if (Array.isArray(fieldsToPick) && fieldsToPick.length > 0) {
    return pick(petDetail, fieldsToPick);
  } else {
    return {};
  }
};

export const getPetDefaultRenderNodeByValue = (value: Partial<PetUpdateParams>) =>
  Object.keys(value).length === 1 ? Object.values(value)[0] : null;
