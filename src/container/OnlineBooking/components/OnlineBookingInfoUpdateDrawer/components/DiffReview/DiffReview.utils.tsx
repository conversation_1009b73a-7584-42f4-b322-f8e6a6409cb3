import { useSelector } from 'amos';
import { isNil } from 'lodash';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React, { useCallback } from 'react';
import { PetAvatar } from '../../../../../../components/Avatar/PetAvatar';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { referralSourceMapBox } from '../../../../../../store/business/referralSource.boxes';
import { printAddress } from '../../../../../../store/customer/customerAddress.selectors';
import { type OBQuestionRecord } from '../../../../../../store/onlineBooking/models/OBQuestion';
import {
  OBClientQuestionKey,
  OBPetQuestionKey,
  OnlineBookingQuestionType,
} from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { PetGender } from '../../../../../../store/pet/pet.boxes';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../../../store/utils/identifier';
import { transformFrequencyToString } from '../../../../../../utils/transformFrequencyToString';
import { jsonParse } from '../../../../../../utils/utils';
import { getPreferredDayText } from '../../../../../Client/ClientInfo/ClientDetail/components/PreferredDayInput';
import { useVaccinePreviewModal } from '../../hooks/useVaccinePreviewModal';
import {
  type CustomQuestionType,
  type CustomerDetailType,
  type NewAddressItem,
  type PetDetailType,
  type ReturnNodeAndValue,
} from '../../type';
import {
  CustomerIgnoreKeys,
  getCustomerDefaultRenderNodeByValue,
  getDiffPartialCustomerDetailByQuestionKey,
} from './DiffClient.utils';
import { getPetDefaultRenderNodeByValue, getDiffPartialPetDetailByQuestionKey, PetIgnoreKeys } from './DiffPet.utils';

const generateEmptyNode = (value: unknown) => ({ node: null, value });

export function useGetCustomRenderNode() {
  return useCallback(
    (question: OBQuestionRecord, detail?: CustomQuestionType, extra?: CustomQuestionType): ReturnNodeAndValue => {
      if (!detail || !extra) {
        // 如果detail或extra为空，直接返回空节点，它表达的意思是 首次填写/删除 问题
        return generateEmptyNode(detail);
      }
      if (question.questionType === OnlineBookingQuestionType.Checkbox) {
        const value = { [detail.key]: detail.answer };
        try {
          // 需要 safe json parse，否则如果 custom question type 从 input 改为 checkbox 的话就会有问题
          const answer = jsonParse(detail.answer, []);
          return {
            node: answer.join(','),
            value,
          };
          // eslint-disable-next-line sonarjs/no-ignored-exceptions
        } catch (_e) {
          return generateEmptyNode(value);
        }
      } else {
        return {
          node: detail.answer || '',
          value: { [detail.key]: detail.answer },
        };
      }
    },
    [],
  );
}

/**
 * TODO(gq): 上线后单独挪到 DiffPet.utils 中
 */
export function useGetPetRenderNode() {
  const [business] = useSelector(selectCurrentBusiness);
  const { openVaccinePreviewModal: onPreview } = useVaccinePreviewModal();

  return useCallback(
    (key: string, detail: PetDetailType, bSideDetail: PetDetailType): ReturnNodeAndValue => {
      if (!detail || PetIgnoreKeys.includes(key as OBPetQuestionKey)) {
        return generateEmptyNode(detail);
      }

      const value = getDiffPartialPetDetailByQuestionKey(detail, key);
      switch (key) {
        case OBPetQuestionKey.EmergencyContact: {
          const emergencyContactName = detail.emergencyContactName || '';
          const emergencyContactPhone = detail.emergencyContactPhone || '';
          const emergencyValue = { emergencyContactName, emergencyContactPhone };
          if (!emergencyContactName && !emergencyContactPhone) {
            return generateEmptyNode(emergencyValue);
          }
          return {
            node: [emergencyContactName, emergencyContactPhone].filter(Boolean).join(','),
            value: emergencyValue,
          };
        }
        case OBPetQuestionKey.PetImage: {
          const { avatarPath } = detail;

          if (avatarPath == null) {
            return generateEmptyNode(value);
          }

          if (!avatarPath) {
            return {
              node: (
                <div className="moe-w-[120px] moe-h-[120px] moe-rounded-[8px] moe-bg-[#e9ecef] moe-flex moe-items-center moe-justify-center">
                  {/* new val 中的 petTypeId 为 null，不可信，需要用 old val 数据 */}
                  <PetAvatar petTypeId={bSideDetail?.petTypeId} size={90} />
                </div>
              ),
              value,
            };
          }

          return {
            node: (
              <img
                className="!moe-w-[120px] !moe-h-[120px] !moe-rounded-[8px] !moe-object-cover"
                src={avatarPath}
                onClick={(e) => {
                  e.stopPropagation();
                  onPreview?.({ previewUrl: avatarPath });
                }}
              />
            ),
            value,
          };
        }
        case OBPetQuestionKey.CoatType:
          return { node: detail.hairLength, value };
        case OBPetQuestionKey.Breed:
          return { node: detail.breed, value };
        case OBPetQuestionKey.Gender: {
          const { gender } = detail;
          if (gender == null) {
            return generateEmptyNode(value);
          }
          return { node: PetGender.mapLabels[gender] || '', value };
        }
        case OBPetQuestionKey.Weight: {
          const { weight } = detail;
          if (weight == null) {
            return generateEmptyNode(value);
          }
          return { node: weight ? `${weight} ${business.unitOfWeight}` : '', value };
        }
        default: {
          return { node: getPetDefaultRenderNodeByValue(value), value };
        }
      }
    },
    [business],
  );
}

/**
 * TODO(gq): 上线后单独挪到 DiffClient.utils 中
 */
export function useGetClientRenderNode() {
  const [business, referralSourceMap, staffMap] = useSelector(selectCurrentBusiness, referralSourceMapBox, staffMapBox);

  return useCallback(
    (key: string, detail: CustomerDetailType): ReturnNodeAndValue => {
      if (!detail || CustomerIgnoreKeys.includes(key as OBClientQuestionKey)) {
        return generateEmptyNode(detail);
      }

      const value = getDiffPartialCustomerDetailByQuestionKey(detail, key);
      switch (key) {
        case OBClientQuestionKey.Birthday: {
          if (!isNormal(detail.birthday)) {
            return generateEmptyNode(value);
          }
          return {
            node: business.formatDate(detail.birthday),
            value,
          };
        }

        case OBClientQuestionKey.PreferredGroomer: {
          const { preferredGroomerId } = detail;
          if (!isNormal(preferredGroomerId)) {
            return generateEmptyNode(value);
          }
          const name = staffMap.mustGetItem(preferredGroomerId).fullName();
          return { node: name, value };
        }

        case OBClientQuestionKey.PreferredTimeOfTheDay: {
          const { preferredTime } = detail;
          if (preferredTime == null) {
            return generateEmptyNode(value);
          }
          const preferredStartTime = preferredTime[0] && business.formatFixedTime(preferredTime[0]! * T_MINUTE);
          const preferredEndTime = preferredTime[1] && business.formatFixedTime(preferredTime[1]! * T_MINUTE);
          const preferredTimeText =
            !isNil(preferredStartTime) && preferredEndTime ? `${preferredStartTime}-${preferredEndTime}` : '';

          return { node: preferredTimeText, value };
        }

        case OBClientQuestionKey.PreferredDayOfTheWeek: {
          const { preferredDay } = detail;
          if (preferredDay == null) {
            return generateEmptyNode(value);
          }
          const preferredDayText = getPreferredDayText(detail.preferredDay, business.daysOfWeekOptionList());
          return { node: preferredDayText, value };
        }

        case OBClientQuestionKey.PreferredFrequency: {
          const { preferredFrequencyDay, preferredFrequencyType } = detail;
          if (preferredFrequencyDay == null) {
            return generateEmptyNode(value);
          }
          return {
            node: transformFrequencyToString(preferredFrequencyDay, preferredFrequencyType),
            value,
          };
        }
        case OBClientQuestionKey.ReferralSource: {
          const { referralSourceId } = detail;
          if (referralSourceId == null) {
            return generateEmptyNode(value);
          }
          const { sourceName } = referralSourceMap.mustGetItem(referralSourceId);
          return { node: sourceName, value };
        }
        default: {
          return { node: getCustomerDefaultRenderNodeByValue(value), value };
        }
      }
    },
    [business, referralSourceMap, staffMap],
  );
}

export const getPrimaryAddressRenderNode = (detail: CustomerDetailType): ReturnNodeAndValue => {
  if (!detail) {
    return generateEmptyNode(detail);
  }
  const { primaryAddress } = detail;
  const addressText = printAddress(primaryAddress);
  return { node: addressText, value: { primaryAddress } };
};

export const getNewAddressItemRenderNode = (address: NewAddressItem, isNew: boolean): ReturnNodeAndValue => {
  const addressId = address?.customerAddressId;
  if (!addressId) {
    return generateEmptyNode(address);
  }
  const addressText = printAddress(address);
  return { node: addressText, value: isNew ? address : null };
};
