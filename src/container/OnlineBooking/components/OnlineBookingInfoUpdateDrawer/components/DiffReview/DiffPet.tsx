import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useState } from 'react';
import {
  OBPetQuestionKey,
  OnlineBookingQuestionCategory,
} from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { onlineBookingQuestionMapBox } from '../../../../../../store/onlineBooking/settings/questions.boxes';
import { selectOnlineBookingQuestionIdList } from '../../../../../../store/onlineBooking/settings/questions.selectors';
import { type CustomerPetsUpdateParams, type PetDetailType, type ReturnNodeAndValue } from '../../type';
import { useGetCustomRenderNode, useGetPetRenderNode } from './DiffReview.utils';
import { DiffHeader } from './components/DiffHeader';
import { DiffItem } from './components/DiffItem';
import { DiffVaccineList } from './components/DiffVaccineList';

export interface DiffPetProps {
  oldVal: PetDetailType;
  newVal: PetDetailType;
  needUpdateVaccineIds?: number[];
  onChange?: (v: Partial<CustomerPetsUpdateParams>) => void;
}

export const DiffPet = memo<DiffPetProps>((props) => {
  const { oldVal, newVal, needUpdateVaccineIds, onChange } = props;
  const [visible, setVisible] = useState(false);
  const [questionMap, questionList] = useSelector(
    onlineBookingQuestionMapBox,
    selectOnlineBookingQuestionIdList(OnlineBookingQuestionCategory.ForPet),
  );

  const handleChange = (
    isNew: boolean,
    oldNode: ReturnNodeAndValue,
    newNode: ReturnNodeAndValue,
    isCustomQuestion = false,
  ) => {
    setVisible(true);

    if (isCustomQuestion) {
      onChange?.({
        petCustomQuestionMap: {
          [oldVal.petId]: isNew ? newNode?.value : oldNode?.value,
        },
      });
    } else {
      onChange?.({
        pets: [
          {
            id: oldVal.petId,
            ...(isNew ? newNode?.value : oldNode?.value),
          },
        ],
      });
    }
  };

  const getCustomRenderNode = useGetCustomRenderNode();
  const getPetRenderNode = useGetPetRenderNode();

  const content = questionList
    .map((id) => questionMap.mustGetItem(id))
    .sort((a, b) => b.sort - a.sort)
    .map((item) => {
      const { isCustomQuestion, key, id } = item;

      if (key === OBPetQuestionKey.VaccineList) {
        return (
          <DiffVaccineList
            key={id}
            oldVal={oldVal}
            newVal={newVal}
            needUpdateVaccineIds={needUpdateVaccineIds}
            onChange={handleChange}
          />
        );
      }

      const oV = oldVal.questionAnswerList?.find((v) => v.key === item.key);
      const nV = newVal.questionAnswerList?.find((v) => v.key === item.key);

      const oldNode = isCustomQuestion ? getCustomRenderNode(item, oV) : getPetRenderNode(key, oldVal, oldVal);
      const newNode = isCustomQuestion ? getCustomRenderNode(item, nV) : getPetRenderNode(key, newVal, oldVal);

      return (
        <DiffItem
          key={id}
          title={item.question}
          newNode={newNode}
          oldNode={oldNode}
          onChange={(...args) => {
            handleChange(...args, isCustomQuestion);
          }}
        />
      );
    });

  return (
    <div className="empty:moe-hidden">
      {visible && <DiffHeader type="pet">Updates for {oldVal.petName || newVal.petName}</DiffHeader>}

      {visible ? <div className={cn('moe-space-y-[16px] moe-mt-[12px]')}>{content}</div> : content}
    </div>
  );
});
