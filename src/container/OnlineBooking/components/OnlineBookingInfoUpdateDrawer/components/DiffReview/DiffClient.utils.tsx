import { pick } from 'lodash';
import { type CustomerUpdateParams, type CustomerDetailType } from '../../type';
import { OBClientQuestionKey } from '../../../../../../store/onlineBooking/models/OBQuestion.base';

export const CustomerIgnoreKeys = [
  OBClientQuestionKey.PhoneNumber,
  OBClientQuestionKey.Address, // address 情况比较复杂，放到另外两个字段(primaryAddress, newAddresses)里处理了，因此这里忽略 address 字段
];

/**
 * Question Key 与 customer detail 相关字段的映射关系
 * 原则是业务依赖的场景都需要显式在代码定义清楚，避免依赖了可能会变的 question key 而埋下坑，导致不可预测的 bug 出现
 */
const questionKeyToFieldMap: Record<
  OBClientQuestionKey,
  // 交集部分可以直接赋值，非交集部分，需要通过函数来做映射
  | (keyof CustomerDetailType & keyof CustomerUpdateParams)[]
  | ((customer: CustomerDetailType) => Partial<CustomerUpdateParams>)
> = {
  [OBClientQuestionKey.FirstName]: ['firstName'],
  [OBClientQuestionKey.LastName]: ['lastName'],
  [OBClientQuestionKey.PhoneNumber]: ['phoneNumber'],
  [OBClientQuestionKey.Email]: ['email'],
  [OBClientQuestionKey.ReferralSource]: ['referralSourceId'],
  [OBClientQuestionKey.PreferredGroomer]: ['preferredGroomerId'],
  [OBClientQuestionKey.Birthday]: ['birthday'],
  [OBClientQuestionKey.PreferredFrequency]: ['preferredFrequencyDay', 'preferredFrequencyType'],
  [OBClientQuestionKey.PreferredDayOfTheWeek]: ['preferredDay'],
  [OBClientQuestionKey.PreferredTimeOfTheDay]: ['preferredTime'],
  [OBClientQuestionKey.Address]: [], // Address 单独做处理
  [OBClientQuestionKey.EmergencyContact]: [], // 业务不需要 diff，直接过
  [OBClientQuestionKey.PeopleAuthorizeToPickupPets]: [], // 业务不需要 diff，直接过
};

/**
 * 获取 diff 数据对应的改动数据的字段
 */
export const getDiffPartialCustomerDetailByQuestionKey = (
  customerDetail: CustomerDetailType,
  questionKey: OBClientQuestionKey | string,
): Partial<CustomerDetailType> => {
  const fieldsToPick = questionKeyToFieldMap[questionKey as OBClientQuestionKey];
  if (typeof fieldsToPick === 'function') {
    return fieldsToPick(customerDetail);
  } else if (Array.isArray(fieldsToPick) && fieldsToPick.length > 0) {
    return pick(customerDetail, fieldsToPick);
  } else {
    return {};
  }
};

export const getCustomerDefaultRenderNodeByValue = (value: Partial<CustomerDetailType>) =>
  Object.keys(value).length === 1 ? Object.values(value)[0] : null;
