import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from '@moego/ui';
import { useDispatch } from 'amos';
import { isUndefined, merge } from 'lodash';
import React, { useEffect, useRef } from 'react';
import { useSetState } from 'react-use';
import { Condition } from '../../../../components/Condition';
import {
  dismissReviewUpdateAction,
  getReviewUpdateDetailAction,
  submitReviewUpdateAction,
} from '../../../../store/onlineBooking/actions/private/onlineBooking.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { truly } from '../../../../store/utils/utils';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { useToggleIntercom } from '../../../../utils/hooks/useToggleIntercom';
import { mergeArray } from '../../../../utils/utils';
import { usePreloadRequestDetailExtra } from '../../hooks/usePreload';
import { ClientInfo } from './components/ClientInfo';
import { DiffClient } from './components/DiffReview/DiffClient';
import { DiffPet } from './components/DiffReview/DiffPet';
import { useFooterActions } from './hooks/useFooterActions';
import { type CustomerPetsUpdateParams, type PetDetailType, type ReviewUpdateRes, type UpdateRecordType } from './type';

interface StateType {
  data: ReviewUpdateRes;
  needUpdateVaccineIds?: Array<number>;
}

export interface OnlineBookingInfoUpdateDrawerProps extends UpdateRecordType {
  onClose: (v: boolean) => void;
  onSchedule: (v: UpdateRecordType) => void;
  showUpdateAndScheduleBtn?: boolean;
}

export const OnlineBookingInfoUpdateDrawer: React.FC<OnlineBookingInfoUpdateDrawerProps> = (props) => {
  const { onClose, showUpdateAndScheduleBtn, ticketId, customerId, bookFlowId, onSchedule } = props;
  const dispatch = useDispatch();

  const [currentState, setCurrentState] = useSetState<StateType>();
  const diffRef = useRef<CustomerPetsUpdateParams>({});

  usePreloadRequestDetailExtra();

  const fetchData = useSerialCallback(async () => {
    const { requestPets, ...rest } = await dispatch(
      getReviewUpdateDetailAction({
        customerId,
      }),
    );
    let uuid = -1; // 新增的vaccine 没有 vaccineBindingId，前端 form error 校验需要加这个
    const newVal = requestPets?.map(({ vaccineList, ...rest }) => {
      return {
        ...rest,
        vaccineList: vaccineList.map(({ vaccineBindingId, ...other }) => ({
          ...other,

          vaccineBindingId: isNormal(vaccineBindingId) ? vaccineBindingId : uuid--,
        })),
      };
    });
    setCurrentState({ data: { ...rest, requestPets: newVal || [] } });
  });

  const isExistVaccine = (petId: number, vaccineBindingId: number) => {
    const oldPets = currentState.data?.pets;
    return oldPets
      ?.find((oldPet) => oldPet.petId === petId)
      ?.vaccineList.find((item) => item.vaccineBindingId === vaccineBindingId);
  };

  const resolvePets = (removeVaccineBindingId = false) => {
    const { pets } = diffRef.current;
    // 最后，如果最终选择的pet vaccine中，有oldPet没有的，说明是被商家删除后，又新增，我们要移除 vaccineBindingId
    // 如果是c端用户新增的vaccine 是不会有这种情况的
    // 不要在之前就移除，因为这样会导致diff的时候，更新异常
    return pets?.map((pet) => {
      const vaccineList = removeVaccineBindingId
        ? pet.vaccineList?.map(({ vaccineBindingId, ...rest }) =>
            isExistVaccine(pet.id, vaccineBindingId as number) ? { vaccineBindingId, ...rest } : rest,
          )
        : pet.vaccineList;
      return {
        ...pet,
        vaccineList: vaccineList || [],
      };
    }) as CustomerPetsUpdateParams['pets']; // 这里 as 主要是因为swagger没有识别出来vaccineBindingId 是可选的，而且它层级比较深。
  };

  const sendUpdate = useLatestCallback(async () => {
    const { pets: _pets, ...rest } = diffRef.current;

    await dispatch(
      submitReviewUpdateAction({
        ...rest,
        customerId,
        pets: resolvePets(true),
      }),
    );
  });

  const handleDismiss = useSerialCallback(async () => {
    await dispatch(dismissReviewUpdateAction({ customerId }));
    onClose(true);
  });

  const handleBeforeUpdate = () => {
    const pets = resolvePets();
    const needUpdateVaccineList = pets
      ?.map((p) => p.vaccineList)
      .flat()
      .filter((v) => truly(v)) // 约束类型，remove undefined
      .filter((v) => !v.expirationDate);

    if (needUpdateVaccineList?.length) {
      setCurrentState({
        needUpdateVaccineIds: needUpdateVaccineList.map((v) => v.vaccineBindingId).filter(truly),
      });
      return true;
    }
    setCurrentState({
      needUpdateVaccineIds: [],
    });
  };

  const handleUpdate = useSerialCallback(async () => {
    const isBreak = handleBeforeUpdate();
    if (isBreak) return;
    await sendUpdate();
    onClose(true);
  });

  const handleUpdateAndSchedule = useSerialCallback(async () => {
    const isBreak = handleBeforeUpdate();
    if (isBreak) return;
    await sendUpdate();
    onClose(false);
    if (ticketId || bookFlowId) {
      onSchedule({ ticketId, customerId, bookFlowId });
    }
  });

  const handleDiffChange = (newDiff: CustomerPetsUpdateParams) => {
    const prev = diffRef.current;
    const prevPets = prev?.pets || [];
    const newPets = newDiff?.pets || [];

    const mergedPets = mergeArray(
      newPets.map((item) => {
        const prevPet = prevPets.find(({ id }) => item.id === id);
        if (prevPet) {
          const vaccineList = mergeArray(item.vaccineList || [], prevPet.vaccineList || [], 'vaccineBindingId').filter(
            (item) => item?.vaccineId,
          ); // 如果没有 vaccineId 说明是取消选中
          return {
            ...prevPet,
            ...item,
            vaccineList,
          };
        } else {
          return item;
        }
      }),
      prevPets,
      'id',
    );

    const { pets, newAddresses, ...rest } = newDiff;

    diffRef.current = {
      ...merge(prev, rest),
      pets: mergedPets,
      /**
       * 如果 newDiff 里有 newAddresses，说明是此次 diff 到了 address，不需要 merge，直接用 newDiff 内已经算好的值
       * 否则说明此次 diff 是其他字段，沿用 prev 的 newAddresses 即可
       */
      newAddresses: isUndefined(newAddresses) ? prev?.newAddresses : newAddresses,
    };
  };

  useEffect(() => {
    if (customerId) {
      fetchData();
    }
  }, [customerId]);

  const { footerActions } = useFooterActions({
    showUpdateAndScheduleBtn,
    handleUpdate,
    handleUpdateAndSchedule,
    handleDismiss,
  });

  useToggleIntercom(true);

  const { data, needUpdateVaccineIds } = currentState;

  return (
    <Drawer
      isOpen={true}
      size="l"
      title="Review updates"
      classNames={{
        footer: 'moe-px-m moe-py-s',
      }}
      isDismissable
      onClose={() => onClose(false)}
      renderActions={() => (
        <div className="moe-flex moe-items-center moe-w-full">
          <div className="moe-flex-1 moe-flex moe-items-center">
            <Condition if={footerActions.tertiary.show}>
              <Button
                variant="tertiary"
                isLoading={footerActions.tertiary.loading}
                onPress={footerActions.tertiary.onClick}
              >
                {footerActions.tertiary.text}
              </Button>
            </Condition>
          </div>
          <div className="moe-flex moe-gap-s moe-items-center">
            <Button
              className="moe-min-w-[140px] moe-px-[20px]"
              variant="secondary"
              onPress={footerActions.secondary.onClick}
              isLoading={footerActions.secondary.loading}
            >
              {footerActions.secondary.text}
            </Button>
            <Button
              className="moe-min-w-[140px] moe-px-[20px]"
              onPress={footerActions.primary.onClick}
              isLoading={footerActions.primary.loading}
            >
              {footerActions.primary.text}
            </Button>
          </div>
        </div>
      )}
    >
      <Spin isLoading={fetchData.isBusy()} classNames={{ iconContainer: 'moe-pt-[120px]' }}>
        <div className="moe-flex moe-flex-col moe-gap-m">
          {data?.customer && <ClientInfo customer={data.customer} />}
          <hr className="moe-border-t moe-border-t-divider" />
          {data?.requestCustomer && (
            <DiffClient oldVal={data.customer} newVal={data.requestCustomer} onChange={handleDiffChange} />
          )}
          {data?.requestPets?.map((item) => {
            let oldPet = data.pets.find(({ petId }) => petId === item.petId);
            if (!oldPet?.vaccineList?.length && item.vaccineList?.length) {
              // 如果旧的 pet 不存在，但是有疫苗，说明是新建的，我们强行进入 diff vaccine 的逻辑，这里的 fallback 只需要关心 数组，对象等即可保证不要报错，因为其他数据也不会有 diff
              oldPet = { petId: oldPet?.petId, petName: oldPet?.petName, vaccineList: [] } as unknown as PetDetailType;
              item = {
                petId: item.petId,
                petName: item.petName,
                vaccineList: item.vaccineList,
              } as unknown as PetDetailType;
            }

            return oldPet ? (
              <DiffPet
                key={item.petId}
                oldVal={oldPet}
                newVal={item}
                needUpdateVaccineIds={needUpdateVaccineIds}
                onChange={handleDiffChange}
              />
            ) : null;
          })}
        </div>
      </Spin>
    </Drawer>
  );
};
