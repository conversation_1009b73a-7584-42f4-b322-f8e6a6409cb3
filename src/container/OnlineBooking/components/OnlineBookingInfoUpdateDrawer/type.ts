import { type ReactNode } from 'react';
import { type OpenApiModels } from '../../../../openApi/schema';

export type ReviewUpdateRes = OpenApiModels['POST/grooming/ob/v2/client-pets-diff']['Res'];

export type CustomerPetsUpdateParams = OpenApiModels['PUT/grooming/ob/v2/client-pets']['Req'];

export type CustomerDetailType = ReviewUpdateRes['customer'];

export type PetDetailType = ReviewUpdateRes['pets'][number];

export type PetUpdateParams = Exclude<CustomerPetsUpdateParams['pets'], undefined>[number];

export type CustomerUpdateParams = Exclude<CustomerPetsUpdateParams['customer'], undefined>;

export type CustomQuestionType = PetDetailType['questionAnswerList'][0];

export type ReturnNodeAndValue = { node: ReactNode; value: any };

export type UpdateRecordType = {
  customerId: number;
  ticketId?: number;
  bookFlowId?: string;
  /**
   * 弹窗是从哪里打开的，可以用来埋点，也可以用来判断是否需要显示某些内容
   */
  source?: 'table-client-info' | 'other';
};

export type NewAddressItem = CustomerDetailType['newAddresses'][number];
