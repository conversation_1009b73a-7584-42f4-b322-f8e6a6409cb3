import {
  type MoeGoNotification,
  type NotificationRecord,
  type NotificationType,
} from '../../../../store/notification/notification.boxes';
import { type Length, type TuplifyUnion } from '../../../../types/advanced';
import { A2PFailedRenderer } from './a2pFailed.renderer';
import { A2PRequiredRenderer } from './a2pRequired.renderer';
import { AgreementSignedRenderer } from './agreementSigned.renderer';
import { AppointmentAssignedRenderer } from './appointment/assigned.renderer';
import { AppointmentCancelledRenderer } from './appointment/cancelled.renderer';
import { AppointmentCancelledByClientRenderer } from './appointment/cancelledByClient.renderer';
import { AppointmentConfirmedByClientRenderer } from './appointment/confirmedByClient.renderer';
import { AppointmentCreatedRenderer } from './appointment/created.renderer';
import { AppointmentRescheduledRenderer } from './appointment/rescheduled.renderer';
import { TasksRenderer } from './appointment/tasks.renderer';
import { AppointmentUpdatedByClientRenderer } from './appointment/updatedByClient.renderer';
import { type INotificationItemRenderer } from './base';
import { VaccineUpdateRenderer } from './clientPet/vaccineUpdate.renderer';
import { AccountingSetupReminderRenderer } from './finance/accountingSetupReminder';
import { FinanceCapitalOfferApprovedRenderer } from './finance/capitalOfferApproved';
import { FinanceCapitalOfferCreatedRenderer } from './finance/capitalOfferCreated';
import { GoogleReserveGeoMatchedRenderer } from './google/reserveGeoMatched.renderer';
import { GoogleReserveGeoUnmatchedRenderer } from './google/reserveGeoUnmatched.renderer';
import { InvoicePaidRenderer } from './invoicePaid.renderer';
import { MarketingCampaignRenderer } from './marketingCampaign.renderer';
import { MembershipPurchasedRenderer } from './membership/membershipPurchased.render';
import { MembershipPaymentFailedRenderer } from './membership/msPaymentFailed.renderer';
import { NewIntakeFormRenderer } from './newIntakeForm.renderer';
import { OBAbandonedRenderer } from './onlinenBooking/abandon.renderer';
import { OBReNewEndDateRenderer } from './onlinenBooking/renewEndDate.renderer';
import { OBRequestRenderer } from './onlinenBooking/request.renderer';
import { OBRequestCancelRenderer } from './onlinenBooking/requestCancel.renderer';
import { OBRequestRescheduleRenderer } from './onlinenBooking/requestReschedule.renderer';
import { OBUpdateClientRenderer } from './onlinenBooking/updateClient.renderer';
import { OBUpdatePetRenderer } from './onlinenBooking/updatePet.renderer';
import { PaymentDisputeRenderer } from './paymentDispute';
import { ReviewSubmittedRenderer } from './reviewSubmitted.renderer';
import { SubPaymentRenderer } from './subPayment.renderer';
import { TwilioFineRenderer } from './twilioFine.renderer';
import { WaitlistRequestRenderer } from './waitlist/waitlistRequest.renderer';
import { WaitlistSlotAvailableRenderer } from './waitlist/waitlistSlotAvailable.renderer';

type IgnoredNotificationType = 'UNKNOWN' | 'SUB_PAYMENT_FAIL';

export class NotificationItemRendererManager {
  // use length count to restrict each notification type has a renderer
  static readonly renderers: readonly INotificationItemRenderer<NotificationType>[] & {
    length: Length<TuplifyUnion<Exclude<keyof typeof MoeGoNotification, IgnoredNotificationType>>>;
  } = [
    // Appointment
    new AppointmentCreatedRenderer(),
    new AppointmentAssignedRenderer(),
    new AppointmentCancelledByClientRenderer(),
    new AppointmentCancelledRenderer(),
    new AppointmentConfirmedByClientRenderer(),
    new AppointmentRescheduledRenderer(),
    new AppointmentUpdatedByClientRenderer(),
    new GoogleReserveGeoMatchedRenderer(),
    new GoogleReserveGeoUnmatchedRenderer(),
    // OB
    new OBAbandonedRenderer(),
    new OBReNewEndDateRenderer(),
    new OBRequestCancelRenderer(),
    new OBRequestRescheduleRenderer(),
    new OBRequestRenderer(),
    new OBUpdateClientRenderer(),
    new OBUpdatePetRenderer(),

    new A2PFailedRenderer(),
    new A2PRequiredRenderer(),
    new TwilioFineRenderer(),
    new AgreementSignedRenderer(),
    new InvoicePaidRenderer(),
    new PaymentDisputeRenderer(),
    new MarketingCampaignRenderer(),
    new NewIntakeFormRenderer(),
    new ReviewSubmittedRenderer(),
    new SubPaymentRenderer(),
    new FinanceCapitalOfferApprovedRenderer(),
    new FinanceCapitalOfferCreatedRenderer(),
    new AccountingSetupReminderRenderer(),
    new TasksRenderer(),
    new WaitlistRequestRenderer(),
    new WaitlistSlotAvailableRenderer(),
    new MembershipPaymentFailedRenderer(),
    new MembershipPurchasedRenderer(),
    new VaccineUpdateRenderer(),
  ] as const;
  static getRenderer(item: NotificationRecord) {
    return this.renderers.find((renderer) => renderer.match(item));
  }
}
