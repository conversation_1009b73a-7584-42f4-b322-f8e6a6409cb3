import { appointmentDateTimeFormat } from '../../../../../container/Appt/utils/appointmentDateTimeFormat';
import { type BusinessRecord } from '../../../../../store/business/business.boxes';
import {
  type ActivityNotificationType,
  type NotificationOBRequestModel,
} from '../../../../../store/notification/notification.boxes';
import { BaseNotificationItemRenderer } from '../base';

export class OBBaseNotificationItemRenderer<
  T extends
    | ActivityNotificationType.OB_ABANDONED
    | ActivityNotificationType.OB_RENEW_END_DATE
    | ActivityNotificationType.OB_REQUEST
    | ActivityNotificationType.OB_REQUEST_CANCEL
    | ActivityNotificationType.OB_REQUEST_RESCHEDULE
    | ActivityNotificationType.CLIENT_UPDATE_PET
    | ActivityNotificationType.CLIENT_UPDATE_CUSTOMER,
> extends BaseNotificationItemRenderer<T> {
  getOBRequestDateTimeDesc = (business: BusinessRecord, data: NotificationOBRequestModel) => {
    return appointmentDateTimeFormat(business, data);
  };
}
