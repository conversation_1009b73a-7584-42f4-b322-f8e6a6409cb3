import React from 'react';
import {
  type ActivityNotificationType,
  MoeGoNotification,
  type NotificationRecord,
  type NotificationType,
} from '../../../../../store/notification/notification.boxes';
import { type INotificationRendererMeta } from '../base';
import { OBBaseNotificationItemRenderer } from './obBase.renderer';
import { Text } from '@moego/ui';
import { MinorPawsOutlined } from '@moego/icons-react';
import { T_SECOND } from 'monofile-utilities/lib/consts';

export class OBUpdatePetRenderer extends OBBaseNotificationItemRenderer<ActivityNotificationType.CLIENT_UPDATE_PET> {
  match(item: NotificationRecord<NotificationType>) {
    return item.is(MoeGoNotification.CLIENT_UPDATE_PET);
  }
  render(meta: INotificationRendererMeta<ActivityNotificationType.CLIENT_UPDATE_PET>) {
    const { item, business } = meta;
    const { customerId, customerFirstName, customerLastName, updateTime, petId, petName } = item.extra;

    return {
      icon: (
        <div className="moe-p-8px-50 moe-bg-information-subtle moe-rounded-full moe-w-[28px] moe-h-[28px]">
          <MinorPawsOutlined color="#0089FF" />
        </div>
      ),
      title: item.title || 'Pet info updated',
      body: (
        <>
          <Text variant="small" className="moe-text-tertiary">
            {customerFirstName} {customerLastName} ({petName})
          </Text>
          <Text variant="small" className="moe-text-tertiary">
            {business.formatFixedDate(updateTime * T_SECOND)}
          </Text>
        </>
      ),
      onClick: async () => {
        window.open(`/client/${customerId}/pets/${petId}`, '_blank', 'noopener noreferrer');
      },
    };
  }
}
