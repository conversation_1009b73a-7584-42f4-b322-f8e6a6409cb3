/*
 * @since 2020-11-19 11:45:49
 * <AUTHOR> <<EMAIL>>
 */

import { useSerialCallback } from '@moego/finance-utils';
import { MajorCloseOutlined } from '@moego/icons-react';
import { Badge, IconButton, Typography, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Tabs } from 'antd';
import { TabPane } from 'rc-tabs';
import React, { memo, useEffect, useState } from 'react';
import { ALL_LOCATIONS, SingleLocationSelector } from '../../../components/Business/SingleLocationSelector';
import { WithMultiLocation } from '../../../components/WithFeature/WithMultiLocation';
import { useCapitalBlocked } from '../../../container/Finance/hooks/useCapitalBlocked';
import { getNotificationUnreadCount } from '../../../store/notification/notification.actions';
import { NotificationListTab } from '../../../store/notification/notification.boxes';
import { selectNotificationUnreadCount } from '../../../store/notification/notification.selectors';
import { getFinanceRedDotStatus } from '../../../store/notification/red-dot.actions';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { notificationPopoverVisibleBox } from './Notification.boxes';
import { NotificationDropdownView } from './NotificationDropdown.style';
import { NotificationList } from './NotificationList';
import { selectBDFeatureEnable } from '../../../store/company/company.selectors';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../utils/growthBook/growthBook.config';

export interface NotificationDropdownProps {
  className?: string;
}

export const NotificationDropdown = memo<NotificationDropdownProps>(({ className }) => {
  const dispatch = useDispatch();
  const [tab, setTab] = useState(NotificationListTab.Activity);
  const [locationId, setLocationId] = useState(ALL_LOCATIONS);
  const [unreadCount, popoverVisible, isBDEnabled] = useSelector(
    selectNotificationUnreadCount(locationId),
    notificationPopoverVisibleBox,
    selectBDFeatureEnable,
  );

  const { value: isCapitalBlocked } = useCapitalBlocked();
  const enableNewClientPortalForGrooming = useFeatureIsOn(GrowthBookFeatureList.EnableNewClientPortalForGrooming);
  const enablePendingReview = enableNewClientPortalForGrooming || isBDEnabled;
  const getUnread = useSerialCallback(async () => {
    await dispatch(getNotificationUnreadCount(locationId));
  });

  const handleOpen = useLatestCallback(
    ({ dropdownVisible, selectVisible }: { dropdownVisible: boolean; selectVisible: boolean }) => {
      dispatch(
        notificationPopoverVisibleBox.setState({
          dropdownVisible,
          selectVisible,
        }),
      );
      if (dropdownVisible && !isCapitalBlocked) {
        dispatch(getFinanceRedDotStatus());
      }
    },
  );

  useEffect(() => {
    getUnread();
  }, [locationId]);

  return (
    <NotificationDropdownView className={className}>
      <div className="moe-leading-[24px] moe-h-[56px] moe-flex moe-flex-row moe-items-center moe-justify-between moe-py-8px-200 moe-px-s moe-text-[16px] moe-font-bold moe-text-primary">
        <div className="moe-flex moe-items-center">
          <Typography.Heading className="moe-mr-s" size="5">
            Notifications
          </Typography.Heading>
          <WithMultiLocation scene="working">
            <SingleLocationSelector
              showAll
              scene="working"
              isSearchable={false}
              onChange={setLocationId}
              value={locationId}
              className="moe-mr-s"
              classNames={{
                control: cn('moe-rounded-full moe-h-[30px] moe-py-[6px] moe-max-w-[176px]'),
                placeholder: cn('moe-truncate'),
              }}
              menuPortalTarget={document.body}
              isOpen={popoverVisible.selectVisible}
              onOpenChange={(open) =>
                handleOpen({
                  dropdownVisible: true,
                  selectVisible: open,
                })
              }
            />
          </WithMultiLocation>
        </div>
        <IconButton
          icon={<MajorCloseOutlined />}
          onPress={() =>
            handleOpen({
              dropdownVisible: false,
              selectVisible: false,
            })
          }
          size="l"
        />
      </div>
      <Tabs activeKey={tab} onChange={(e) => setTab(e as NotificationListTab)}>
        <TabPane
          tab={
            <div className="tab-item">
              <span>Activity</span>
              {unreadCount.activity > 0 && !getUnread.isBusy() && (
                <span className="extra">
                  <Badge.Count count={unreadCount.activity} maxCount={999} />
                </span>
              )}
            </div>
          }
          key={NotificationListTab.Activity}
        >
          <NotificationList
            locationId={locationId}
            tab={NotificationListTab.Activity}
            visible={tab === NotificationListTab.Activity}
          />
        </TabPane>
        <TabPane
          tab={
            <div className="tab-item">
              <span>System</span>
              {unreadCount.system > 0 && !getUnread.isBusy() && (
                <span className="extra">
                  <Badge.Count count={unreadCount.system} />
                </span>
              )}
            </div>
          }
          key={NotificationListTab.System}
        >
          <NotificationList
            locationId={locationId}
            tab={NotificationListTab.System}
            visible={tab === NotificationListTab.System}
          />
        </TabPane>
        {enablePendingReview ? (
          <TabPane
            tab={
              <div className="tab-item">
                <span>Pending Review</span>
                {unreadCount.pendingReview > 0 && !getUnread.isBusy() && (
                  <span className="extra">
                    <Badge.Count count={unreadCount.pendingReview} />
                  </span>
                )}
              </div>
            }
            key={NotificationListTab.PendingReview}
          >
            <NotificationList
              locationId={locationId}
              tab={NotificationListTab.PendingReview}
              visible={tab === NotificationListTab.PendingReview}
            />
          </TabPane>
        ) : (
          <></>
        )}
      </Tabs>
    </NotificationDropdownView>
  );
});
