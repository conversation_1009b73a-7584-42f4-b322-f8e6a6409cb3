import { Button, Modal } from '@moego/ui';
import React, { useRef, useState } from 'react';
import { type CreateTicketState } from '../../router/paths';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { Condition } from '../Condition';
import { useModal } from '../Modal/useModal';
import {
  AddClientWithPets,
  type AddClientWithPetsContentProps,
  type AddClientWithPetsContentRef,
} from './AddClientWithPets';

interface AddClientWithPetsModalProps
  extends Pick<
      AddClientWithPetsContentProps,
      'isShowAddAndSchedule' | 'isShowAdd' | 'from' | 'defaultValues' | 'onAdded' | 'onAddedAndSchedule'
    >,
    React.ComponentProps<typeof Modal> {
  createTicketInfo?: CreateTicketState;
  onClose?: () => void;
}

function AddClientWithPetsModal({
  from,
  defaultValues,
  createTicketInfo,
  onClose,
  isShowAdd = true,
  isShowAddAndSchedule = true,
  onAdded,
  onAddedAndSchedule,
  ...rest
}: AddClientWithPetsModalProps) {
  const ref = useRef<AddClientWithPetsContentRef>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAdded = useLatestCallback(async (clientId: number, petIdList: number[]) => {
    await onAdded?.(clientId, petIdList);
    onClose?.();
  });

  const handleAddedAndSchedule = useLatestCallback(async (clientId: number, petIdList: number[]) => {
    await onAddedAndSchedule?.(clientId, petIdList);
    onClose?.();
  });

  return (
    <Modal
      isOpen
      title="Add client"
      onClose={() => ref.current?.triggerClose()}
      classNames={{ footer: '[&_div]:moe-w-full', body: 'moe-relative' }}
      {...rest}
      footer={
        <div className="moe-w-full moe-flex moe-justify-between">
          <Button align="start" variant="tertiary-legacy" onPress={() => setIsFullscreen(true)}>
            Show more options
          </Button>
          <div className="moe-basis-0 moe-flex moe-gap-s">
            <Condition if={!isShowAdd || !isShowAddAndSchedule}>
              <Button variant="secondary" onPress={() => ref.current?.triggerClose()}>
                Cancel
              </Button>
            </Condition>
            <Condition if={isShowAdd}>
              <Button
                variant={isShowAddAndSchedule ? 'secondary' : 'primary'}
                onPress={() => ref.current?.triggerAdd()}
                isLoading={isSubmitting}
              >
                Add
              </Button>
            </Condition>
            <Condition if={isShowAddAndSchedule}>
              <Button variant="primary" onPress={() => ref.current?.triggerAddAndSchedule()} isLoading={isSubmitting}>
                Add and schedule
              </Button>
            </Condition>
          </div>
        </div>
      }
    >
      <AddClientWithPets
        ref={ref}
        onClose={onClose}
        isFullscreen={isFullscreen}
        isShowAdd={isShowAdd}
        onAdded={handleAdded}
        isShowAddAndSchedule={isShowAddAndSchedule}
        onAddedAndSchedule={handleAddedAndSchedule}
        onSubmitStatusChange={setIsSubmitting}
      />
    </Modal>
  );
}

export const useAddClientWithPetsFormModal = () => {
  return useModal(AddClientWithPetsModal);
};
