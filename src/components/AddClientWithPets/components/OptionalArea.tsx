import { MinorChevronDownOutlined } from '@moego/icons-react';
import { Text } from '@moego/ui';
import { useBoolean } from 'ahooks';
import React from 'react';

export interface OptionalAreaProps {
  title: string;
}

export const OptionalArea = (props: React.PropsWithChildren<OptionalAreaProps>) => {
  const { title, children } = props;
  const [isOpen, { toggle }] = useBoolean(false);

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
      <div className="moe-flex moe-items-center moe-cursor-default" onClick={toggle}>
        <Text variant="small">{title}</Text>
        <MinorChevronDownOutlined className={isOpen ? 'moe-rotate-180' : ''} />
      </div>
      {isOpen && <div className="moe-bg-neutral-sunken-light moe-p-[20px] moe-rounded-m">{children}</div>}
    </div>
  );
};
