import { Modal, type ModalProps } from '@moego/ui';
import React from 'react';
import type { Customer } from '@moego/bff-openapi/clients/client.leads';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useSelector } from 'amos';

interface ExistedLeadModalProps extends ModalProps {
  customer: Customer;
}

export const ExistedLeadModal = (props: ExistedLeadModalProps) => {
  const { customer } = props;
  const [business] = useSelector(selectCurrentBusiness());
  return (
    <Modal isOpen {...props} title="Convert lead" confirmText="View lead details" className="moe-w-[540px]">
      <div className="moe-text-regular">
        {`A lead with the phone number ${business.formatPhoneNumber(customer.phoneNumber)} already exists. To create a client using this number, please convert the
        lead on lead detail page.`}
      </div>
    </Modal>
  );
};
