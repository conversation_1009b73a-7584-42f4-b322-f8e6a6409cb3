import { MajorCloseOutlined } from '@moego/icons-react';
import { Anchor, Heading, IconButton, type AnchorProps } from '@moego/ui';
import cn from 'classnames';
import React, { memo, useEffect, useRef, useState } from 'react';
import { useIntersection } from 'react-use';

interface FullscreenContainerProps {
  title: string;
  children: React.ReactNode;
  onClose?: () => void;
  className?: string;
  classNames?: { base?: string; title?: string };
  actions?: React.ReactNode[];
  anchorItems: AnchorProps['items'];
  isFullscreen?: boolean;
}

/**
 * 通过 css 样式控制全屏容器的显示，保持渲染结构一致，避免 children 重新 remount，导致状态丢失
 */
export const FullscreenContainer = memo<FullscreenContainerProps>(
  ({ className, classNames, onClose, actions, children, anchorItems, title, isFullscreen }) => {
    const [isHeaderPinned, setIsHeaderPinned] = useState(false);
    const anchorContainerRef = useRef<HTMLDivElement>(null);
    const sentinelIntersectionRef = useRef<HTMLDivElement>(null);
    const sentinelIntersection = useIntersection(sentinelIntersectionRef, {
      threshold: 1,
    });
    useEffect(() => {
      if (sentinelIntersection) {
        const isHeaderPinned = sentinelIntersection.intersectionRatio < 1;
        setIsHeaderPinned(isHeaderPinned);
      }
    }, [sentinelIntersection?.intersectionRatio]);

    return (
      <div
        data-slot="fullscreen-container"
        ref={anchorContainerRef}
        className={cn(
          {
            'moe-fixed moe-top-0 moe-left-0 moe-bg-white moe-min-w-[948px] moe-w-[100vw] moe-h-[100vh] moe-pb-m moe-overflow-auto':
              isFullscreen,
          },
          className,
          classNames?.base,
        )}
      >
        <div ref={sentinelIntersectionRef}></div>
        <div
          className={cn(
            'moe-h-[72px] moe-flex moe-items-center moe-justify-between moe-px-m moe-py-s moe-bg-white moe-sticky moe-top-0 moe-z-10 moe-border-divider moe-border-solid',
            { 'moe-border-b-[1px]': isHeaderPinned, 'moe-hidden': !isFullscreen },
          )}
        >
          <IconButton size="xl" color="transparent" icon={<MajorCloseOutlined />} onPress={onClose} />
          <div className="moe-flex moe-gap-s">{actions}</div>
        </div>
        <div className={cn({ 'moe-mt-s moe-mx-auto moe-w-fit': isFullscreen })}>
          <div className={cn({ 'moe-align-top moe-inline-block moe-mr-[40px]': isFullscreen })}>
            <Heading
              size="2"
              className={cn(classNames?.title, { 'moe-hidden': !isFullscreen })}
              data-slot="fullscreen-container-title"
            >
              {title}
            </Heading>
            {children}
          </div>
          <Anchor
            className={cn('moe-w-[210px] moe-align-top moe-inline-block moe-sticky moe-top-[88px]', {
              'moe-hidden': !isFullscreen,
            })}
            getContainer={() => anchorContainerRef.current!}
            items={anchorItems}
            targetOffset={72}
          />
        </div>
      </div>
    );
  },
);
