import React from 'react';
import { NumberInputV2 } from '../../form/NumberInputV2';
import { RE_INPUT_PHONE, stripNonNumericInput } from '../../form/NumberInput';
import { cn, FormFieldSharedProps, Input, Markup, UseFormReturn } from '@moego/ui';
import { OpenApiDefinitions } from '../../../openApi/schema';
import { useLatestCallback } from '@moego/finance-utils';
import { FormItemHelpText } from '@moego/ui/dist/esm/components/Form/FormItemHelpText';

interface PickupValue {
  firstName: string;
  lastName: string;
  phoneNumber: string;
}

interface PickupProps extends Omit<FormFieldSharedProps, 'children'> {
  form: UseFormReturn<any, any>;
  className?: string;
  title: string;
  value?: PickupValue;
  onChange?: (value: PickupValue | undefined) => void;
}

export type AdditionalContactDTO = OpenApiDefinitions['customer']['com.moego.server.customer.dto.AdditionalContactDTO'];

export function toPickupAdditionalContactDTO(
  value: Partial<PickupValue> | undefined | null,
  type: number,
): AdditionalContactDTO | undefined {
  if (!value) return undefined;
  const { firstName, lastName, phoneNumber } = value;
  const dto: AdditionalContactDTO = {
    firstName: firstName?.trim() || '',
    lastName: lastName?.trim() || '',
    phone: phoneNumber?.trim() || '',
    type,
    title: '',
    id: '',
    email: '',
  };
  return dto;
}

export const Pickup = React.forwardRef<HTMLDivElement, PickupProps>((props, ref) => {
  const { value, onChange, title, className, name, form } = props;
  const { firstName = '', lastName = '', phoneNumber = '' } = value ?? {};
  const changePartial = useLatestCallback((partial: Partial<PickupValue>) => {
    const nextValue = { firstName, lastName, phoneNumber, ...partial };
    const isAllEmpty = Object.values(nextValue).every((v) => !v || v.trim().length === 0);
    onChange?.(isAllEmpty ? undefined : nextValue);
    form.trigger(name!);
  });
  const fieldState = form.getFieldState(name!);
  const { invalid, error } = fieldState;
  const invalidFirstName = invalid && (error as any)?.firstName?.message;
  const invalidLastName = invalid && (error as any)?.lastName?.message;
  const invalidPhoneNumber = invalid && (error as any)?.phoneNumber?.message;

  return (
    <div ref={ref} className={cn('moe-flex moe-flex-col moe-gap-y-xxs', className)}>
      <Markup variant="small">{title}</Markup>
      <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
        <div className="moe-flex moe-items-center moe-gap-x-[10px]">
          <div className="moe-flex-1 moe-min-w-0">
            <Input.Text
              placeholder="Enter first name"
              value={firstName}
              onChange={(firstName) => changePartial({ firstName })}
            />
            {invalidFirstName && (
              <FormItemHelpText className="moe-mt-xxs moe-whitespace-nowrap" errorMessage={invalidFirstName} />
            )}
          </div>
          <div className="moe-flex-1 moe-min-w-0">
            <Input.Text
              placeholder="Enter last name"
              value={lastName}
              onChange={(lastName) => changePartial({ lastName })}
            />
            {invalidLastName && (
              <FormItemHelpText className="moe-mt-xxs moe-whitespace-nowrap" errorMessage={invalidLastName} />
            )}
          </div>
        </div>
        <div className="moe-flex moe-items-center moe-gap-x-[10px]">
          <div className="moe-flex-1 moe-min-w-0">
            <NumberInputV2
              allowStartWithZero
              inputFormat={RE_INPUT_PHONE}
              preProcess={stripNonNumericInput}
              placeholder="Enter contact phone number"
              value={phoneNumber}
              onChange={(phoneNumber) => changePartial({ phoneNumber })}
            />
            {invalidPhoneNumber && (
              <FormItemHelpText className="moe-mt-xxs moe-whitespace-nowrap" errorMessage={invalidPhoneNumber} />
            )}
          </div>
          <div className="moe-flex-1 moe-min-w-0" />
        </div>
      </div>
    </div>
  );
});
