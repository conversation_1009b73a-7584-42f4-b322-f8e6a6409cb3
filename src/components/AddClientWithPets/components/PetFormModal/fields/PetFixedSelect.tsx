import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useUpdate } from 'react-use';
import { addPetFixed } from '../../../../../store/pet/petFixed.actions';
import { petFixedMapBox } from '../../../../../store/pet/petFixed.boxes';
import { selectBusinessPetFixeds } from '../../../../../store/pet/petFixed.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { trimText } from '../../../../../utils/utils';

interface PetFixedSelectProps extends SelectProps {}

export const PetFixedSelect = memo<PetFixedSelectProps>((props) => {
  const update = useUpdate();
  const dispatch = useDispatch();
  const [petFixedList, fixedMap] = useSelector(selectBusinessPetFixeds(), petFixedMapBox);

  const options = useMemo(() => {
    return petFixedList.toArray().map((_id) => {
      const { id, name } = fixedMap.mustGetItem(_id);
      return { key: id, value: name, label: name };
    });
  }, [petFixedList, fixedMap]);

  const handleCreatableChange = useLatestCallback((value: string, option, action) => {
    props.onChange?.(value, option, action);
    if (!option?.isCreate) return;
    return dispatch(addPetFixed({ name: trimText(value) })).then(update);
  }) as SelectProps['onChange'];

  return (
    <Select
      isCreatable
      isClearable
      options={options}
      placeholder="Type to search or add new fixed type"
      {...props}
      onChange={handleCreatableChange}
    />
  );
});

PetFixedSelect.displayName = 'PetFixedSelect';
