import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { PetGender } from '../../../../../store/pet/pet.boxes';

interface PetGenderSelectProps extends SelectProps {}

export const PetGenderSelect = memo<PetGenderSelectProps>((props) => {
  const options = useMemo(() => {
    return PetGender.values.map((item) => ({
      value: item,
      label: PetGender.mapLabels[item],
    }));
  }, []);

  return <Select options={options} isSearchable={false} isClearable placeholder="Select gender" {...props} />;
});

PetGenderSelect.displayName = 'PetGenderSelect';
