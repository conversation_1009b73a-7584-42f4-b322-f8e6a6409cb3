import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useUpdate } from 'react-use';
import { addPetHairLength } from '../../../../../store/pet/petHairLength.actions';
import { petHairLengthMapBox } from '../../../../../store/pet/petHairLength.boxes';
import { selectBusinessPetHairLengths } from '../../../../../store/pet/petHairLength.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

interface PetCoatTypeSelectProps extends SelectProps {}

export const PetCoatTypeSelect = memo<PetCoatTypeSelectProps>((props) => {
  const update = useUpdate();
  const dispatch = useDispatch();
  const [hairLengthList, hairLengthMap] = useSelector(selectBusinessPetHairLengths(), petHairLengthMapBox);

  const options = useMemo(() => {
    return hairLengthList.toArray().map((_id) => {
      const { id, name } = hairLengthMap.mustGetItem(_id);
      return { key: id, value: name, label: name };
    });
  }, [hairLengthList, hairLengthMap]);

  const handleCreatableChange = useLatestCallback((value: string, option, action) => {
    props.onChange?.(value, option, action);
    if (!option?.isCreate) return;
    dispatch(addPetHairLength({ name: value })).then(update);
  }) as SelectProps['onChange'];

  return (
    <Select
      isCreatable
      isClearable
      options={options}
      placeholder="Type to search or add new coat type"
      {...props}
      onChange={handleCreatableChange}
    />
  );
});

PetCoatTypeSelect.displayName = 'PetCoatTypeSelect';
