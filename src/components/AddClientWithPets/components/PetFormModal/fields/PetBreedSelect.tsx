import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useUpdate } from 'react-use';
import { addBreed } from '../../../../../store/pet/petBreed.actions';
import { petBreedMapBox } from '../../../../../store/pet/petBreed.boxes';
import { selectPetBreeds } from '../../../../../store/pet/petBreed.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { usePermissionCheck } from '../../../../GuardRoute/WithPermission';
import { usePetBreedCreatable } from '../usePetBreedCreatable';

interface PetBreedSelectProps extends SelectProps {
  petTypeId: number;
}

export const PetBreedSelect = memo<PetBreedSelectProps>(({ petTypeId, ...props }) => {
  const update = useUpdate();
  const dispatch = useDispatch();
  const [breedMap, petBreedList] = useSelector(petBreedMapBox, selectPetBreeds(petTypeId));
  const canEditBreed = usePermissionCheck({
    permissions: 'accessClientPetSettings',
  });

  const options = useMemo(() => {
    return petBreedList.toArray().map((_id) => {
      const { id, name } = breedMap.mustGetItem(_id);
      return { key: id, value: name, label: name };
    });
  }, [petBreedList, breedMap]);

  const handleCreatableChange = useLatestCallback((value: string, option, action) => {
    props.onChange?.(value, option, action);
    const trimmedValue = value.trim();
    if (!option?.isCreate || !trimmedValue) return;
    return dispatch(addBreed({ name: trimmedValue, petTypeId })).then(update);
  }) as SelectProps['onChange'];
  const { searchValue, setSearchValue, currentValueCanCreate } = usePetBreedCreatable(options);
  const isCreatable = isNormal(petTypeId) && canEditBreed && currentValueCanCreate;

  return (
    <Select
      isCreatable={isCreatable}
      options={options}
      placeholder="Type to search or add new breed"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      {...props}
      onChange={handleCreatableChange}
    />
  );
});

PetBreedSelect.displayName = 'PetBreedSelect';
