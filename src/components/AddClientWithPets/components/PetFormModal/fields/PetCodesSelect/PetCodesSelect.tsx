import { MinorPlusOutlined } from '@moego/icons-react';
import { Button, LegacySelect as Select, type LegacySelectProps as SelectProps, Tag } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectCurrentPermissions } from '../../../../../../store/business/role.selectors';
import { addPetCode } from '../../../../../../store/pet/petCode.actions';
import { petCodeMapBox } from '../../../../../../store/pet/petCode.boxes';
import { selectBusinessPetCodes } from '../../../../../../store/pet/petCode.selectors';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { usePetCodeFormModal } from './usePetCodeFormModal';

interface PetCodeOption {
  label: string;
  value: string | number;
  color: string;
  codeNumber: string;
}

interface PetCodesSelectProps extends SelectProps<PetCodeOption, true> {}

export const PetCodesSelect = memo<PetCodesSelectProps>((props) => {
  const dispatch = useDispatch();
  const [petCodeList, codeMap, permissions] = useSelector(
    selectBusinessPetCodes(),
    petCodeMapBox,
    selectCurrentPermissions(),
  );
  const hasManageGeneralClientAndPetSetting = permissions.has('manageGeneralClientAndPetSetting');

  const openAddPetCode = usePetCodeFormModal();

  const options = useMemo<PetCodeOption[]>(() => {
    return petCodeList.toArray().map((id) => {
      const item = codeMap.mustGetItem(id);
      return {
        color: item.color,
        codeNumber: item.codeNumber,
        value: item.id,
        label: item.description,
      };
    });
  }, [petCodeList, codeMap]);

  const handleAddPetCode = useLatestCallback(() => {
    openAddPetCode({
      onSubmitSuccess: async (formValues) => {
        await dispatch(addPetCode(formValues));
      },
    });
  });

  const formatOption = useLatestCallback((option: PetCodeOption) => {
    return (
      <div className="moe-flex moe-items-center moe-gap-[10px]">
        <Tag color={option.color} label={option.codeNumber} variant="filled" />
        <span className="ml-2">{option.label}</span>
      </div>
    );
  });

  const footer = useMemo(
    () =>
      hasManageGeneralClientAndPetSetting ? (
        <Button icon={<MinorPlusOutlined />} variant="tertiary-legacy" onPress={handleAddPetCode}>
          Add
        </Button>
      ) : null,
    [handleAddPetCode, hasManageGeneralClientAndPetSetting],
  );

  return (
    <Select<PetCodeOption, true>
      isClearable
      isMultiple
      multipleMode="tag"
      options={options}
      footer={footer}
      formatOptionLabel={formatOption}
      formatOptionTitle={formatOption}
      placeholder="Type to search or add new pet code"
      {...props}
    />
  );
});

PetCodesSelect.displayName = 'PetCodesSelect';
