import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import { createFilter } from '@moego/ui/dist/esm/components/LegacySelect/ReactSelect/filters';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { currentAccountIdBox } from '../../../../../store/account/account.boxes';
import { petMapBox } from '../../../../../store/pet/pet.boxes';
import { PetTypeRecord, petTypeMapBox } from '../../../../../store/pet/petType.boxes';
import { selectBusinessAvailablePetTypes } from '../../../../../store/pet/petType.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

interface PetTypeSelectProps extends SelectProps {
  petId?: number;
}

export const PetTypeSelect = memo<PetTypeSelectProps>(({ petId, ...rest }) => {
  const [pet, petTypeList, typeMap, currentAccountId] = useSelector(
    petMapBox.mustGetItem(petId!),
    selectBusinessAvailablePetTypes,
    petTypeMapBox,
    currentAccountIdBox,
  );

  const hasUnknownTypeOption =
    pet?.petTypeId && !petTypeList.includes(PetTypeRecord.ownId(pet.petTypeId, currentAccountId));
  const unknownTypeOption = useMemo(
    () => ({
      value: pet?.petTypeId!,
      label: pet?.typeName!,
    }),
    [pet?.petTypeId, pet?.typeName],
  );
  const typeFilterOption = useLatestCallback((option, inputValue) => {
    const searchResult = createFilter()(option, inputValue);
    if (!hasUnknownTypeOption) return searchResult;
    return searchResult && unknownTypeOption.value !== +option.value;
  });
  const typeOptions = useMemo(() => {
    const options = petTypeList
      .toArray()
      .filter((id) => typeMap.mustGetItem(id).isAvailable)
      .map((id) => {
        const item = typeMap.mustGetItem(id);
        return {
          value: item.petTypeId,
          label: item.typeName,
        };
      });
    if (hasUnknownTypeOption) {
      options.push(unknownTypeOption);
    }
    return options;
  }, [petTypeList, typeMap, hasUnknownTypeOption, unknownTypeOption]);

  return <Select options={typeOptions} filterOption={typeFilterOption} placeholder="Select type" {...rest} />;
});

PetTypeSelect.displayName = 'PetTypeSelect';
