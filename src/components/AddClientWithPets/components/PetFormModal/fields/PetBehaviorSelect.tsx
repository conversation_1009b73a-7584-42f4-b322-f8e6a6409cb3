import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useUpdate } from 'react-use';
import { addPetBehavior } from '../../../../../store/pet/petBehavior.actions';
import { petBehaviorMapBox } from '../../../../../store/pet/petBehavior.boxes';
import { selectBusinessPetBehaviors } from '../../../../../store/pet/petBehavior.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { trimText } from '../../../../../utils/utils';

interface PetBehaviorSelectProps extends SelectProps {}

export const PetBehaviorSelect = memo<PetBehaviorSelectProps>((props) => {
  const update = useUpdate();
  const dispatch = useDispatch();
  const [petBehaviorList, behaviorMap] = useSelector(selectBusinessPetBehaviors(), petBehaviorMapBox);

  const options = useMemo(() => {
    return petBehaviorList.toArray().map((_id) => {
      const { id, name } = behaviorMap.mustGetItem(_id);
      return { key: id, value: name, label: name };
    });
  }, [petBehaviorList, behaviorMap]);

  const handleCreatableChange = useLatestCallback((value: string, option, action) => {
    props.onChange?.(value, option, action);
    if (!option?.isCreate) return;
    return dispatch(addPetBehavior({ name: trimText(value) })).then(update);
  }) as SelectProps['onChange'];

  return (
    <Select
      isCreatable
      isClearable
      options={options}
      placeholder="Type to search or add new behavior"
      {...props}
      onChange={handleCreatableChange}
    />
  );
});

PetBehaviorSelect.displayName = 'PetBehaviorSelect';
