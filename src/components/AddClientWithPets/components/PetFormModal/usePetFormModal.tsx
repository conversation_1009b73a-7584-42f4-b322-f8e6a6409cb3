import { AlertDialog, Modal, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useMemo } from 'react';
import { useMount, useToggle } from 'react-use';
import { addPet, getPetOptions, updatePet } from '../../../../store/pet/pet.actions';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useModal } from '../../../Modal/useModal';
import { dirtyCheckConfig } from '../../utils';
import { type PetFormFields, usePetForm } from './usePetForm';

interface PetFormModalProps extends React.ComponentProps<typeof Modal> {
  clientId?: number;
  petId?: number;
  onAdded?: (petId: number, inputs: PetFormFields) => void | Promise<void>;
}

export const PetFormModal = ({ clientId, petId = ID_ANONYMOUS, onClose, onAdded, ...rest }: PetFormModalProps) => {
  const isEdit = isNormal(petId);
  const dispatch = useDispatch();
  const [pet] = useSelector(petMapBox.mustGetItem(petId!));
  const defaultValues = useMemo(() => (isEdit ? pet.toJSON() : void 0), [pet]);
  const [showMore, toggleShowMore] = useToggle(isEdit);
  const { form, dom } = usePetForm({ lessMode: !showMore, defaultValues });
  const { isDirty, isSubmitting } = useFormState(form);

  useMount(() => {
    dispatch(getPetOptions());
  });

  const handleClose = useLatestCallback(() => {
    if (isDirty) {
      AlertDialog.open({
        ...dirtyCheckConfig,
        title: 'Pet has unsaved changes',
        onCancel: onClose,
      });
    } else {
      onClose?.();
    }
  });

  const handleSubmit = useLatestCallback(() => {
    return form.handleSubmit(async (fieldValues) => {
      if (isEdit) {
        await dispatch(updatePet({ ...fieldValues, id: petId }));
      } else {
        const res = await dispatch(addPet({ ...fieldValues, customerId: clientId }));
        await onAdded?.(res.data, fieldValues);
      }
      onClose?.();
    })();
  });

  return (
    <Modal
      isOpen
      isMaskCloseable={false}
      className="moe-w-[720px]"
      classNames={{ body: 'moe-pt-xs' }}
      title={isEdit ? 'Edit pet' : 'Add pet'}
      showTertiaryButton={!isEdit}
      tertiaryText={showMore ? 'Show less options' : 'Show more options'}
      onTertiary={toggleShowMore}
      confirmText={isEdit ? 'Save' : 'Add'}
      onClose={handleClose}
      {...rest}
      autoCloseOnConfirm={false}
      onConfirm={handleSubmit}
      confirmButtonProps={{ isDisabled: !isDirty, isLoading: isSubmitting }}
    >
      {dom}
    </Modal>
  );
};

export const usePetFormModal = () => {
  return useModal(PetFormModal);
};
