import { useMemo, useState } from 'react';

export const usePetBreedCreatable = (list: { label: string }[]) => {
  const [searchValue, setSearchValue] = useState('');
  const alreadyExists = useMemo(() => {
    return list.some((item) => item.label.trim().toLowerCase() === searchValue.trim().toLowerCase());
  }, [list, searchValue]);
  return {
    searchValue,
    setSearchValue,
    currentValueCanCreate: searchValue.length > 0 && !alreadyExists,
  };
};
