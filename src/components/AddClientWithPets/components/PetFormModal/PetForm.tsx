import { forwardRef, useImperativeHandle } from 'react';
import { type UsePetFormProps, usePetForm } from './usePetForm';

interface PetFormProps extends UsePetFormProps {}

export interface PetFormRef {
  form: ReturnType<typeof usePetForm>['form'];
}

/**
 * 需要确保 dispatch(getPetOptions()) 已经在外部调用
 */
export const PetForm = forwardRef<PetFormRef, PetFormProps>((props, ref) => {
  const { form, dom } = usePetForm(props);
  /**
   * formState is wrapped with a Proxy to improve render performance and skip extra logic if specific state is not subscribed to. Therefore make sure you invoke or read it before a render in order to enable the state update.
   *
   * 需要在 render 之前读取 formState.isDirty，以便启用状态更新
   * 而 ref 使用注定是在 render 之后才能被调用，所以需要在 render 之前读取 formState.isDirty
   * 后续才能在函数里通过  ref.current?.form.formState.isDirty 获取到最新的值
   */
  form.formState.isDirty;
  useImperativeHandle(ref, () => ({ form }), [form]);
  return dom;
});
