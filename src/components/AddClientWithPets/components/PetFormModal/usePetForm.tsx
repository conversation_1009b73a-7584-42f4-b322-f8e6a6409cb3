import { Checkbox, DatePicker, Form, Input, Text, transformer, useForm, useWatch, yupResolver } from '@moego/ui';
import { useSelector } from 'amos';
import classNames from 'classnames';
import dayjs, { type Dayjs } from 'dayjs';
import { pick } from 'lodash';
import React, { useEffect, useMemo } from 'react';
import * as Yup from 'yup';

import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectBDFeatureEnable } from '../../../../store/company/company.selectors';
import { petBreedMapBox } from '../../../../store/pet/petBreed.boxes';
import { selectPetBreeds } from '../../../../store/pet/petBreed.selectors';
import { printAge } from '../../../../utils/DateTimeUtil';
import { filterNilValues } from '../../../../utils/filterNilValues';
import { Condition } from '../../../Condition';
import { AvatarUpload } from '../../../Upload/AvatarUpload';
import { Show } from '../../../form/Show';
import { emptyToUndefinedWrapper, transformers } from '../../../form/transformers';
import { PetBehaviorSelect } from './fields/PetBehaviorSelect';
import { PetBreedSelect } from './fields/PetBreedSelect';
import { PetCoatTypeSelect } from './fields/PetCoatTypeSelect';
import { PetCodesSelect } from './fields/PetCodesSelect/PetCodesSelect';
import { PetFixedSelect } from './fields/PetFixedSelect';
import { PetGenderSelect } from './fields/PetGenderSelect';
import { PetTypeSelect } from './fields/PetTypeSelect';

const emptyAndDateStringToDayjsTransformer = emptyToUndefinedWrapper(transformer.dateStringToDayjs);

export const petValidateSchema = Yup.object().shape({
  petId: Yup.number(),
  avatarPath: Yup.string().default(''),
  petName: Yup.string().required('Name is required'),
  breed: Yup.string().required('Breed is required'),
  breedMix: Yup.number().default(0),
  petTypeId: Yup.number().required('Type is required'),
  hairLength: Yup.string().default(''),
  fixed: Yup.string().default(''),
  birthday: Yup.string().default(''),
  behavior: Yup.string().default(''),
  gender: Yup.number().default(0),
  petCodeIdList: Yup.array(Yup.number().required()).default([]),
  weight: Yup.string().default(''),
});

const petFormFieldKeys = Object.keys(petValidateSchema.fields);

export type PetFormFields = Yup.InferType<typeof petValidateSchema>;

export type PetFormFieldsList = Array<keyof PetFormFields>;

export interface UsePetFormProps {
  lessMode?: boolean;
  className?: string;
  defaultValues?: Partial<PetFormFields>;
  excludeNames?: PetFormFieldsList;
}

const resolver = yupResolver(petValidateSchema);

/**
 * 需要确保 dispatch(getPetOptions()) 已经在外部调用
 */
export const usePetForm = ({
  className,
  lessMode,
  defaultValues: defaultValuesProp,
  excludeNames = [],
}: UsePetFormProps = {}) => {
  const defaultValues = useMemo(() => filterNilValues(pick(defaultValuesProp, petFormFieldKeys)), [defaultValuesProp]);
  const form = useForm({
    resolver,
    defaultValues: defaultValues as PetFormFields,
    mode: 'onBlur',
  });
  const [petId, petTypeId, birthday] = useWatch({ control: form.control, name: ['petId', 'petTypeId', 'birthday'] });

  const [business, enableBD, breedMap, breedList] = useSelector(
    selectCurrentBusiness,
    selectBDFeatureEnable,
    petBreedMapBox,
    selectPetBreeds(petTypeId),
  );

  useEffect(() => {
    // 修改 petTypeId 后，检查 breed 是否在列表中，不在则重置为默认值
    const breed = form.getValues('breed');
    const isIncludeBreedList = breedList.some((v) => breedMap.mustGetItem(v).name === breed);
    if (!isIncludeBreedList) {
      const defaultBreed = breedList.size > 0 ? breedMap.mustGetItem(breedList.get(0)!).name : '';
      form.setValue('breed', (defaultBreed || void 0)!);
      form.clearErrors('breed');
    }
  }, [petTypeId]);

  const dom = (
    <Form
      form={form}
      footer={null}
      className={classNames('moe-grid moe-grid-cols-2 moe-gap-y-m moe-gap-x-[16px]', className)}
    >
      <div
        className={classNames('moe-col-span-full', { 'moe-hidden': lessMode || excludeNames.includes('avatarPath') })}
      >
        <Form.Item name="avatarPath" valuePropName="src">
          <AvatarUpload />
        </Form.Item>
      </div>
      <Form.Item name="petName" label="Name">
        <Input isRequired placeholder="Enter pet name" />
      </Form.Item>
      <Show if={!excludeNames.includes('gender')}>
        <Form.Item name="gender" label="Gender" transformer={transformers.emptyToUndefined}>
          <PetGenderSelect />
        </Form.Item>
      </Show>

      <Form.Item name="petTypeId" label="Type" transformer={transformers.emptyToUndefined}>
        <PetTypeSelect isRequired petId={petId} />
      </Form.Item>
      <div className="moe-flex moe-gap-s">
        <Form.Item name="breed" label="Breed" transformer={transformers.emptyToUndefined}>
          <PetBreedSelect
            isRequired
            petTypeId={petTypeId}
            classNames={{ formItemWrapper: 'moe-flex-1' }}
            className="moe-min-w-0"
          />
        </Form.Item>
        <Form.Item name="breedMix" label="&nbsp;" valuePropName="isSelected" transformer={transformer.numberToBoolean}>
          <Checkbox className="moe-flex-shrink-0 moe-flex-grow-0">Mixed</Checkbox>
        </Form.Item>
      </div>
      <Show if={!excludeNames.includes('weight')}>
        <Form.Item name="weight" label="Weight">
          <Input.Number
            precision={2}
            classNames={{ suffix: 'moe-text-regular-short' }}
            suffix={business.unitOfWeight}
            placeholder="Enter weight"
          />
        </Form.Item>
      </Show>
      <Show if={!excludeNames.includes('hairLength')}>
        <Form.Item name="hairLength" label="Coat type" transformer={transformers.emptyToUndefined}>
          <PetCoatTypeSelect />
        </Form.Item>
      </Show>
      <Show if={!lessMode}>
        <Form.Item name="fixed" label="Spayed/Neutered" transformer={transformers.emptyToUndefined}>
          <PetFixedSelect />
        </Form.Item>
      </Show>
      <Show if={!lessMode && !excludeNames.includes('birthday')}>
        <div className="moe-flex-1 moe-flex moe-items-end moe-gap-x-s">
          <Form.Item<string, Dayjs> name="birthday" label="Birthday" transformer={emptyAndDateStringToDayjsTransformer}>
            <DatePicker
              className="moe-flex-1"
              classNames={{ input: 'moe-w-0' }}
              isClearable
              format={business.dateFormat}
            />
          </Form.Item>
          <Condition if={birthday}>
            <Text variant="regular-short" as="div" className="moe-shrink-0 moe-h-[40px] moe-flex moe-items-center">
              {printAge(dayjs(birthday))}
            </Text>
          </Condition>
        </div>
      </Show>
      <Show
        if={!lessMode && !excludeNames.includes('behavior')}
        className={classNames({ 'moe-col-span-full': !enableBD })}
      >
        <Form.Item name="behavior" label="Behavior" transformer={transformers.emptyToUndefined}>
          <PetBehaviorSelect />
        </Form.Item>
      </Show>
      <Show if={!lessMode && !excludeNames.includes('petCodeIdList')}>
        <Form.Item name="petCodeIdList" label="Pet codes">
          <PetCodesSelect />
        </Form.Item>
      </Show>
    </Form>
  );

  return {
    form,
    dom,
  };
};
