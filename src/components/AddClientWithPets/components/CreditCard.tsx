import {
  MajorCardOutlined,
  <PERSON><PERSON><PERSON><PERSON>Filled,
  MinorErrorFilled,
  MinorQuestionFilled,
  MinorTrashOutlined,
} from '@moego/icons-react';
import { IconButton, Tooltip, Typography } from '@moego/ui';
import React, { memo } from 'react';
import { CardAuthStatus } from '../../../store/payment/payment.boxes';
import { Condition } from '../../Condition';
import { BaseCard, type BaseProps, CardIconGroup } from './BaseCard';

export interface CreditCardProps extends BaseProps {
  name: string | undefined | null;
  last4: string | undefined | null;
  /** ACH has no expiredDate */
  expiredDate?: string;
  brandIcon?: React.ReactNode;
  /** should show auth status icon */
  authEnabled?: boolean;
  /** required when authEnabled is true */
  authStatus?: number;
  /** credit card extra info */
  extra?: React.ReactNode | (() => React.ReactNode);
  /** customize suffix */
  suffix?: React.ReactNode | (() => React.ReactNode);

  // events
  handleDelete?: () => void;
}

/**
 * credit card rendering for stripe & square & ACH
 */
export const CreditCard = memo<CreditCardProps>((props) => {
  const {
    className,
    name,
    expiredDate,
    brandIcon,
    last4,
    authEnabled,
    authStatus,
    extra,
    suffix,
    handleDelete,
    ...restProps
  } = props;

  const renderBrandIcon = () => {
    if (!brandIcon) {
      return <IconButton className="moe-w-[48px] moe-h-[48px]" icon={brandIcon ? brandIcon : <MajorCardOutlined />} />;
    }

    return <div className="moe-w-[48px] moe-h-[48px]">{brandIcon}</div>;
  };

  const renderAuthStatus = () => {
    if (!authEnabled) {
      return null;
    }

    const { title } = CardAuthStatus.mapLabels[authStatus || CardAuthStatus.Unknown];
    const icon =
      authStatus === CardAuthStatus.Succeeded ? (
        <MinorCheckFilled color="var(--moe-color-icon-success)" />
      ) : authStatus === CardAuthStatus.Failed ? (
        <MinorErrorFilled color="var(--moe-color-icon-danger)" />
      ) : (
        <MinorQuestionFilled color="var(--moe-color-icon-warning)" />
      );

    return (
      <Tooltip content={title} side="top">
        {icon}
      </Tooltip>
    );
  };

  const renderSuffixAction = () => {
    if (suffix) {
      return typeof suffix === 'function' ? suffix() : suffix;
    }

    return handleDelete ? (
      <CardIconGroup>
        <IconButton icon={<MinorTrashOutlined />} tooltip="Delete" onPress={handleDelete} />
      </CardIconGroup>
    ) : null;
  };

  return (
    <BaseCard className={className} {...restProps}>
      <div className="moe-flex moe-items-center moe-gap-s">
        {renderBrandIcon()}
        <div data-slot="credit-info">
          <div className="moe-flex moe-gap-xs moe-items-center">
            <span className="moe-flex moe-gap-xxs moe-items-center">
              {Array.from({ length: 4 }).map((_, index) => (
                <svg key={index} xmlns="http://www.w3.org/2000/svg" width="3" height="4" viewBox="0 0 3 4" fill="none">
                  <circle cx="1.5" cy="2" r="1.5" fill="#202020" />
                </svg>
              ))}
            </span>
            <Typography.Heading className="moe-inline-block" size="5">
              {last4}
            </Typography.Heading>

            {renderAuthStatus()}
          </div>

          <div className="moe-text-tertiary">
            <Typography.Text as="span" className="moe-mt-xxs" variant="caption">
              {expiredDate ? `Exp. ${expiredDate}` : ''}
            </Typography.Text>
            <Condition if={name}>
              <Typography.Text as="span" variant="caption">
                ({name})
              </Typography.Text>
            </Condition>
          </div>

          <Condition if={extra}>{typeof extra === 'function' ? extra() : extra}</Condition>
        </div>
      </div>

      {renderSuffixAction()}
    </BaseCard>
  );
});
