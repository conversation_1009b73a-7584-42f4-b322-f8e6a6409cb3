import { DatePicker, Form, type UploadItem, transformer, useForm, yupResolver } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useState } from 'react';
import * as Yup from 'yup';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { MoeGoUIUpload } from '../../../Upload/MoeGoUIUpload';
import { VaccineName } from './fields/VaccineName';
import { VaccineNameSelect } from './fields/VaccineNameSelect';

const validateSchema = Yup.object().shape({
  vaccineId: Yup.number().required('Vaccine name is required'),
  expirationDate: Yup.string().required('Expiration date is required'),
  documentUrls: Yup.array() as Yup.Schema<UploadItem[]>,
});

export type VaccineFormFields = Yup.InferType<typeof validateSchema>;

const resolver = yupResolver(validateSchema);

export const useVaccineForm = () => {
  const [business] = useSelector(selectCurrentBusiness);
  const [tagText, setTag] = useState('');
  const [petId, setPetId] = useState(0);
  const form = useForm({
    resolver,
  });
  const setTagText = (tag: string) => {
    setTag(tag);
  };
  const isAddNotUpdateOne = !tagText; // 表示的是client&pet => pet => add vaccine ，而不是drawer => update missing vaccine

  const dom = (
    <Form form={form} footer={null} className="moe-flex moe-flex-col moe-gap-y-m moe-gap-x-[24px]">
      <Form.Item label="Vaccine name" name="vaccineId">
        {isAddNotUpdateOne ? <VaccineNameSelect isRequired petId={petId} /> : <VaccineName tagText={tagText} />}
      </Form.Item>

      <Form.Item label="Expiration date" name="expirationDate" transformer={transformer.dateStringToDayjs}>
        <DatePicker isRequired format={business.dateFormat} />
      </Form.Item>
      <Form.Item name="documentUrls">
        <MoeGoUIUpload
          label="Upload Document"
          isMultiple
          enableDrop
          maxSize={10 << 20}
          uploadDescription="Supported format: .jpg, .png, doc, docx and .pdf."
          accept=".doc,.docx,.pdf,image/*"
        />
      </Form.Item>
    </Form>
  );

  return {
    dom,
    form,
    setTagText,
    setPetId,
  };
};
