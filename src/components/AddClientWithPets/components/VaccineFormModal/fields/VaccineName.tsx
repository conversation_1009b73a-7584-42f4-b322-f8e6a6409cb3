import { type LegacySelectProps, Tag } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { petVaccineMapBox } from '../../../../../store/pet/petVaccine.boxes';
import { selectBusinessPetVaccines } from '../../../../../store/pet/petVaccine.selectors';
import { VACCINE_TEXT_EXPIRED } from '../../../../../store/pet/petVaccine.utils';

interface VaccineNameProps extends LegacySelectProps {
  tagText?: string;
}

export const VaccineName = memo<VaccineNameProps>((props) => {
  const [vaccineList, vaccineMap] = useSelector(selectBusinessPetVaccines, petVaccineMapBox);
  const { tagText } = props;

  const isWillExpire = tagText === VACCINE_TEXT_EXPIRED;
  const options = useMemo(() => {
    return vaccineList.toArray().map((id) => {
      const vaccine = vaccineMap.mustGetItem(id);
      return {
        value: vaccine.id,
        label: vaccine.name,
      };
    });
  }, [vaccineList, vaccineMap]);

  const name = options.find((item) => {
    return item.value === props.value;
  })?.label;

  return (
    <div>
      <span className="moe-font-bold">Vaccine name: </span>
      <span className="moe-mr-[8px]">{name}</span>
      {tagText ? (
        <Tag
          color={isWillExpire ? '#DE921F' : '#F3413B'}
          isBordered={false}
          fillColor={isWillExpire ? '#FFF7F0' : '#FFF0EE'}
          label={tagText}
        />
      ) : null}
    </div>
  );
});

VaccineName.displayName = 'VaccineName';
