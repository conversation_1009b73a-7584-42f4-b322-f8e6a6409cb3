import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';

import { isUndefined } from 'lodash';
import React, { memo, useMemo } from 'react';
import { useBoolean, useMount, useUpdate } from 'react-use';
import { petMapBox } from '../../../../../store/pet/pet.boxes';
import { addPetVaccine, getPetVaccineList } from '../../../../../store/pet/petVaccine.actions';
import { petVaccineMapBox } from '../../../../../store/pet/petVaccine.boxes';
import { selectBusinessPetVaccines } from '../../../../../store/pet/petVaccine.selectors';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { usePermissionCheck } from '../../../../GuardRoute/WithPermission';

interface VaccineNameSelectProps extends SelectProps {
  petId?: number;
}

export const VaccineNameSelect = memo<VaccineNameSelectProps>((props) => {
  const update = useUpdate();
  const store = useStore();
  const dispatch = useDispatch();
  const [vaccineList, vaccineMap] = useSelector(selectBusinessPetVaccines, petVaccineMapBox);
  const { petId } = props;
  // 拥有 Client & Pet Setting 权限才能新增疫苗
  const haveClientAndPetSettingPermission = usePermissionCheck({
    permissions: 'accessClientPetSettings',
  });

  const options = useMemo(() => {
    const totalList = vaccineList.toArray().map((id) => {
      const vaccine = vaccineMap.mustGetItem(id);
      return {
        value: vaccine.id,
        label: vaccine.name,
        onlyForSpecificPetType: vaccine.onlyForSpecificPetType,
        availablePetTypes: vaccine.availablePetTypes,
      };
    });
    return isUndefined(petId)
      ? totalList
      : totalList.filter((item) => {
          const petTypeMap = store.select(petMapBox.mustGetItem(petId));
          return item.onlyForSpecificPetType ? item.availablePetTypes.includes(petTypeMap.petTypeId) : true;
        });
  }, [vaccineList, vaccineMap, petId]);

  const [isCreating, setIsCreating] = useBoolean(false);

  const isLoadingVaccineList = useBool(false);

  useMount(async () => {
    isLoadingVaccineList.open();
    await dispatch(getPetVaccineList());
    isLoadingVaccineList.close();
  });

  const handleCreatableChange = useLatestCallback(async (value: string, option, action) => {
    if (option?.isCreate) {
      setIsCreating(true);
      const { id } = await dispatch(addPetVaccine({ name: value }));
      setIsCreating(false);
      update();
      props.onChange?.(id, option, action);
    } else {
      props.onChange?.(value, option, action);
    }
  }) as SelectProps['onChange'];

  return (
    <Select
      isLoading={isLoadingVaccineList.value || isCreating}
      isCreatable={haveClientAndPetSettingPermission}
      isClearable
      options={options}
      placeholder={`Type to search ${haveClientAndPetSettingPermission ? 'or add new vaccine' : ''}`}
      {...props}
      onChange={handleCreatableChange}
    />
  );
});

VaccineNameSelect.displayName = 'VaccineNameSelect';
