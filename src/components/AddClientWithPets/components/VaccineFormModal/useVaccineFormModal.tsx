import { AlertDialog, Modal, UploadStatus, toast, useFormState, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useMemo } from 'react';
import { useMount } from 'react-use';
import { useGetCalendarEvents } from '../../../../container/Calendar/latest/ApptCalendar/hooks/useFullCalendarEvents';
import { addVaccineBinding, updateVaccineBinding } from '../../../../store/pet/petVaccineBinding.actions';
import { petVaccineBindingMapBox } from '../../../../store/pet/petVaccineBinding.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { getSearch, memoQuery } from '../../../../utils/utils';
import { useModal } from '../../../Modal/useModal';
import { toastApi } from '../../../Toast/Toast';
import { generateUID } from '../../../Upload/MoeGoUIUpload';
import { dirtyCheckConfig } from '../../utils';
import { useVaccineDelete } from './useVaccineDelete';
import { type VaccineFormFields, useVaccineForm } from './useVaccineForm';

interface VaccineFormModalProps extends React.ComponentProps<typeof Modal> {
  petId: number;
  vaccineBindingId?: number;
  vaccineId?: number;
  tagText?: string;
}
const VaccineFormModal = ({ petId, vaccineBindingId, onClose, vaccineId, tagText, ...rest }: VaccineFormModalProps) => {
  const isEdit = isNormal(vaccineBindingId);
  const isAddNotUpdateOne = !tagText;
  const [_defaultVaccine] = useSelector(petVaccineBindingMapBox.getItem(vaccineBindingId));
  const { reloadAppts } = useGetCalendarEvents();
  const defaultVaccine = useMemo(
    () =>
      isEdit
        ? _defaultVaccine?.toJSON()
        : isAddNotUpdateOne
          ? void 0
          : {
              vaccineBindingId: vaccineBindingId ?? ID_ANONYMOUS,
              petId: petId ?? ID_ANONYMOUS,
              vaccineId: vaccineId ?? 0,
              expirationDate: '',
              documentUrls: [],
            },
    [_defaultVaccine],
  );
  const { form, dom, setTagText, setPetId } = useVaccineForm();

  useMount(() => {
    setPetId(petId);
    setTagText(tagText || '');
    form.reset({
      ...defaultVaccine,
      expirationDate: defaultVaccine?.expirationDate?.trim?.()?.length ? defaultVaccine?.expirationDate : undefined,
      documentUrls:
        defaultVaccine?.documentUrls?.map((url, index) => ({
          uid: generateUID(index),
          status: UploadStatus.success,
          url,
          name: memoQuery(getSearch(url)).name || new URL(url).pathname.split('/').pop(),
        })) || [],
    });
  });

  const dispatch = useDispatch();

  const { isDirty, isSubmitting } = useFormState(form);

  const documentUrls = useWatch({ control: form.control, name: 'documentUrls' });
  const isUploading = documentUrls?.some(({ status }) => status === UploadStatus.uploading);

  const handleClose = () => {
    if (isDirty) {
      AlertDialog.open({
        ...dirtyCheckConfig,
        title: 'Vaccine has unsaved changes',
        onCancel: onClose,
      });
    } else {
      onClose?.();
    }
  };

  const handleSubmit = async (fieldValues: VaccineFormFields) => {
    const documentUrls = (fieldValues.documentUrls?.map(({ url }) => url).filter(Boolean) as string[]) || [];
    if (!vaccineBindingId || vaccineBindingId === ID_ANONYMOUS) {
      await dispatch(addVaccineBinding({ ...fieldValues, documentUrls, petId }));
    } else {
      await dispatch(updateVaccineBinding({ ...fieldValues, documentUrls, vaccineBindingId }));
    }
    toastApi.success(`Vaccine saved successfully`);
    reloadAppts(); // 这里刷新calendar是为了让appt card vaccine icon 中数据更新，与drawer同步
    onClose?.();
  };

  const handleConfirm = useLatestCallback(() => {
    return form.handleSubmit(handleSubmit)();
  });

  const deleteVaccine = useVaccineDelete();
  const handleDelete = useSerialCallback(async () => {
    if (!isNormal(vaccineBindingId)) return;
    deleteVaccine(vaccineBindingId, {
      onSuccess: () => {
        toast({ type: 'success', title: 'Vaccine deleted!' });
        onClose?.();
      },
    });
  });

  return (
    <Modal
      isOpen
      isMaskCloseable={false}
      className="moe-w-[540px]"
      classNames={{ body: 'moe-pt-xs' }}
      title={`${isEdit ? 'Update' : 'Add'} vaccine`}
      confirmText={isEdit ? 'Save' : 'Add'}
      showTertiaryButton={isEdit}
      tertiaryText="Delete"
      onTertiary={handleDelete}
      tertiaryButtonProps={{ isLoading: handleDelete.isBusy() }}
      onClose={handleClose}
      {...rest}
      autoCloseOnConfirm={false}
      onConfirm={handleConfirm}
      confirmButtonProps={{ isDisabled: !isDirty || isUploading, isLoading: isSubmitting }}
    >
      {dom}
    </Modal>
  );
};

export const useVaccineFormModal = () => {
  return useModal(VaccineFormModal);
};
