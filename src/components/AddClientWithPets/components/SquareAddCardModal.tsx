import { Form, Input, Modal, Spin, cn } from '@moego/ui';
import { captureException } from '@sentry/browser';
import { payments, type Card, type TokenResult } from '@square/web-sdk';
import { useDispatch, useSelector } from 'amos';
import { ANY } from 'monofile-utilities/lib/consts';
import React, { memo, useEffect, useRef } from 'react';
import { useAsync, useSetState } from 'react-use';
import { customerMapBox } from '../../../store/customer/customer.boxes';
import { addSquareCard } from '../../../store/square/actions/public/square.actions';
import { selectSquareAccount } from '../../../store/square/square.selectors';
import { nullable } from '../../../store/utils/utils';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { toastApi } from '../../Toast/Toast';

export interface SquareAddCardModalProps {
  className?: string;
  clientId: number;
  // we cannot use single instance for square cannot clean up the form after submit
  onClose: () => void;
}

export const SquareAddCardModal = memo<SquareAddCardModalProps>(({ className, onClose, clientId }) => {
  const dispatch = useDispatch();
  const [customer, square] = useSelector(customerMapBox.mustGetItem(clientId), selectSquareAccount());
  const inputRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<Card>();
  const [state, setState] = useSetState({
    name: customer.fullName(),
    squareReady: nullable<boolean>(),
  });

  useEffect(() => {
    setState({ name: customer.fullName() });
  }, [customer]);

  useAsync(async () => {
    if (square.isReady()) {
      try {
        const Payments = await payments(square.appId, square.defaultLocationId);
        if (!Payments) {
          toastApi.error(`Load Square SDK failed, please try again later.`);
          return;
        }
        const card = await Payments.card();
        await card.attach(inputRef.current!);
        cardRef.current = card;
        setState({ squareReady: true });
      } catch (e) {
        setState({ squareReady: false });
        captureException(e);
        toastApi.error(e + '');
      }
    }
  }, [square]);

  useEffect(() => {
    cardRef.current?.destroy();
  }, []);

  const handleSubmit = useSerialCallback(async () => {
    if (!cardRef.current) {
      return;
    }
    let token: TokenResult;
    try {
      token = await cardRef.current!.tokenize();
    } catch (e) {
      captureException(e);
      toastApi.error(e + '');
      return;
    }
    if (token.status !== 'OK' || !token.token) {
      captureException({ ...token.errors, name: 'SquareTokenError' });
      toastApi.error(
        token.errors?.map((c) => ('field' in c ? c.field + ': ' + c.message : c.message)).join('\n') || token.status,
      );
      return;
    }
    await dispatch(
      addSquareCard({
        customerId: clientId,
        cardNonce: token.token,
        cardHolderName: state.name,
        billingAddress: ANY,
      }),
    );
    toastApi.success('The card is added successfully!');
    onClose();
  });

  return (
    <Modal
      isOpen
      className={cn('moe-w-[540px]')}
      classNames={{
        body: 'moe-pb-0',
      }}
      title="Add card on file (Square)"
      isMaskCloseable={false}
      autoCloseOnConfirm={false}
      confirmText="Add"
      onClose={onClose}
      onConfirm={handleSubmit}
    >
      <Spin
        classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
        isLoading={!square.isReady() || !state.squareReady}
      >
        <Form.Label label="Name on the card" />
        <Input
          className="moe-mt-xxs"
          placeholder="Name on the card"
          value={state.name}
          onChange={(v) => setState({ name: v })}
        ></Input>

        <Form.Label className="moe-mt-l" isRequired label="Card number" />
        <div ref={inputRef} className="moe-mt-xxs" />
      </Spin>
    </Modal>
  );
});

export const useSquareAddCardModal = (clientId: number) => {
  const { mountModal } = useFloatableHost();

  return useLatestCallback((props?: SquareAddCardModalProps) => {
    const { promise, closeFloatable: closeModal } = mountModal(
      <SquareAddCardModal {...props} clientId={clientId} onClose={() => closeModal()} />,
    );

    return promise;
  });
};
