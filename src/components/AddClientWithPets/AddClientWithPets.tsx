import { FNK_PaymentMethodTypeEnum } from '@moego/finance-web-kit';
import { Button } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { useEffect, useImperativeHandle } from 'react';
import { useBeforeUnload } from 'react-use';
import { type AddCustomerWithPetsInput, addCustomerWithPets } from '../../store/customer/customer.actions';
import { AutoMessageKinds } from '../../store/customer/customer.boxes';
import { addRecurringPaymentMethod } from '../../store/payment/actions/private/payment.actions';
import { isUndefinedOrNull } from '../../utils/common';
import { type SerialCallback, useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { unSavedDoubleConfirmV2 } from '../../utils/hooks/useUnsavedConfirmV2';
import { memoForwardRef } from '../../utils/react';
import { useDefaultLocationValue } from '../Business/hooks/useLocationValue';
import { FullscreenContainer } from './components/FullscreenContainer';
import {
  type DefaultClientWithPetsFields,
  anchorItems,
  useAddClientWithPetsForm,
} from './hooks/useAddClientWithPetsForm';
import { AddClientFromApiSourceMap, dirtyCheckConfig } from './utils';
import { toPickupAdditionalContactDTO } from './components/Pickup';
import { CustomerContactKinds } from '../../store/customer/customerContact.boxes';

export interface AddClientWithPetsContentRef {
  triggerClose: SerialCallback<() => void>;
  triggerAdd: SerialCallback<() => Promise<void>>;
  triggerAddAndSchedule: SerialCallback<() => Promise<void>>;
}

export interface AddClientWithPetsContentProps {
  from?: number;
  isFullscreen?: boolean;
  defaultValues?: DefaultClientWithPetsFields;
  onClose?: () => void;
  isShowAdd?: boolean;
  onAdded?: (customerId: number, petIdList: number[]) => void | Promise<void>;
  isShowAddAndSchedule?: boolean;
  onAddedAndSchedule?: (customerId: number, petIdList: number[]) => void | Promise<void>;
  onSubmitStatusChange?: (isSubmitting: boolean) => void;
}

export const AddClientWithPets = memoForwardRef<AddClientWithPetsContentRef, AddClientWithPetsContentProps>(
  (
    {
      onClose,
      onAdded,
      onAddedAndSchedule,
      onSubmitStatusChange,
      isShowAdd = true,
      isShowAddAndSchedule = true,
      defaultValues,
      from,
      isFullscreen,
    },
    ref,
  ) => {
    const dispatch = useDispatch();
    const { form, dom, getIsDirty } = useAddClientWithPetsForm({ lessMode: !isFullscreen, defaultValues });
    const computedAnchorItems = anchorItems.filter((item) => {
      if ('shouldDisplay' in item) return item.shouldDisplay();
      return true;
    });

    useBeforeUnload(getIsDirty, 'You have unsaved changes, are you sure?');

    useDefaultLocationValue({ scene: 'accessClient', preferred: 'current' }, (value) => {
      form.setValue('preferredBusinessId', value);
    });

    const handleSubmit = (onSuccess: (customerId: number, petIdList: number[]) => Promise<void>) => {
      return form.handleSubmit(async (fieldValues) => {
        const { petList, autoMessage, card, pickupContact: pickup, emergencyContact: em, ...rest } = fieldValues;
        const emergencyContact = toPickupAdditionalContactDTO(em, CustomerContactKinds.Emergency);
        const pickupContact = toPickupAdditionalContactDTO(pickup, CustomerContactKinds.Pickup);
        const emergencyRest: AddCustomerWithPetsInput = emergencyContact ? { emergencyContact } : {};
        const pickupRest: AddCustomerWithPetsInput = pickupContact ? { pickupContact } : {};

        const res = await dispatch(
          addCustomerWithPets({
            ...rest,
            ...pickupRest,
            ...emergencyRest,
            sendAutoEmail: autoMessage?.includes(AutoMessageKinds.Email) ? 1 : 0,
            sendAutoMessage: autoMessage?.includes(AutoMessageKinds.Message) ? 1 : 0,
            petList: petList as AddCustomerWithPetsInput['petList'],
            source: from ? AddClientFromApiSourceMap.mapLabels[AddClientFromApiSourceMap.Abandon] : undefined,
            referralSourceId: isUndefinedOrNull(rest.referralSourceId) ? undefined : Number(rest.referralSourceId),
          }),
        );
        await onSuccess(res.data.id, res.data.petIdList);
        // payment v2 才有 card 字段，需要分两步添加 COF
        if (card) {
          await dispatch(
            addRecurringPaymentMethod({
              customerId: res.data.id.toString(),
              paymentMethodType: FNK_PaymentMethodTypeEnum.Card,
              detail: { card: { ...card.token, saveCard: true } },
              extra: { card: { alias: card.cardName } },
            }),
          );
        }
      })();
    };

    const handleAdd = useSerialCallback(async () => {
      await handleSubmit(async (id, petIdList) => {
        if (onAdded) {
          await onAdded(id, petIdList);
        }
      });
    });

    const handleAddAndSchedule = useSerialCallback(async () => {
      await handleSubmit(async (id, petIdList) => {
        if (onAddedAndSchedule) {
          await onAddedAndSchedule(id, petIdList);
        }
      });
    });

    const handleCancel = useSerialCallback(async () => {
      if (getIsDirty()) {
        await unSavedDoubleConfirmV2(void 0, onClose, dirtyCheckConfig);
      } else {
        onClose?.();
      }
    });

    useImperativeHandle(ref, () => ({
      triggerClose: handleCancel,
      triggerAdd: handleAdd,
      triggerAddAndSchedule: handleAddAndSchedule,
    }));

    const isSubmitting = handleAdd.isBusy() || handleAddAndSchedule.isBusy();
    useEffect(() => {
      onSubmitStatusChange?.(isSubmitting);
    }, [isSubmitting]);

    const cancelBtn = (
      <Button key="cancel" variant="secondary" onPress={handleCancel}>
        Cancel
      </Button>
    );
    const addBtn = isShowAdd && (
      <Button
        key="add"
        isLoading={isSubmitting}
        variant={isShowAddAndSchedule ? 'secondary' : 'primary'}
        onPress={handleAdd}
      >
        Add
      </Button>
    );
    const addAndScheduleBtn = isShowAddAndSchedule && (
      <Button key="addAndSchedule" isLoading={isSubmitting} variant="primary" onPress={handleAddAndSchedule}>
        Add and schedule
      </Button>
    );
    const actions = [cancelBtn, addBtn, addAndScheduleBtn].filter(Boolean);

    return (
      <FullscreenContainer
        isFullscreen={isFullscreen}
        title="Add client"
        classNames={{ title: 'moe-mb-xl' }}
        onClose={handleCancel}
        actions={actions}
        anchorItems={computedAnchorItems}
      >
        {dom}
      </FullscreenContainer>
    );
  },
);

AddClientWithPets.displayName = 'AddClientWithPets';
