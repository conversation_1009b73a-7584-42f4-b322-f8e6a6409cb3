import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { Button } from '../Button/Button';
import { addClientFilterList, removeAllClientFilter } from '../../store/customer/clientFilters.action';
import { type AddFilterModel, type ClientFilterListSource } from '../../store/customer/clientFilters.boxes';
import { type CustomerSmartListReq } from '../../store/customer/customer.boxes';
import { selectOnlineBookingPreference } from '../../store/onlineBooking/onlineBookingPreference.selectors';
import { jsonParse } from '../../utils/utils';
import {
  AllFilterList,
  paymentUniquePropertyList,
} from '../../container/Client/ClientList/componentsLegacy/ClientFilters/AllFilterList';
import { OperatorKeyMap } from '../../container/Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.config';
import { useClientFilterListParams } from '../../container/Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.hooks';
import { transformFilterValues } from '../../container/Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.utils';
import { ClientGroupsDrawerContainer } from './ClientGroupsDrawer.style';

interface ClientGroupsDrawerProps {
  visible: boolean;
  source: ClientFilterListSource;
  onClose: () => void;
  onSave: (filterListParams: CustomerSmartListReq['filters']) => void;
}

export const ClientGroupsDrawer = memo<ClientGroupsDrawerProps>(({ visible, source, onClose, onSave }) => {
  const filterListParams = useClientFilterListParams(source);
  const dispatch = useDispatch();
  const [preference] = useSelector(selectOnlineBookingPreference());

  useEffect(() => {
    const { filters } = jsonParse<{ filters?: AddFilterModel[] }>(preference.certainGroupSettingAcceptRule, {});
    if (filters) {
      const filterList = filters
        .map((filter) => {
          return {
            ...filter,
            operator: filter.operator ? OperatorKeyMap[filter.operator] : '',
            source: source,
          };
        })
        .map((filter) => transformFilterValues(filter))
        .filter((item) => !!item.operator);
      dispatch(addClientFilterList(filterList));
    } else {
      dispatch(removeAllClientFilter(source));
    }
  }, [source, preference, dispatch]);

  const handleSave = () => {
    onClose();
    onSave?.(filterListParams);
  };

  const handelClearFilter = () => {
    dispatch(removeAllClientFilter(source));
  };

  return (
    <ClientGroupsDrawerContainer
      title="Client groups"
      visible={visible}
      onClose={onClose}
      footer={
        <div className="moe-flex moe-justify-between">
          <Button
            className="!moe-border-none !moe-p-0 !moe-text-[#333] !moe-font-bold"
            btnType="white-border"
            onClick={handelClearFilter}
          >
            Clear
          </Button>
          <div className="moe-flex moe-gap-x-[12px]">
            <Button
              className="!moe-px-[30px] !moe-py-[8px] !moe-text-[#333] !moe-font-bold"
              btnType="white-border"
              buttonRadius="circle"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              className="!moe-px-[30px] !moe-py-[8px] !moe-font-bold"
              btnType="primary"
              buttonRadius="circle"
              onClick={handleSave}
            >
              Apply
            </Button>
          </div>
        </div>
      }
    >
      <AllFilterList className="!moe-p-0" source={source} extraPropertyFilterList={paymentUniquePropertyList} />
    </ClientGroupsDrawerContainer>
  );
});
