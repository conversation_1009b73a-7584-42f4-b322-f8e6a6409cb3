import React, { useEffect, useMemo, useState } from 'react';
import { InputPanel } from '../form/PanelCheckbox';
import { type CustomerSmartList } from '../../store/customer/customer.boxes';
import { type ClientFilterListSource } from '../../store/customer/clientFilters.boxes';
import { useBool } from '../../utils/hooks/useBool';
import { jsonParse } from '../../utils/utils';
import { getFilterSelectedDesc } from '../../container/Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.utils';
import { ClientGroupsDrawer } from './ClientGroupsDrawer';

interface Props {
  source: ClientFilterListSource;
  value?: string;
  onChange?: (newValue: string) => void;
}

type FiltersType = CustomerSmartList['Req']['filters'];

export const ExistingClientsFiltersSelect = ({ source, value, onChange }: Props) => {
  const [filters, setFilters] = useState<FiltersType>();
  const groupDrawerVisible = useBool();

  const panelTitle = useMemo(() => {
    return filters ? getFilterSelectedDesc(filters.filters, 'criterion', 'criteria') : 'All existing clients';
  }, [filters]);

  const handleOpenGroupDrawer = () => {
    groupDrawerVisible.open();
  };

  const handleCloseGroupDrawer = () => {
    groupDrawerVisible.close();
  };

  useEffect(() => {
    if (value !== undefined) {
      // Controlled
      const rule = jsonParse<Exclude<FiltersType | {}, undefined>>(value, {});
      setFilters('filters' in rule ? rule : undefined);
    }
  }, [value]);

  const onChangeInternal = (newFilters?: FiltersType) => {
    onChange?.(newFilters ? JSON.stringify(newFilters) : '{}');
    if (value === undefined) {
      // Uncontrolled
      setFilters(newFilters);
    }
  };

  return (
    <>
      <InputPanel
        panelTitle={panelTitle}
        placeholder="Select client groups"
        onClickPanel={handleOpenGroupDrawer}
        className="moe-h-[40px] !moe-mt-[8px] !moe-text-[16px]"
      />
      <ClientGroupsDrawer
        visible={groupDrawerVisible.value}
        source={source}
        onClose={handleCloseGroupDrawer}
        onSave={onChangeInternal}
      />
    </>
  );
};
