import { type HTMLAttributes } from 'react';
import { type ChannelEnum } from './components/ChannelSelect/ChannelSelect';
import type { ImagePickerButtonProps } from './components/ImagePickerButton';
import { type PicInfo } from './components/SendImageModal/types';

export interface SendMessageOptions {
  method?: ChannelEnum;
}

export interface MessageSendBoxProps
  extends Pick<ImagePickerButtonProps, 'allowCloseModalAfterImageSendSuccess'>,
    HTMLAttributes<HTMLDivElement> {
  customerId: number;
  customerName?: string;
  phoneNumber?: string;
  onSendText: (text: string, options: SendMessageOptions) => Promise<void>;
  onSendImage: (url: string, options: SendMessageOptions) => Promise<void>;
  onSendMultipleImages?: (list: PicInfo[], options: SendMessageOptions) => Promise<number>; // number 为消耗的 mms 数量，乘以 MULTIPLE_IMAGES_COST_SMS_COUNT 就是 sms 数量
  maxLength?: number;
  allowAppt?: boolean;
  /** upcoming appt whether use appt variable */
  allowApptVariable?: boolean;
  /**
   * 目标对象是customer还是leads，会影响部分variable是否被隐藏
   * @default:false
   **/
  isLead?: boolean;
  allowAgreement?: boolean;
  /**
   * whether use agreement variable
   * @default true
   * */
  allowAgreementVariable?: boolean;
  /**
   * @description 是否支持 pet parent app 的 two way 通信
   * @default false
   */
  allowPpaTwoWay?: boolean;
  /**
   * @description 是否显示右上角的 sms tooltip 组件
   * @default true
   */
  allowSmsTooltip?: boolean;
  /**
   * @description 是否展示消息调度功能
   * @default true
   */
  allowScheduledMessage?: boolean;
  /**
   * @description 是否展示 channel 选择
   * @default true
   */
  allowChannelSelect?: boolean;
  allowPetParentPortal?: boolean;
  allowPetParentPortalVariable?: boolean;
  allowPetParentAppDownload?: boolean;
  allowPetParentAppDownloadVariable?: boolean;
  allowOnlineBooking?: boolean;
  allowOnlineBookingVariable?: boolean;
  allowIntakeForm?: boolean;
  allowIntakeFormVariable?: boolean;
  allowRequestCof?: boolean;
  allowImage?: boolean;
  allowEmoji?: boolean;
  allowVariableBtn?: boolean;
  allowMessageTemplate?: boolean;
  sendType?: 'massText' | 'twoWay';
  massTextClientsCount?: number;
  shareAppt?: boolean;
  lapsedRemindDay?: string;
  simplified?: boolean;
  messagePlaceholder?: string;
  allowRequestCofVariable?: boolean;
  allowSellMembership?: boolean;
  allowMembershipVariable?: boolean;
  defaultChannel?: ChannelEnum;
  showSpecialLinksCopyVariable?: boolean;
  setTextAreaHeightRecord?: (height: number) => void;
}
