/* eslint-disable sonarjs/no-nested-conditional */
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Method } from '@moego/api-web/moego/models/message/v1/message_enums';
import { MinorClockOutlined } from '@moego/icons-react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button as <PERSON><PERSON><PERSON><PERSON><PERSON>, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useAsync } from 'react-use';
import { MessageTestIds } from '../../config/testIds/message';
import { BrandedAppAvailablePopup } from '../../container/BrandedApp/BrandedAppAvailablePopup';
import { stripeAvailableCountryOption } from '../../container/CardProcessing/stripe/StripeSetup';
import { insertText } from '../../container/settings/Agreement/components/EditTemplate';
import { getReminderContent } from '../../store/autoMessage/autoMessage.actions';
import { AutoReminderType } from '../../store/autoMessage/autoReminder.boxes';
import { useBrandedAppConfig } from '../../store/branded/branded.hooks';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { getCustomerUpcomingUrl } from '../../store/grooming/grooming.actions';
import { isNormal } from '../../store/utils/identifier';
import { useBool } from '../../utils/hooks/useBool';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { renderCountableNounPlurals } from '../../utils/utils';
import { Condition } from '../Condition';
import { modalApi } from '../Modal/Modal';
import { WithSMSLimitUpgrade } from '../Pricing/WithLimitComponents';
import { WithPricingEnableUpgrade } from '../Pricing/WithPricingComponents';
import { WithMessageSendContext } from './MessageSendContext';
import { AssistantList } from './components/AssistantList';
import { ChannelEnum, ChannelSelect, type ChannelSelectRef } from './components/ChannelSelect/ChannelSelect';
import { EmojiPickerButton } from './components/EmojiPickerButton';
import { ImagePickerButton } from './components/ImagePickerButton';
import { MessageCostTooltip } from './components/MessageCostTooltip';
import { MessageTemplateButton } from './components/MessageTemplateButton';
import {
  ALLOW_TEXT_LENGTH_TWOWAY,
  TEXT_MAX_LENGTH,
  checkIfMatchAnyVariable,
  getSmsSegments,
} from './components/MessageUtils';
import { ModalScheduleMessage, OperateType } from './components/ScheduledMessage/ModalScheduleMessage';
import { ScheduledMessageDrawer, ScheduledMessageSource } from './components/ScheduledMessage/ScheduleMessageDrawer';
import { SpecialLinks } from './components/SpecialLinks/SpecialLinks';
import { VariableButton } from './components/VariableButton';
import { useHasMembershipPermission } from './components/hooks/useHasMembershipPermission';
import { type MessageSendBoxProps } from './type';

const { MessageInput, MessageSendBtn, MessageScheduleBtn } = MessageTestIds;

type StoreType = Record<number, Partial<Record<ChannelEnum, string>>>;

// 需要和 MessageSendBox.scss 中 input-wrapper 的 maxHeight 相同，若修改需要同步修改
const MAX_HEIGHT_FOR_TEXTAREA = 140;

class CustomerMessageStore {
  constructor() {
    this.map = {};
  }
  map: StoreType = {};
  get(customerId: number, channel: ChannelEnum | void) {
    if (!this.map[customerId]) {
      return '';
    }
    if (!channel) {
      return '';
    }
    return this.map[customerId][channel] || '';
  }
  set(customerId: number, channel: ChannelEnum | void, text: string) {
    if (!this.map[customerId]) {
      this.map[customerId] = {};
    }
    if (!channel) {
      return;
    }
    this.map[customerId][channel] = text;
  }
}

export const MessageSendBox = memo<MessageSendBoxProps>(
  ({
    className,
    customerId,
    customerName,
    phoneNumber,
    onSendText,
    onSendImage,
    onSendMultipleImages,
    allowAppt = isNormal(customerId),
    allowApptVariable = true,
    allowAgreement = isNormal(customerId),
    allowAgreementVariable = true,
    allowPetParentPortal = isNormal(customerId),
    allowPetParentPortalVariable = true,
    allowPetParentAppDownload = !allowPetParentPortal,
    allowPetParentAppDownloadVariable = true,
    allowOnlineBooking = isNormal(customerId),
    allowOnlineBookingVariable = true,
    allowIntakeForm = isNormal(customerId),
    allowIntakeFormVariable = true,
    allowRequestCof = isNormal(customerId),
    allowRequestCofVariable = true,
    allowPpaTwoWay = false,
    allowSmsTooltip = true,
    allowScheduledMessage = true,
    allowSellMembership = isNormal(customerId),
    allowMembershipVariable = true,
    allowChannelSelect = true,
    allowImage = true,
    allowEmoji = true,
    allowVariableBtn = true,
    allowMessageTemplate = true,
    allowCloseModalAfterImageSendSuccess,
    maxLength = TEXT_MAX_LENGTH,
    sendType,
    massTextClientsCount,
    shareAppt = false,
    lapsedRemindDay,
    defaultChannel,
    showSpecialLinksCopyVariable = false,
    setTextAreaHeightRecord,
    isLead = false,
    ...rest
  }) => {
    const disableInput = useBool(false);
    const { current: messageStore } = useRef<CustomerMessageStore>(new CustomerMessageStore());
    const inputRef = useRef<HTMLTextAreaElement>(null);

    const [textContent, _setTextContent] = useState('');
    const isTextContentEmpty = textContent.trim().length === 0;
    const dispatch = useDispatch();
    const [business] = useSelector(selectCurrentBusiness);
    const showDialog = useBool(false);
    const channelSelectRef = useRef<ChannelSelectRef>(null);

    const [channelType, setChannelType] = useState(channelSelectRef.current?.getCurrentValue() || defaultChannel);
    const setTextContent = useLatestCallback((text: string) => {
      messageStore.set(customerId, channelType, text);
      _setTextContent(text);
    });
    const hasMembershipPermission = useHasMembershipPermission('sellMembership');
    const hasAccessMembershipPermission = useHasMembershipPermission('accessMembership');
    const { petParentDownloadLink } = useBrandedAppConfig();

    allowAppt = channelType !== ChannelEnum.ppa && allowAppt;
    allowPetParentPortal = channelType !== ChannelEnum.ppa && allowPetParentPortal;
    allowPetParentAppDownload = channelType !== ChannelEnum.ppa && allowPetParentAppDownload;
    allowSmsTooltip = channelType === ChannelEnum.sms && allowSmsTooltip;

    const focusInput = () => {
      if (sendType === 'twoWay' && channelType === ChannelEnum.ppa && !allowPpaTwoWay) {
        return;
      }
      inputRef.current?.focus();
    };

    const { remainLength: smsRemainLength, smsSegmentLength } = useMemo(
      () => getSmsSegments(textContent.trim(), ALLOW_TEXT_LENGTH_TWOWAY),
      [textContent],
    );
    const handleInsertText = useLatestCallback(async (text: string) => {
      await Promise.resolve(); // 防止光标跳到行首
      setTextContent(insertText(text, inputRef.current));
    });
    const handleSendText = useLatestCallback(() => {
      onSendText(textContent.trim(), {
        method: channelType,
      }).then(() => {
        setTextContent('');
      });
    });

    const handleAdoptAnswer = useLatestCallback((answer: string) => {
      focusInput();
      inputRef.current?.select();
      document.execCommand('insertText', false, answer);
      messageStore.set(customerId, channelType, answer);
    });

    useEffect(() => {
      _setTextContent(messageStore.get(customerId, channelType));
      focusInput();
      autoExtend();
    }, [customerId, channelType]);

    useAsync(async () => {
      if (lapsedRemindDay === undefined) {
        return;
      }
      try {
        disableInput.open();
        const lapsedRemindText = await dispatch(getReminderContent(customerId, AutoReminderType.Rebook));
        setTextContent(insertText(lapsedRemindText, inputRef.current));
        focusInput();
      } catch (error) {
        console.log('error :>> ', error);
      } finally {
        disableInput.close();
      }
    }, [lapsedRemindDay]);

    useEffect(() => {
      if (shareAppt) {
        dispatch(getCustomerUpcomingUrl(customerId)).then((res) => setTextContent(insertText(res, inputRef.current)));
      }
    }, [shareAppt]);

    const hasVariables = useMemo(() => checkIfMatchAnyVariable(textContent), [textContent]);

    const autoExtend = () => {
      if (!inputRef.current) {
        return;
      }

      // 通过赋值auto触发重排，获取最新的scrollHeight
      inputRef.current.style.height = 'auto';

      const scrollHeight = inputRef.current.scrollHeight;
      inputRef.current.style.height = `${scrollHeight}px`;
      const textAreaHeight = scrollHeight > MAX_HEIGHT_FOR_TEXTAREA ? MAX_HEIGHT_FOR_TEXTAREA : scrollHeight;
      setTextAreaHeightRecord?.(textAreaHeight);
    };

    const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      autoExtend();
      setTextContent(e.target.value);
    };

    const ScheduledMessage = () => {
      if (!allowScheduledMessage) return null;
      const method: Method = channelType === ChannelEnum.ppa ? Method.APP : Method.SMS;

      return (
        <>
          <WithPricingEnableUpgrade permission="scheduledMessage">
            <IconButton
              className="moe-mr-[7px]"
              variant="primary"
              size="m"
              color="transparent"
              icon={<MinorClockOutlined />}
              tooltip="Schedule for later"
              onPress={showDialog.open}
              data-testid={MessageScheduleBtn}
            />
          </WithPricingEnableUpgrade>
          <ModalScheduleMessage
            customerId={customerId}
            customerName={customerName || ''}
            phoneNumber={phoneNumber || ''}
            visible={showDialog.value}
            content={{ content: textContent }}
            handleClose={() => {
              _setTextContent('');
              showDialog.close();
            }}
            opType={OperateType.Create}
            method={method}
          />
        </>
      );
    };

    // 添加对 command + enter / ctrl + enter 的支持，根据 isTextContentEmpty 判断是否触发
    useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        if (!isTextContentEmpty && e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
          handleSendText();
        }
      };
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }, [isTextContentEmpty, handleSendText]);

    return (
      <div className={cn('send-box-wrapper', className)} {...rest}>
        <ScheduledMessageDrawer from={ScheduledMessageSource.USER} customerId={customerId} phoneNumber={phoneNumber} />
        <div className="send-box moe-relative moe-overflow-hidden moe-pb-[16px]">
          {allowChannelSelect && (
            <BrandedAppAvailablePopup step={1}>
              <ChannelSelect
                className="moe-ml-[16px]"
                ref={channelSelectRef}
                storageKey={customerId}
                key={customerId}
                onValueChange={(value) => {
                  if (sendType === 'twoWay' && value === ChannelEnum.ppa && !allowPpaTwoWay) {
                    inputRef.current?.blur();
                  }
                  setChannelType(value);
                  _setTextContent(messageStore.get(customerId, value));
                }}
                defaultChannel={defaultChannel}
              />
            </BrandedAppAvailablePopup>
          )}
          <div className={cn('input-wrapper', !allowChannelSelect && '!moe-pt-[16px]')}>
            <textarea
              disabled={disableInput.value}
              value={textContent}
              maxLength={maxLength}
              placeholder="Write a message"
              onChange={handleTextChange}
              className={cn(
                allowChannelSelect ? 'moe-min-h-[30px]' : 'moe-min-h-[70px]',
                'moe-font-manrope moe-font-normal moe-text-base-24',
              )}
              ref={inputRef}
              data-testid={MessageInput}
            />
            <Condition if={textContent.length > 0 && allowSmsTooltip}>
              <MessageCostTooltip smsRemainLength={smsRemainLength} smsSegmentLength={smsSegmentLength} />
            </Condition>
          </div>
          <AssistantList
            customerId={customerId + ''}
            sendType={sendType}
            onAdoptAnswer={handleAdoptAnswer}
            prompt={textContent.trim()}
          ></AssistantList>
          <div className="widget-wrapper">
            <div className="widget-btn-group">
              {allowImage && (
                <ImagePickerButton
                  allowCloseModalAfterImageSendSuccess={allowCloseModalAfterImageSendSuccess}
                  onSelect={(url) => {
                    return onSendImage?.(url, {
                      method: channelType,
                    });
                  }}
                  onSelectMultiple={
                    onSendMultipleImages
                      ? (list) => {
                          return onSendMultipleImages?.(list, {
                            method: channelType,
                          });
                        }
                      : undefined
                  }
                />
              )}
              {allowEmoji && <EmojiPickerButton onSelect={handleInsertText} />}
              {allowVariableBtn && <VariableButton isLead={isLead} onSelect={handleInsertText} />}
              {allowMessageTemplate && <MessageTemplateButton onSelect={handleInsertText} channel={channelType} />}
              <SpecialLinks
                customerId={customerId}
                allowOnlineBooking={allowOnlineBooking}
                allowOnlineBookingVariable={allowOnlineBookingVariable}
                allowAgreement={allowAgreement}
                allowAgreementVariable={allowAgreementVariable}
                allowAppt={allowAppt}
                allowApptVariable={allowApptVariable}
                allowIntakeForm={allowIntakeForm}
                allowIntakeFormVariable={allowIntakeFormVariable}
                allowPetParentPortal={allowPetParentPortal}
                allowPetParentPortalVariable={allowPetParentPortalVariable}
                allowPetParentAppDownload={allowPetParentAppDownload}
                allowPetParentAppDownloadVariable={allowPetParentAppDownloadVariable}
                allowMembership={allowSellMembership && hasMembershipPermission && hasAccessMembershipPermission}
                allowMembershipVariable={allowMembershipVariable}
                allowRequestCof={allowRequestCof && !!stripeAvailableCountryOption(business.country)}
                allowRequestCofVariable={allowRequestCofVariable}
                onSelect={handleInsertText}
                showCopy={showSpecialLinksCopyVariable}
                channel={channelType}
              />
            </div>
            <div className="right-btn-group">
              {!sendType ? (
                <WithSMSLimitUpgrade canUnlimited={false}>
                  <>
                    <ScheduledMessage />
                    <MoeGoButton
                      isDisabled={isTextContentEmpty}
                      onPress={handleSendText}
                      size="s"
                      data-testid={MessageSendBtn}
                    >
                      Send
                    </MoeGoButton>
                  </>
                </WithSMSLimitUpgrade>
              ) : sendType === 'massText' ? (
                <WithPricingEnableUpgrade permission="massText">
                  {(onCapture1) => (
                    <WithSMSLimitUpgrade canUnlimited={false}>
                      {(onCapture2) => (
                        <>
                          <MoeGoButton
                            isDisabled={isTextContentEmpty}
                            onPress={
                              onCapture1 ||
                              onCapture2 ||
                              (() => {
                                const clients = massTextClientsCount ?? 1;
                                const smsCost = smsSegmentLength * clients;
                                modalApi.confirm({
                                  type: 'success',
                                  content: `Message will be sent to ${renderCountableNounPlurals(
                                    clients,
                                    'recipient',
                                  )}. ${renderCountableNounPlurals(
                                    smsCost,
                                    'SMS credit',
                                  )} will be used. Are you sure to proceed? `,
                                  okText: 'Confirm',
                                  onOk: handleSendText,
                                });
                              })
                            }
                            size="s"
                            data-testid={MessageSendBtn}
                          >
                            Send
                          </MoeGoButton>
                        </>
                      )}
                    </WithSMSLimitUpgrade>
                  )}
                </WithPricingEnableUpgrade>
              ) : sendType === 'twoWay' ? (
                <WithPricingEnableUpgrade permission="twoWay">
                  {(capture1) => (
                    <WithSMSLimitUpgrade canUnlimited={false}>
                      {(capture2) => (
                        <>
                          <ScheduledMessage />
                          <MoeGoButton
                            isDisabled={isTextContentEmpty}
                            onPress={capture1 || capture2 || handleSendText}
                            size="s"
                            data-testid={MessageSendBtn}
                          >
                            Send
                          </MoeGoButton>
                        </>
                      )}
                    </WithSMSLimitUpgrade>
                  )}
                </WithPricingEnableUpgrade>
              ) : (
                ''
              )}
            </div>
          </div>
          <Condition if={sendType === 'twoWay' && channelType === ChannelEnum.ppa && !allowPpaTwoWay}>
            <div
              className={classNames(
                'moe-absolute moe-left-0 moe-top-[35px] moe-bg-[#ffffff80]',
                'moe-w-full moe-backdrop-blur-sm moe-h-[calc(100%-35px)] moe-rounded-5',
                'moe-flex moe-flex-col moe-items-center moe-justify-center',
              )}
            >
              <span className="moe-mb-m moe-text-center moe-max-w-[80%] moe-font-manrope moe-font-normal moe-text-secondary">
                Invite client to download app for quick and easy access to all your services
              </span>
              <MoeGoButton
                variant="tertiary"
                onPress={() => {
                  const newContent = `Hi ${
                    customerName?.split(' ')[0] ?? ''
                  }, we're excited to invite you to download our new app! Experience easier booking, real-time updates, and more. Download it now at ${petParentDownloadLink}`;
                  messageStore.set(customerId, ChannelEnum.sms, newContent);
                  channelSelectRef.current?.setCurrentValue(ChannelEnum.sms);
                }}
              >
                Send SMS to invite
              </MoeGoButton>
            </div>
          </Condition>
        </div>
        {sendType === 'massText' && hasVariables && (
          <div style={{ marginTop: 10, color: '#999', paddingLeft: 20 }}>
            <ExclamationCircleOutlined style={{ color: '#FAAD14', marginRight: 6 }} />
            {"Please do not edit the content within '{}'. Any edits will break the variable."}
          </div>
        )}
      </div>
    );
  },
);

export const EnhanceMessageSendBox = WithMessageSendContext(MessageSendBox);
