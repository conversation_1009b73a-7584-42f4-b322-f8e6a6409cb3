import React, { createContext, useContext } from 'react';
import { type MessageSendBoxProps } from './type';

export interface MessageSendContextProps {
  messageSendBoxProps?: MessageSendBoxProps;
  isMassText?: boolean;
}

export const MessageSendContext = createContext<MessageSendContextProps>({});

export const useMessageSendContext = () => useContext(MessageSendContext);

export const WithMessageSendContext =
  (Comp: React.ComponentType<MessageSendBoxProps>) => (props: MessageSendBoxProps) => {
    const messageSendBoxProps = { messageSendBoxProps: props, isMassText: props?.sendType === 'massText' };

    return (
      <MessageSendContext.Provider value={messageSendBoxProps}>
        <Comp {...props} />
      </MessageSendContext.Provider>
    );
  };
