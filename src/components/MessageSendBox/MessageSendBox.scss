.send-box-wrapper {
  padding: 16px;

  .send-box {
    border-radius: 10px;
    border: solid 0.5px #b1bece;
    width: 100%;
    display: flex;
    flex-direction: column;

    .input-wrapper {
      padding: 8px 16px 0;
      flex: 1 1 auto;
      display: flex;

      textarea {
        resize: none;
        width: 100%;
        height: 100%;
        border: none;
        background: none;
        outline: none;
        // 需要和 MessageSendBox 中 MAX_HEIGHT_FOR_TEXTAREA 的值相同，若修改需要同步修改
        max-height: 140px;
      }

      textarea::-webkit-scrollbar {
        display: none;
      }

      textarea:focus {
        border: none;
      }

      textarea::-webkit-input-placeholder {
        color: #9b9b9b;
      }

      .word-counter {
        cursor: pointer;
        padding: 4px 12px;
        line-height: 16px;
        height: 24px;
        margin-top: -4px;
        font-size: 12px;
        font-weight: 600;
        color: #9b9b9b;
        border-radius: 12px;
        white-space: nowrap;

        &:hover {
          background-color: #f8f9fe;
        }
      }

      .count-highlight {
        color: #29cd56;
      }

      .message-cost-tip {
        white-space: nowrap;
        font-size: 12px;
      }

      .ant-tooltip {
        max-width: none;
      }

      .ant-tooltip-arrow {
        right: 41px;
      }
    }

    .widget-wrapper {
      padding: 0 10px;
      display: flex;
      align-items: center;
      margin-top: 2px;
      justify-content: space-between;

      .widget-btn-group {
        min-width: 0;
        flex-shrink: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 4px;
      }

      .right-btn-group {
        min-width: 0;
        flex-shrink: 0;
        display: flex;
        justify-content: flex-end;

        .schedule-btn {
          background: #fff;
          border: 1px solid #101928;
          color: #101928;
          margin-right: 10px;
        }
      }
    }
  }

  .schedule-tip {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px;
    color: #828282;
    margin-bottom: 11px;

    img {
      margin-left: 6px;
      margin-right: 2px;
    }

    .view-text {
      margin-left: 2px;
      color: #f96b18;
      cursor: pointer;
      font-weight: 800;
    }
  }
}

.message-template-container {
  .send-box-wrapper {
    padding: 0;
    width: 100%;

    .send-box {
      box-shadow: none;
      border-color: #dee1e5;

      textarea {
        min-height: 120px;
      }

      textarea::-webkit-input-placeholder {
        color: #ccc;
      }
    }
  }

  .ant-form-item-has-error .ant-input {
    border-color: #ff4d4f;
  }

  .ant-form-item-has-error .send-box {
    border: solid 1px #ff4d4f;
  }
}
