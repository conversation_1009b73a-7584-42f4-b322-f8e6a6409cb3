import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { MinorImageOutlined } from '@moego/icons-react';
import { IconButton, type UploadItem, UploadStatus, toast } from '@moego/ui';
import { noop } from 'lodash';
import React, { memo, useMemo, useState } from 'react';
import { MessageTestIds } from '../../../config/testIds/message';
import { withPl } from '../../../utils/calculator';
import { GrowthBookFeatureList } from '../../../utils/growthBook/growthBook.config';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { SendMultipleImagesModal, type SendMultipleImagesModalProps } from './SendImageModal/SendMultipleImagesModal';
import { SendSingleImageModal, type SendSingleImageModalProps } from './SendImageModal/SendSingleImageModal';
import { MULTIPLE_IMAGES_COST_SMS_COUNT, type PicInfo } from './SendImageModal/types';

export interface ImagePickerModalProps
  extends Pick<SendSingleImageModalProps, 'pic' | 'setPic'>,
    Pick<SendMultipleImagesModalProps, 'picList' | 'setPicList'> {
  visible: boolean;
  onSendSingle: () => void;
  onSendMultiple: () => void;
  onClose: () => void;
}

export const ImagePickerModal = memo<ImagePickerModalProps>(
  ({ visible, onClose, pic, setPic, onSendSingle, picList, setPicList, onSendMultiple }) => {
    const isEnableSendMultipleImages = useFeatureIsOn(GrowthBookFeatureList.EnableSendMultipleImages);

    return isEnableSendMultipleImages ? (
      <SendMultipleImagesModal
        picList={picList}
        setPicList={setPicList}
        visible={visible}
        onSend={onSendMultiple}
        onClose={onClose}
      />
    ) : (
      <SendSingleImageModal pic={pic} setPic={setPic} visible={visible} onSend={onSendSingle} onClose={onClose} />
    );
  },
);

export interface ImagePickerButtonProps {
  /** mass text support close modal when image send successfully */
  allowCloseModalAfterImageSendSuccess?: boolean;
  onSelect: (url: string) => Promise<void>;
  onSelectMultiple?: (picList: PicInfo[]) => Promise<number>; // 返回值为发送 multiple image 分组的组数
}

export const ImagePickerButton = memo<ImagePickerButtonProps>(
  ({ onSelect, onSelectMultiple, allowCloseModalAfterImageSendSuccess = false }) => {
    const [pic, setPic] = useState<UploadItem | undefined>(undefined);
    const [picList, setPicList] = useState<UploadItem[]>([]);
    const modalVisible = useBool();

    const validPicList = useMemo<PicInfo[]>(() => {
      // 如果先 filter 后 map，那么 map 里会有很多 optional chaining 和 non-null assertion，因此这里先 map 再 filter，最后再取想要的字段
      return picList
        .map((item) => {
          return {
            url: item?.url || '',
            fileSize: (item?.response as PicInfo)?.fileSize || 0,
            status: item?.status,
          };
        })
        .filter((item) => {
          // 过滤掉不合法的图片
          const isSuccess = item.status === UploadStatus.success;
          const isWithUrl = !!item.url;
          return isSuccess && isWithUrl;
        })
        .map((item) => {
          return {
            url: item.url,
            fileSize: item.fileSize,
          };
        });
    }, [picList]);

    const onClose = useLatestCallback(() => {
      modalVisible.close();
      setPic(undefined);
      setPicList([]);
    });

    return (
      <>
        <ImagePickerModal
          visible={modalVisible.value}
          onClose={onClose}
          /**
           * properties for send single image modal
           */
          pic={pic}
          setPic={setPic}
          onSendSingle={() => {
            // allow send successfully then close modal
            if (!pic?.url) {
              return;
            }

            const url = pic.url;
            if (allowCloseModalAfterImageSendSuccess) {
              onSelect(url).then(() => {
                onClose();
              });
            } else {
              onSelect(url);
              onClose();
            }
          }}
          /**
           * properties for send multiple images modal
           */
          picList={picList}
          setPicList={setPicList}
          onSendMultiple={
            onSelectMultiple
              ? () => {
                  const succeededImagesCount = validPicList.length;
                  const failedImagesCount = picList.length - validPicList.length;

                  // allow send successfully then close modal
                  if (allowCloseModalAfterImageSendSuccess) {
                    onSelectMultiple(validPicList).then((groupCount) => {
                      toastMultipleImagesSendResult(succeededImagesCount, failedImagesCount, groupCount);
                      onClose();
                    });
                  } else {
                    onSelectMultiple(validPicList).then((groupCount) => {
                      toastMultipleImagesSendResult(succeededImagesCount, failedImagesCount, groupCount);
                    });
                    onClose();
                  }
                }
              : noop
          }
        />
        <IconButton
          variant="primary"
          size="m"
          color="transparent"
          icon={<MinorImageOutlined />}
          tooltip="Image"
          onPress={() => {
            modalVisible.open();
          }}
          data-testid={MessageTestIds.MessageQuickImageBtn}
        />
      </>
    );
  },
);

const toastMultipleImagesSendResult = (succeededImagesCount: number, failedImagesCount: number, groupCount: number) => {
  if (!succeededImagesCount) {
    toast({
      type: 'error',
      title: 'No image sent',
    });
    return;
  }

  const smsCount = groupCount * MULTIPLE_IMAGES_COST_SMS_COUNT;
  const succeededText = `${smsCount} SMS used to send ${withPl(succeededImagesCount, 'image', 'images')}`;
  const failedText = failedImagesCount ? `${failedImagesCount} failed` : '';

  toast({
    type: 'success',
    title: [succeededText, failedText].filter(Boolean).join(', '),
  });
};
