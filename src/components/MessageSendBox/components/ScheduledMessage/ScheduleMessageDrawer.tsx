/**
 * schedule message drawer
 * support different source
 * The action of getting the data is coupled in this module
 */
import { AutoMessageType, Method, ScheduleMessageStatus } from '@moego/api-web/moego/models/message/v1/message_enums';
import { type ScheduleMessageModel } from '@moego/api-web/moego/models/message/v1/schedule_message_models';
import {
  MinorClockFilled,
  MinorClockOutlined,
  MinorEditOutlined,
  MinorSendOutlined,
  MinorTrashOutlined,
} from '@moego/icons-react';
import { useDispatch, useSelector } from 'amos';
import { Drawer } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useEffect, useState } from 'react';
import SvgIconAlertSvg from '../../../../assets/svg/icon-alert.svg';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { deleteScheduleMessage, getScheduleMessageList } from '../../../../store/message/scheduleMessage.actions';
import {
  selectScheduleMessageListAll,
  selectScheduleMessageListByCustomerId,
} from '../../../../store/message/scheduleMessage.selector';
import { isNormal } from '../../../../store/utils/identifier';
import { Z_INDEX_DRAWER } from '../../../../style/_variables';
import { useBool } from '../../../../utils/hooks/useBool';
import { Condition } from '../../../Condition';
import { SvgIcon } from '../../../Icon/Icon';
import { modalApi } from '../../../Modal/Modal';
import { Tooltip } from '../../../Popup/Tooltip';
import { ModalScheduleMessage, OperateType } from './ModalScheduleMessage';
import './ScheduleMesssageDrawer.scss';

export enum ScheduledMessageSource {
  ALL = 'all',
  USER = 'user',
}

export type ScheduledMessageDrawer = {
  from: ScheduledMessageSource;
  customerId?: string | number;
  phoneNumber?: string | number;
};

type Item = Partial<Omit<ScheduleMessageModel, 'id' | 'customerId'>> & Pick<ScheduleMessageModel, 'id' | 'customerId'>;

type MessageItem = {
  showRecipient?: boolean;
  item: Item;
};

export const ScheduledMessageDrawer = memo<ScheduledMessageDrawer>(({ from, customerId, phoneNumber }) => {
  const dispatch = useDispatch();
  const drawerVisible = useBool(false);
  const showDialog = useBool(false);
  const showPreSendModal = useBool(false);
  const [messageItem, setMessageItem] = useState<Item>({
    id: '',
    customerId: '',
    content: '',
    autoMessageType: AutoMessageType.APPT_BOOKED,
    status: ScheduleMessageStatus.SCHEDULED,
    sendOutAt: '',
    method: Method.SMS,
  });
  const [customerMap, setCustomerMap] = useState<Map<string, string>>(new Map());
  const [contactMap, setContactMap] = useState<Map<string, string>>(new Map());
  const [messageList, setMessageList] = useState<Item[]>([]);
  const [business] = useSelector(selectCurrentBusiness());
  const [scheduleListAll, scheduleList] = useSelector(
    selectScheduleMessageListAll(business.id),
    selectScheduleMessageListByCustomerId(Number(customerId)),
  );
  useEffect(() => {
    customerId && isNormal(Number(customerId))
      ? dispatch(getScheduleMessageList(customerId + ''))
      : dispatch(getScheduleMessageList(undefined, business.id));
  }, []);

  const updateList = (customerId?: string | number) => {
    dispatch(getScheduleMessageList(undefined, business.id));
    customerId && dispatch(getScheduleMessageList(customerId + ''));
  };

  useEffect(() => {
    if (customerId && from === ScheduledMessageSource.USER) {
      setMessageList(scheduleList.scheduleMessages as Item[]);
      setCustomerMap((prevMap) => {
        const newMap = new Map(prevMap);
        scheduleList.customers.forEach((item) => {
          newMap.set(item.id, item.firstName + ' ' + item.lastName);
        });
        return newMap;
      });
    }
  }, [from, scheduleList]);

  useEffect(() => {
    if (from === ScheduledMessageSource.ALL) {
      setMessageList(scheduleListAll.scheduleMessages as Item[]);
      setCustomerMap((prevMap) => {
        const newMap = new Map(prevMap);
        scheduleListAll.customers.forEach((item) => {
          newMap.set(item.id, item.firstName + ' ' + item.lastName);
        });
        return newMap;
      });
      setContactMap((prevMap) => {
        const newMap = new Map(prevMap);
        scheduleListAll.contacts.forEach((item) => {
          newMap.set(item.id, item.phoneNumber);
        });
        return newMap;
      });
    }
  }, [from, scheduleListAll]);

  const handleDeleteItem = (item: Item) => {
    modalApi.confirmDelete({
      content: `Are you sure you want to delete this scheduled message to ${customerMap.get(item.customerId)}`,
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: async () => {
        await dispatch(deleteScheduleMessage(item.id));
        updateList(item.customerId);
      },
      okButtonProps: {
        shape: 'round',
      },
      cancelButtonProps: {
        shape: 'round',
      },
      icon: (
        <SvgIcon
          src={SvgIconAlertSvg}
          className="!moe-mt-[3px] !moe-mr-[8px] !moe-float-left"
          size={16}
          color="#D0021B"
        />
      ),
    });
  };

  const handleModifyItem = (item: Item) => {
    showDialog.open();
    setMessageItem(item);
  };

  const MessageItem = memo<MessageItem>(({ item, showRecipient }) => {
    const methodText = item.method === Method.UNSPECIFIED ? '' : item.method === Method.APP ? 'App' : 'SMS';

    return (
      <div
        className="message-item moe-flex moe-justify-between moe-font-[14px] moe-cursor-pointer"
        onClick={() => handleModifyItem(item)}
      >
        <div className="message-item-left">
          <Condition if={showRecipient}>
            <div className="message-item-title">{customerMap.get(item.customerId)}</div>
          </Condition>
          <div className="message-item-content">{item.content}</div>
          <div className="message-item-time">
            {[business.formatDateTime(dayjs(item.sendOutAt)), methodText].filter(Boolean).join(', ')}
          </div>
        </div>
        <div>
          <Tooltip overlay="Edit" trigger="hover">
            <MinorEditOutlined
              className="schedule-message-icon"
              onClick={(e) => {
                e.stopPropagation();
                handleModifyItem(item);
              }}
            />
          </Tooltip>
          <Tooltip overlay="Send now" trigger="hover">
            <MinorSendOutlined
              className="schedule-message-icon moe-ml-[16px] moe-mr-[16px]"
              onClick={(e) => {
                e.stopPropagation();
                setMessageItem(item);
                showPreSendModal.open();
              }}
            />
          </Tooltip>
          <Tooltip overlay="Delete" trigger="hover">
            <MinorTrashOutlined
              className="schedule-message-icon"
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteItem(item);
              }}
            />
          </Tooltip>
        </div>
      </div>
    );
  });
  return (
    <Condition if={messageList.length > 0}>
      <Condition if={from === ScheduledMessageSource.ALL}>
        <Tooltip overlay="schedule for later" trigger="hover">
          <div className="t-b-btn" style={{ margin: '16px auto 0' }} onClick={drawerVisible.open}>
            <MinorClockOutlined className="moe-mr-[2px]" />
            <span>{messageList.length}</span>
          </div>
        </Tooltip>
      </Condition>
      <Condition if={from === ScheduledMessageSource.USER}>
        <div className="schedule-tip">
          <MinorClockFilled color="#9B9B9B" />
          <p style={{ marginLeft: 2 }}>{`${messageList.length} message is scheduled to be sent.`}</p>
          <span className="view-text" onClick={drawerVisible.open}>
            View
          </span>
        </div>
      </Condition>
      <div>
        <Drawer
          title="Scheduled messages"
          closable={true}
          onClose={drawerVisible.close}
          width={360}
          visible={drawerVisible.value}
          zIndex={Z_INDEX_DRAWER}
        >
          {messageList.map((item, index) => (
            <MessageItem item={item} key={index} showRecipient={from === ScheduledMessageSource.ALL} />
          ))}
        </Drawer>
      </div>
      <ModalScheduleMessage
        customerId={Number(messageItem.customerId)}
        customerName={customerMap.get(messageItem.customerId) || ''}
        phoneNumber={phoneNumber || (messageItem.contactId && contactMap.get(messageItem.contactId)) || ''}
        autoType={messageItem.autoMessageType}
        visible={showPreSendModal.value || showDialog.value}
        content={messageItem}
        handleClose={() => {
          showDialog.close();
          showPreSendModal.close();
        }}
        opType={showPreSendModal.value ? OperateType.SendPreCheck : OperateType.Edit}
        method={messageItem.method ?? Method.SMS}
      />
    </Condition>
  );
});
