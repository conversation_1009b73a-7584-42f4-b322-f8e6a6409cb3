import { AutoMessageType, type Method } from '@moego/api-web/moego/models/message/v1/message_enums';
import { type ScheduleMessagePublicView } from '@moego/api-web/moego/models/message/v1/schedule_message_models';
import { MinorClockOutlined, MinorQuestionOutlined } from '@moego/icons-react';
import { useSerialCallback } from '@moego/tools';
import { Button, DatePicker, Form, Input, Text, TimePicker, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useState } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { customerMapBox } from '../../../../store/customer/customer.boxes';
import { customerContactMapBox } from '../../../../store/customer/customerContact.boxes';
import { selectMessageThreadContact } from '../../../../store/message/message.selectors';
import {
  createScheduleMessage,
  getScheduleMessageList,
  sendScheduleMessage,
  updateScheduleMessage,
} from '../../../../store/message/scheduleMessage.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../utils/DateTimeUtil';
import { Condition } from '../../../Condition';
import { WithPricingEnableUpgrade } from '../../../Pricing/WithPricingComponents';
import { Switch } from '../../../SwitchCase';
import { toastApi } from '../../../Toast/Toast';
import { MAX_MESSAGE_LENGTH } from '../../Const';
import { ScheduledMessageModalView } from './ModalScheduleMessage.style';

export enum OperateType {
  Create = 'create',
  Edit = 'edit',
  SendPreCheck = 'SendPreCheck',
}

export interface ModalScheduleMessageProps {
  customerId: number;
  customerName: string;
  phoneNumber: string | number;
  appointmentId?: string | number;
  autoType?: number;
  visible: boolean;
  handleClose: () => void;
  content: Partial<ScheduleMessagePublicView>;
  opType: OperateType;
  method: Method;
  maxLength?: number;
}

export const ModalScheduleMessage = memo<ModalScheduleMessageProps>(
  ({
    customerId,
    customerName,
    phoneNumber,
    appointmentId,
    autoType = AutoMessageType.UNSPECIFIED,
    visible,
    handleClose,
    maxLength = MAX_MESSAGE_LENGTH,
    opType,
    method,
    content,
  }) => {
    const yesterdayEndTimestamp = dayjs().add(-1, 'day').endOf('day').valueOf();
    const dispatch = useDispatch();
    const [messageContent, setMessageContent] = useState('');
    const [customer, threadContact, contactMap, business] = useSelector(
      customerMapBox.mustGetItem(customerId),
      selectMessageThreadContact(customerId),
      customerContactMapBox,
      selectCurrentBusiness,
    );

    const recommendTimeList = [
      {
        text: `Later today ${business.formatTime(dayjs().add(2, 'hour'))}`,
        timestamp: dayjs().add(2, 'hour').valueOf(),
      },
      {
        text: `Tomorrow morning ${business.formatTime(dayjs().add(1, 'day').startOf('day').add(9, 'hour'))}`,
        timestamp: dayjs().add(1, 'day').startOf('day').add(9, 'hour').valueOf(),
      },
    ];
    const [selectedTime, setSelectedTime] = useState(recommendTimeList[0].timestamp);
    const handleSelectTime = (timestamp: number) => {
      setSelectedTime(timestamp);
    };

    const updateList = () => {
      dispatch(getScheduleMessageList(undefined, business.id));
      dispatch(getScheduleMessageList(customerId + ''));
    };

    useEffect(() => {
      content.content && setMessageContent(content.content);
      content.sendOutAt && setSelectedTime(dayjs(content.sendOutAt).valueOf());
    }, [appointmentId, customerId, content]);

    const renderDatePickerComponent = () => (
      <div>
        <Form.Label isRequired label="Send out at"></Form.Label>
        <div className="moe-flex moe-items-center moe-justify-between moe-gap-s">
          <DatePicker
            className="moe-flex-1"
            isClearable={false}
            format={business.dateFormat}
            value={dayjs(selectedTime)}
            disabledDate={(currentDate) => {
              return currentDate.valueOf() < yesterdayEndTimestamp;
            }}
            onChange={(date) => {
              if (date) {
                setSelectedTime((pre) => {
                  const preMinute = dayjs(pre).getMinutes();
                  return dayjs(date).setMinutes(preMinute).valueOf();
                });
              }
            }}
          />
          <TimePicker
            className="moe-flex-1"
            isClearable={false}
            format={business.timeFormat()}
            value={dayjs(selectedTime)}
            onChange={(v) => {
              if (v) {
                setSelectedTime((pre) => {
                  const preDate = dayjs(pre).format(DATE_FORMAT_EXCHANGE);
                  return dayjs(preDate).setMinutes(v.getMinutes()).valueOf();
                });
              }
            }}
          />
        </div>
      </div>
    );

    const RecommendSelector = () => (
      <div>
        <Form.Label label="Recommended time"></Form.Label>
        {recommendTimeList.map((item, index) => (
          <div
            className="moe-flex moe-gap-xs moe-cursor-pointer moe-mb-[8px] moe-w-[271px] moe-p-[3px] moe-rounded-[9px] hover:moe-bg-[#F2F2F2]"
            key={index}
            onClick={() => handleSelectTime(item.timestamp)}
          >
            <MinorClockOutlined />
            <Text variant="regular-short">{item.text}</Text>
          </div>
        ))}
      </div>
    );
    const primaryContact = contactMap.mustGetItem(customer.primaryContactId);
    const realContact = threadContact || primaryContact;
    const contactId = content.contactId || realContact?.customerContactId;
    const handleConfirm = useSerialCallback(async () => {
      if (appointmentId || isNormal(autoType)) {
        if (opType === OperateType.Create) {
          await dispatch(
            createScheduleMessage({
              customerId: customerId + '',
              byAutoMessage: {
                appointmentId: appointmentId + '',
                // content: messageContent,
                type: autoType,
              },
              sendOutAt: dayjs(selectedTime).toISOString(),
              method,
            }),
          );
          toastApi.success('Message scheduled successfully');
          updateList();
        }
        if (opType === OperateType.Edit) {
          await dispatch(
            updateScheduleMessage({
              scheduleMessageId: content.id + '',
              sendOutAt: dayjs(selectedTime).toISOString(),
            }),
          );
          updateList();
        }
        if (opType === OperateType.SendPreCheck) {
          await dispatch(sendScheduleMessage(content.id + ''));
          updateList();
        }
      } else {
        if (opType === OperateType.Create) {
          await dispatch(
            createScheduleMessage({
              customerId: customerId + '',
              byCustomContent: {
                receiptContactId: contactId + '',
                content: messageContent,
              },
              sendOutAt: dayjs(selectedTime).toISOString(),
              method,
            }),
          );
          toastApi.success('Message scheduled successfully');
          updateList();
        }
        if (opType === OperateType.Edit) {
          await dispatch(
            updateScheduleMessage({
              scheduleMessageId: content.id + '',
              byCustomContent: {
                receiptContactId: contactId + '',
                content: messageContent,
              },
              sendOutAt: dayjs(selectedTime).toISOString(),
            }),
          );
          toastApi.success('Message scheduled successfully');
          updateList();
        }
        if (opType === OperateType.SendPreCheck) {
          await dispatch(
            updateScheduleMessage({
              scheduleMessageId: content.id + '',
              byCustomContent: {
                content: messageContent,
              },
              sendOutAt: dayjs(selectedTime).toISOString(),
            }),
          );
          await dispatch(sendScheduleMessage(content.id + ''));
          updateList();
        }
      }
      handleClose();
    });
    return (
      <Switch>
        <Switch.Case if={appointmentId || autoType > 0}>
          <ScheduledMessageModalView
            isOpen={visible}
            onClose={handleClose}
            size="s"
            title={opType === OperateType.SendPreCheck ? 'Send message now' : 'Schedule message'}
            footer={
              <div className="moe-flex-1 moe-flex moe-justify-end moe-gap-s">
                <Button size="s" variant="secondary" onPress={handleClose}>
                  Cancel
                </Button>
                <WithPricingEnableUpgrade permission="scheduledMessage">
                  <Button size="s" onPress={() => handleConfirm()} isLoading={handleConfirm.isBusy()}>
                    Confirm
                  </Button>
                </WithPricingEnableUpgrade>
              </div>
            }
          >
            <div className="moe-flex moe-flex-col moe-gap-m">
              <div>
                <Form.Label label="Recipient"></Form.Label>
                <Text variant="regular-short">{customerName}</Text>
              </div>
              <div>
                <Form.Label
                  label={
                    <div className="moe-flex moe-gap-xxs">
                      Message
                      <Tooltip content="Scheduled auto message can not be edited">
                        <MinorQuestionOutlined className="moe-text-tertiary" />
                      </Tooltip>
                    </div>
                  }
                />

                <Input.TextArea
                  isDisabled
                  className="moe-cursor-not-allowed"
                  value={messageContent}
                  maxLength={maxLength}
                  placeholder="Write a message"
                />
              </div>
              <Condition if={opType !== OperateType.SendPreCheck}>{renderDatePickerComponent()}</Condition>
              <Condition if={opType !== OperateType.SendPreCheck}>{RecommendSelector()}</Condition>
            </div>
          </ScheduledMessageModalView>
        </Switch.Case>
        <Switch.Case else>
          <ScheduledMessageModalView
            isOpen={visible}
            onClose={handleClose}
            size="s"
            title={
              opType === OperateType.SendPreCheck ? (
                'Send message now'
              ) : (
                <div className="moe-flex moe-items-center">
                  <div>Schedule message</div>
                </div>
              )
            }
            footer={
              <div className="moe-flex-1 moe-flex moe-justify-end moe-gap-s">
                <Button size="s" variant="secondary" onPress={handleClose}>
                  Cancel
                </Button>
                <WithPricingEnableUpgrade permission="scheduledMessage">
                  <Button size="s" onPress={() => handleConfirm()} isLoading={handleConfirm.isBusy()}>
                    Confirm
                  </Button>
                </WithPricingEnableUpgrade>
              </div>
            }
          >
            <div className="moe-flex moe-flex-col moe-gap-m">
              <div>
                <Form.Label label="Recipient"></Form.Label>
                <Text variant="regular-short">{`${customerName} (${business.formatPhoneNumber(phoneNumber + '')})`}</Text>
              </div>
              <div>
                <Form.Label label="Message"></Form.Label>
                <Input.TextArea
                  value={messageContent}
                  maxLength={maxLength}
                  placeholder="Write a message"
                  onChange={(e) => setMessageContent(e)}
                />
              </div>
              <Condition if={opType !== OperateType.SendPreCheck}>{renderDatePickerComponent()}</Condition>
              <Condition if={opType !== OperateType.SendPreCheck}>{RecommendSelector()}</Condition>
            </div>
          </ScheduledMessageModalView>
        </Switch.Case>
      </Switch>
    );
  },
);
