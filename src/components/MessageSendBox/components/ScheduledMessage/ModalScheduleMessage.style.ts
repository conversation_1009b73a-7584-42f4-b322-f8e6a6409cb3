import { Modal } from '@moego/ui';
import styled from 'styled-components';

export const ScheduledMessageModalView = styled(Modal)`
  .create-modal-footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &__close {
      font-weight: 700;
      font-size: 14px;
      line-height: 18px;
      color: #f96b18;
      cursor: pointer;
      &:hover {
        color: #de5a21;
      }
    }
  }

  .item-content {
    color: #666;
    font-family: Nunito;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
    margin-bottom: 16px;
  }

  .item-title {
    color: #666;
    font-family: Nunito;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px;
    margin-bottom: 8px;
  }

  .input-wrapper {
    flex: 1 1 auto;
    display: flex;
    border-radius: 4px;
    border: 1px solid #dee1e5;
    height: 74px;
    padding: 14px 16px;

    textarea {
      resize: none;
      width: 100%;
      height: 100%;
      border: none;
      background: none;
      outline: none;
    }

    textarea::-webkit-scrollbar {
      display: none;
    }

    textarea:focus {
      border: none;
    }

    textarea::-webkit-input-placeholder {
      color: #9b9b9b;
    }

    .word-counter {
      cursor: pointer;
      padding: 4px 12px;
      line-height: 16px;
      height: 24px;
      margin-top: -4px;
      font-size: 12px;
      font-weight: 600;
      color: #9b9b9b;
      border-radius: 12px;
      white-space: nowrap;
      &:hover {
        background-color: #f8f9fe;
      }
    }

    .count-highlight {
      color: #29cd56;
    }

    .message-cost-tip {
      white-space: nowrap;
      font-size: 12px;
    }
    .ant-tooltip {
      max-width: none;
    }
    .ant-tooltip-arrow {
      right: 41px;
    }
  }
`;
