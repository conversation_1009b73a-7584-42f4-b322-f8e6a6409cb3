import { MinorVariableOutlined } from '@moego/icons-react';
import { Dropdown, IconButton } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { MessageTestIds } from '../../../config/testIds/message';
import { selectTemplatePlaceholderList } from '../../../store/message/messageTemplate.selectors';

type ProgrammingButtonProps = {
  onSelect: (message: string) => void;
  isLead?: boolean;
};

export const VariableButton = memo((props: ProgrammingButtonProps) => {
  const { onSelect, isLead = false } = props;
  const [placeholderList] = useSelector(selectTemplatePlaceholderList);

  const handleAddVariable = async (variable: string) => {
    onSelect?.(variable);
  };

  if (!placeholderList.length) {
    return null;
  }

  return (
    <Dropdown side="top">
      <Dropdown.Trigger>
        <IconButton
          variant="primary"
          size="m"
          color="transparent"
          icon={<MinorVariableOutlined />}
          tooltip="Variable"
          data-testid={MessageTestIds.MessageQuickVariableBtn}
        />
      </Dropdown.Trigger>
      <Dropdown.Menu selectionMode="none" autoFocus={false}>
        {placeholderList
          // 如果是leads模块，需要隐藏appt相关的变量
          ?.filter((i) => (!isLead ? true : i.placeholderGroup !== 'Appointment'))
          ?.map((item) => {
            const { placeholderName = '', placeholderText = '' } = item || {};

            return (
              <Dropdown.MenuItem
                key={placeholderText}
                textValue={placeholderName}
                onAction={() => {
                  handleAddVariable(placeholderText);
                }}
              >
                {placeholderName}
              </Dropdown.MenuItem>
            );
          })}
      </Dropdown.Menu>
    </Dropdown>
  );
});
