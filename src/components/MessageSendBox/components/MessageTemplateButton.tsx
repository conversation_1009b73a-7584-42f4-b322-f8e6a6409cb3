import { type MessageTemplateSimpleView } from '@moego/api-web/moego/models/message/v1/message_template_models';
import { MinorSaveRepliesOutlined } from '@moego/icons-react';
import { IconButton } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useRef, useState } from 'react';
import { MessageTestIds } from '../../../config/testIds/message';
import { getAllMessageTemplates } from '../../../store/message/messageTemplate.actions';
import { useBool } from '../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { type ChannelEnum } from './ChannelSelect/ChannelSelect';
import { MessageTemplateDrawer } from './MessageTemplate/MessageTemplateDrawer';
import { MessageTemplateModal, type MessageTemplateModalRef } from './MessageTemplate/MessageTemplateModal';

export interface MessageTemplateButtonProps {
  onSelect: (message: string) => void;
  channel?: ChannelEnum;
}

export const MessageTemplateButton = memo<MessageTemplateButtonProps>((props) => {
  const { onSelect, channel } = props;
  const dispatch = useDispatch();
  const templateDrawerVisible = useBool(false);
  const templateModalVisible = useBool(false);
  const templateModalRef = useRef<MessageTemplateModalRef>(null);
  const [searchWord, setSearchWord] = useState<string>('');
  const [currentTemplateItem, setCurrentTemplateItem] = useState<MessageTemplateSimpleView>();

  const handleClick = useSerialCallback(async () => {
    await dispatch(getAllMessageTemplates());
    templateDrawerVisible.open();
  });

  const handleDrawerClose = () => {
    templateDrawerVisible.close();
    setSearchWord('');
  };

  const handleModalClose = () => {
    templateModalVisible.close();
    setCurrentTemplateItem(undefined);
  };

  const handleEdit = (item: MessageTemplateSimpleView) => {
    templateModalRef.current?.setFieldValue(item);
    templateModalVisible.open();
    setCurrentTemplateItem(item);
  };

  const handleSelect = (message: string) => {
    onSelect?.(message);
  };

  const handleCreate = () => {
    setSearchWord('');
  };

  return (
    <>
      <IconButton
        variant="primary"
        size="m"
        color="transparent"
        icon={<MinorSaveRepliesOutlined />}
        tooltip="Saved replies"
        onPress={handleClick}
        data-testid={MessageTestIds.MessageQuickTemplateBtn}
      />
      <MessageTemplateDrawer
        visible={templateDrawerVisible.value}
        onClose={handleDrawerClose}
        onAddNew={templateModalVisible.open}
        onSelect={handleSelect}
        onEdit={handleEdit}
        searchWord={searchWord}
        setSearchWord={setSearchWord}
      />
      <MessageTemplateModal
        ref={templateModalRef}
        visible={templateModalVisible.value}
        onModalClose={handleModalClose}
        currentTemplateItem={currentTemplateItem}
        onCreate={handleCreate}
        searchWord={searchWord}
        channel={channel}
      />
    </>
  );
});
