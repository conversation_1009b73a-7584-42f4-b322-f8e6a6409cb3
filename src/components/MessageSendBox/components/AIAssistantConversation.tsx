import { useDispatch, useSelector } from 'amos';
import { Input } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import SvgAccountIconBackSvg from '../../../assets/svg/account-icon-back.svg';
import {
  adoptAnswer,
  askQuestion,
  closeConversation,
  createConversation,
  rephraseAnswer,
} from '../../../store/aiAssistant/aiAssistant.actions';
import {
  QUESTION_ID_INITIAL,
  QUESTION_ID_LOADING,
  loadingQAStatusBox,
  qAMapBox,
} from '../../../store/aiAssistant/aiAssistant.boxes';
import { selectConversationQAList } from '../../../store/aiAssistant/aiAssistant.selectors';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { Button } from '../../Button/Button';
import { SvgIcon } from '../../Icon/Icon';
import { Loading } from '../../Loading/Loading';
import { StyledAIConversation } from './AIAssistantConversation.style';
import { AIAssistantQA } from './AIAssistantQA';

export interface AIAssistantConversationProps {
  prompt: string;
  question?: string;
  customerId: string;
  onAdoptAnswer: (answer: string) => void;
  sendType?: 'massText' | 'twoWay';
}

export interface AIAssistantConversationInnerProps extends AIAssistantConversationProps {
  conversationId: string;
}

export const AIAssistantConversation: React.FC<AIAssistantConversationProps> = ({
  prompt,
  question,
  customerId,
  onAdoptAnswer,
  sendType = 'twoWay',
}) => {
  const [conversationId, setConversationId] = useState<string>();
  const conversationLoading = useBool(true);
  const conversationLoadFailed = useBool(false);
  const dispatch = useDispatch();
  const load = useLatestCallback(async () => {
    try {
      conversationLoading.open();
      conversationLoadFailed.close();
      const params =
        sendType == 'twoWay'
          ? { twoWayMessage: { customerId } }
          : {
              massText: {},
            };
      const conversationId = await dispatch(
        createConversation({
          ...params,
          prompt,
        }),
      );
      setConversationId(conversationId);
      conversationLoading.close();
    } catch (error) {
      conversationLoading.close();
      conversationLoadFailed.open();
    }
  });
  useEffect(() => {
    load();
  }, []);
  useEffect(() => {
    return () => {
      if (conversationId) {
        dispatch(closeConversation(conversationId));
      }
    };
  }, [conversationId]);
  return (
    <StyledAIConversation>
      <div className="title">MoeGo AI assist</div>
      {!conversationId || conversationLoading.value ? (
        conversationLoadFailed.value ? (
          <div className="!moe-flex !moe-fle-row !moe-p-[16px]">
            Conversation load failed, please&nbsp;
            <Button
              size="sm"
              btnType="link"
              className="!moe-text-brand !moe-no-underline"
              hoverAnimation={false}
              onClick={load}
            >
              retry
            </Button>
            .
          </div>
        ) : (
          <Loading loading className="!moe-mt-[20px]" />
        )
      ) : (
        <AIAssistantConversationInner
          conversationId={conversationId!}
          prompt={prompt}
          question={question}
          customerId={customerId}
          onAdoptAnswer={onAdoptAnswer}
        />
      )}
    </StyledAIConversation>
  );
};

export const AIAssistantConversationInner: React.FC<AIAssistantConversationInnerProps> = ({
  conversationId,
  question,
  onAdoptAnswer,
}) => {
  const dispatch = useDispatch();
  const [qaList, qaMap, loadingQAStatus] = useSelector(
    selectConversationQAList(conversationId),
    qAMapBox,
    loadingQAStatusBox,
  );
  const hasLoadingQA = loadingQAStatus.questionId !== QUESTION_ID_INITIAL;
  const [questionText, setQuestionText] = useState('');
  const qaListRef = useRef<HTMLDivElement>(null);
  const load = useLatestCallback(async () => {
    if (question) {
      await dispatch(askQuestion({ conversationId, question: question! }));
    }
  });

  const handleAdoptAnswer = useLatestCallback(async (questionId: string, answer: string) => {
    onAdoptAnswer(answer);
    dispatch(adoptAnswer({ conversationId, questionId }));
  });

  const handleAskQuestion = useLatestCallback(async () => {
    if (!questionText || hasLoadingQA) {
      return;
    }
    setQuestionText('');
    await dispatch(askQuestion({ conversationId, question: questionText }));
  });

  const handleRephrase = useLatestCallback(async (questionId: string) => {
    if (questionId === QUESTION_ID_LOADING) {
      const question = qaMap.mustGetItem(questionId).question;
      await dispatch(askQuestion({ conversationId, question }));
    } else {
      await dispatch(rephraseAnswer({ conversationId, questionId }));
    }
  });

  useEffect(() => {
    load();
  }, []);

  useEffect(() => {
    // scroll last qa into view
    qaListRef?.current?.lastElementChild?.scrollIntoView();
  }, [qaList.last()]);

  return (
    <>
      <div className="qa-list-container" ref={qaListRef}>
        {qaList.map((id) => {
          const questionAndAnswer = qaMap.mustGetItem(id);
          return (
            <AIAssistantQA
              question={questionAndAnswer.question}
              answer={questionAndAnswer.answer}
              questionId={id}
              onAdoptAnswer={handleAdoptAnswer}
              onRephrase={handleRephrase}
              disabled={hasLoadingQA && id !== loadingQAStatus.questionId}
              loadStatus={id === loadingQAStatus.questionId ? loadingQAStatus.loadStatus : undefined}
              key={id}
            />
          );
        })}
      </div>
      <div className="input-container">
        <Input
          disabled={hasLoadingQA}
          className="input-wrapper"
          placeholder="Tell us to..."
          value={questionText}
          onChange={(e) => {
            setQuestionText(e.target.value);
          }}
          onPressEnter={handleAskQuestion}
          suffix={
            <Button
              btnType="primary"
              buttonRadius="circle"
              className="!moe-h-[24px] !moe-w-[24px] !moe-p-[0]"
              disabled={!questionText || hasLoadingQA}
              onClick={handleAskQuestion}
            >
              <SvgIcon src={SvgAccountIconBackSvg} rotate={180} size={14} />
            </Button>
          }
        />
      </div>
    </>
  );
};
