import React from 'react';
import { AIAssistant, type AIAssistantProps } from './AIAssistant';
import { AssistantListStyle } from './AssistantList.style';
import { RecoveryTemplate } from './AssistantTemplate/Recovery';

type Props = {} & AIAssistantProps;

export const AssistantList = (props: Props) => {
  const { customerId, sendType, onAdoptAnswer, prompt } = props;

  return (
    <AssistantListStyle>
      <RecoveryTemplate onAdoptAnswer={onAdoptAnswer} />
      <AIAssistant
        buttonProps={{
          className: 'moe-h-[32px]',
        }}
        customerId={customerId + ''}
        sendType={sendType}
        onAdoptAnswer={onAdoptAnswer}
        prompt={prompt}
      />
    </AssistantListStyle>
  );
};
