import { useSelector } from 'amos';
import React from 'react';
import { stripeAvailableCountryOption } from '../../../container/CardProcessing/stripe/StripeSetup';
import { useHasBrandedApp } from '../../../store/branded/branded.hooks';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type ChannelEnum } from './ChannelSelect/ChannelSelect';
import { EmojiPickerButton } from './EmojiPickerButton';
import { SpecialLinks } from './SpecialLinks/SpecialLinks';
import { VariableButton } from './VariableButton';
import { useHasMembershipPermission } from './hooks/useHasMembershipPermission';

interface MessageBoxBtnGroupProps {
  onSelect: (link: string) => void;
  customerId: number;
  channel?: ChannelEnum;
}

export const MessageBoxBtnGroup = ({ customerId, onSelect, channel }: MessageBoxBtnGroupProps) => {
  const [business] = useSelector(selectCurrentBusiness);

  const hasMembershipPermission = useHasMembershipPermission('sellMembership');
  const hasAccessMembershipPermission = useHasMembershipPermission('accessMembership');
  const { has: hasBrandedApp } = useHasBrandedApp();

  return (
    <>
      <EmojiPickerButton onSelect={onSelect} />
      <VariableButton onSelect={onSelect} />
      <SpecialLinks
        customerId={customerId}
        allowOnlineBooking
        allowOnlineBookingVariable
        allowAgreement
        allowAgreementVariable
        allowAppt
        allowApptVariable
        allowIntakeForm
        allowIntakeFormVariable
        allowPetParentPortal={!hasBrandedApp}
        allowPetParentPortalVariable
        allowPetParentAppDownload={hasBrandedApp}
        allowPetParentAppDownloadVariable
        allowMembership={hasMembershipPermission && hasAccessMembershipPermission}
        allowMembershipVariable
        allowRequestCof={!!stripeAvailableCountryOption(business.country)}
        allowRequestCofVariable
        onSelect={onSelect}
        channel={channel}
      />
    </>
  );
};
