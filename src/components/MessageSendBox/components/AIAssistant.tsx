import { useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { CSSTransition } from 'react-transition-group';
import { type AIAssistantQuestionConfig } from '../../../store/aiAssistant/aiAssistant.boxes';
import { META_DATA_KEY_LIST } from '../../../store/metadata/metadata.config';
import { selectMetaDataByKey } from '../../../store/metadata/metadata.selectors';
import { useBool } from '../../../utils/hooks/useBool';
import { Condition } from '../../Condition';
import { AIAssistantDropdown, AIButton, type AIButtonProps } from './AIButton';
import { StyledBorderGradientButton } from './GradientButton.style';

export interface AIAssistantProps {
  prompt: string;
  customerId: string;
  onAdoptAnswer: (answer: string) => void;
  sendType?: 'massText' | 'twoWay';

  buttonProps?: AIButtonProps;
}

export const AIAssistant = memo<AIAssistantProps>(
  ({ customerId, onAdoptAnswer, prompt, sendType, buttonProps = {} }) => {
    const [aiEnabled, aiQuestionConfig] = useSelector(
      selectMetaDataByKey<number>(META_DATA_KEY_LIST.AIAssistantEnabled),
      selectMetaDataByKey<AIAssistantQuestionConfig>(META_DATA_KEY_LIST.AIAssistantConfig),
    );
    const presetButtonsVisible = useBool(false);

    useEffect(() => {
      presetButtonsVisible.as(!!prompt);
    }, [prompt]);

    return (
      <Condition if={!!aiEnabled}>
        {aiQuestionConfig?.questionList.map(({ question, label }, index) => (
          <AIAssistantDropdown
            question={question}
            prompt={prompt}
            customerId={customerId + ''}
            onAdoptAnswer={onAdoptAnswer}
            key={index}
            sendType={sendType}
          >
            <CSSTransition in={presetButtonsVisible.value} unmountOnExit timeout={300} className="preset-button">
              <div>
                <StyledBorderGradientButton>{label}</StyledBorderGradientButton>
              </div>
            </CSSTransition>
          </AIAssistantDropdown>
        ))}

        <AIAssistantDropdown
          prompt={prompt}
          customerId={customerId + ''}
          onAdoptAnswer={onAdoptAnswer}
          sendType={sendType}
        >
          <AIButton {...buttonProps} />
        </AIAssistantDropdown>
      </Condition>
    );
  },
);
