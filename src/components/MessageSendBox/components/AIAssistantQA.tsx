import React from 'react';
import { type AIQuestionLoadStatus, type QAModel } from '../../../store/aiAssistant/aiAssistant.boxes';
import { Button } from '../../Button/Button';
import { Loading } from '../../Loading/Loading';
import { QAContent } from './AIAssistantConversation.style';

export interface AIAssistantQAProps extends QAModel {
  loadStatus?: AIQuestionLoadStatus;
  onAdoptAnswer: (questionId: string, answer: string) => void;
  onRephrase: (questionId: string) => void;
  disabled?: boolean;
}

export const AIAssistantQA: React.FC<AIAssistantQAProps> = ({
  question,
  questionId,
  answer,
  loadStatus,
  onAdoptAnswer,
  onRephrase,
  disabled,
}) => {
  return (
    <QAContent>
      <div className="command-prompt">{question}</div>
      {loadStatus === 'loading' ? (
        <div className="answer">
          <Loading loading />
        </div>
      ) : (
        <div className="answer">{answer}</div>
      )}
      <div className="actions">
        <Button
          size="sm"
          btnType="primary"
          buttonRadius="circle"
          disabled={(loadStatus && ['loading', 'error'].includes(loadStatus)) || disabled}
          onClick={() => onAdoptAnswer(questionId, answer || '')}
        >
          Insert
        </Button>
        <Button
          size="sm"
          btnType="link"
          className="!moe-text-brand !moe-no-underline"
          hoverAnimation={false}
          disabled={loadStatus === 'loading' || disabled}
          onClick={() => onRephrase(questionId)}
        >
          Rephrase
        </Button>
      </div>
    </QAContent>
  );
};
