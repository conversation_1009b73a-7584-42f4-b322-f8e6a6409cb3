import { useSelector } from 'amos';
import { type PermissionKinds } from '../../../../store/business/role.boxes';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';

export function useHasMembershipPermission(permissionKey: PermissionKinds) {
  const enableMembership = useFeatureIsOn(GrowthBookFeatureList.EnableMembership);
  const [permission] = useSelector(selectCurrentPermissions);
  return enableMembership && permission.has(permissionKey);
}
