import EmojiPicker from '@emoji-mart/react';
import { MinorEmojiOutlined } from '@moego/icons-react';
import { IconButton, Popover } from '@moego/ui';
import React, { memo, useRef } from 'react';
import { MessageTestIds } from '../../../config/testIds/message';
import { useAsyncState } from '../../../utils/hooks/hooks';
import { useBool } from '../../../utils/hooks/useBool';

export interface EmojiPickerButtonProps {
  onSelect: (emoji: string) => void;
}

export const EmojiPickerButton = memo<EmojiPickerButtonProps>(({ onSelect }) => {
  const popoverVisible = useBool();
  const tooltipVisible = useBool();
  const ref = useRef<HTMLDivElement>(null);
  const data = useAsyncState(() =>
    import(
      /* webpackChunkName: "emoji-mart__data"*/
      /* webpackPrefetch: true */
      '@emoji-mart/data'
    ).then((data) => ({ ...data })),
  );
  return (
    <div className="emoji-picker-popover-wrapper" ref={ref}>
      <Popover
        side="right"
        align="center"
        isOpen={popoverVisible.value}
        onOpenChange={(visible) => {
          popoverVisible.as(visible);
          tooltipVisible.close();
        }}
        // 把 popover 挂载到当前组件所在的 DOM 节点上
        // 避免在 modal 打开的时候，emoji 的 input 无法激活
        container={ref.current}
      >
        <Popover.Trigger>
          <IconButton
            variant="primary"
            size="m"
            color="transparent"
            icon={<MinorEmojiOutlined />}
            tooltip="Emoji"
            onPress={popoverVisible.open}
            data-testid={MessageTestIds.MessageQuickEmojiBtn}
          />
        </Popover.Trigger>
        <Popover.Content footer={null}>
          <div className="moe-mt-[16px]">
            <EmojiPicker
              autoFocus
              theme="light"
              data={data}
              onEmojiSelect={(emoji: any) => {
                if ('native' in emoji) {
                  onSelect(emoji.native);
                  popoverVisible.close();
                  tooltipVisible.close();
                }
              }}
            />
          </div>
        </Popover.Content>
      </Popover>
    </div>
  );
});
