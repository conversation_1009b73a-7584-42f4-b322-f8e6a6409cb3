import { MinorCAppOutlined, MinorChevronDownOutlined, MinorMessageOutlined } from '@moego/icons-react';
import { Dropdown, cn } from '@moego/ui';
import classNames from 'classnames';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useMount } from 'react-use';
import { useChannelSelectStorage } from './useChannelSelectStorage';

const { Menu, MenuItem, Trigger } = Dropdown;

export enum ChannelEnum {
  sms = 1,
  email = 2,
  call = 4,
  ppa = 5,
}

export interface ChannelSelectProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  /**
   * @description 传入 storageKey 时，会将选中的值存储在 localStorage 中，下次打开页面时会自动选中上次选中的值
   * @design 传入 -1 时，不会存储
   */
  storageKey?: -1 | string | number;
  className?: string;

  /**
   * @description 只有用户主动选择时/通过 storage 初始化时才会触发
   */
  onChange?: (value: ChannelEnum) => void;
  /**
   * @description 选中值改变时触发
   */
  onValueChange?: (value: ChannelEnum) => void;

  defaultChannel?: ChannelEnum;

  ppaIcon?: React.ReactNode;
}

export type ChannelSelectRef = {
  setCurrentValue: (value: ChannelEnum) => void;
  getCurrentValue: () => ChannelEnum;
};

export const ChannelSelect = forwardRef<ChannelSelectRef, ChannelSelectProps>(
  ({ storageKey, className, onChange, onValueChange, defaultChannel = ChannelEnum.ppa, ppaIcon, ...rest }, ref) => {
    const hasKey = Boolean(storageKey) && storageKey !== useChannelSelectStorage.IGNORE_STORAGE_KEY;
    const [{ upsert }, storageStore] = useChannelSelectStorage(hasKey);
    const [currentValue, setCurrentValue] = useState<ChannelEnum>(defaultChannel);

    const options: {
      value: ChannelEnum;
      title: string;
      icon: React.ReactNode;
    }[] = useMemo(
      () => [
        {
          value: ChannelEnum.ppa,
          title: 'Pet Parent App',
          icon: ppaIcon || <MinorCAppOutlined className="moe-text-[20px]" />,
        },
        {
          value: ChannelEnum.sms,
          title: 'SMS',
          icon: <MinorMessageOutlined className="moe-text-[20px]" />,
        },
      ],
      [ppaIcon],
    );

    useMount(() => {
      if (!hasKey || !storageStore || !storageKey) {
        return;
      }
      const storedValue = storageStore[storageKey];
      if (!storedValue) {
        return;
      }
      setCurrentValue(storedValue);
      onChange?.(storedValue);
    });

    const handleSetCurrentValue = (value: ChannelEnum) => {
      setCurrentValue(value);
      if (hasKey) {
        upsert(String(storageKey!), value);
      }
      onChange?.(value);
    };

    const currentOption = useMemo(
      () => options.find((option) => option.value === currentValue),
      [options, currentValue],
    );

    useImperativeHandle(ref, () => ({
      setCurrentValue,
      getCurrentValue: () => currentValue,
    }));

    useEffect(() => {
      onValueChange?.(currentValue);
    }, [currentValue, onValueChange]);

    return (
      <Dropdown sideOffset={-4}>
        <Trigger>
          <div
            className={classNames(
              'moe-flex moe-flex-row moe-items-center moe-cursor-pointer moe-w-[200px] moe-pt-[8px] moe-pb-[10px] moe-gap-[4px]',
              className,
            )}
            {...rest}
          >
            {currentOption?.icon}
            <span className="moe-text-[14px] moe-leading-[18px] moe-font-manrope">{currentOption?.title}</span>
            <MinorChevronDownOutlined className="moe-text-[20px]" />
          </div>
        </Trigger>
        <Menu
          onAction={(value) => {
            handleSetCurrentValue(Number(value));
          }}
          selectedKeys={[currentValue]}
          className="moe-w-[200px]"
        >
          {options.map((option) => (
            <MenuItem
              key={option.value}
              icon={option.icon}
              title={option.title}
              value={option.value}
              className={cn({
                '!moe-bg-brand-subtle': option.value === currentValue,
              })}
              classNames={{
                title: option.value === currentValue ? 'moe-font-bold' : '',
              }}
            />
          ))}
        </Menu>
      </Dropdown>
    );
  },
);
