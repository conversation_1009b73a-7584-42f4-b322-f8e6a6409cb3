import { useLocalStorageState } from 'ahooks';
import { type ChannelEnum } from './ChannelSelect';

const STORAGE_KEY = 'channel-select-storage';
const IGNORE_STORAGE_KEY = -1;

const _useChannelSelectStorage = (listen = false) => {
  const [storageStore, setStorageStore] = useLocalStorageState<Record<string, ChannelEnum>>(STORAGE_KEY, {
    defaultValue: {} as Record<string, ChannelEnum>,
    listenStorageChange: listen,
  });

  const remove = (key: string) => {
    if (!storageStore?.[key]) {
      return;
    }
    delete storageStore[key];
    setStorageStore(storageStore);
  };

  const upsert = (key: string, value: ChannelEnum) => {
    setStorageStore({ ...(storageStore || {}), [key]: value });
  };

  return [{ remove, upsert }, storageStore, setStorageStore] as const;
};

export const useChannelSelectStorage = Object.assign(_useChannelSelectStorage, {
  STORAGE_KEY,
  IGNORE_STORAGE_KEY,
});
