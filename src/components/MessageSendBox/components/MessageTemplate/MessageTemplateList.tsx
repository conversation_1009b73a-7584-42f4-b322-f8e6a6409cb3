import { type MessageTemplateSimpleView } from '@moego/api-web/moego/models/message/v1/message_template_models';
import { Spin } from '@moego/ui';
import React, { memo } from 'react';
import ImageOrangeOpenMouthCatPng from '../../../../assets/image/orange-open-mouth-cat.png';
import { Switch } from '../../../../components/SwitchCase';
import { WithPricingEnableUpgrade } from '../../../Pricing/WithPricingComponents';
import { AddNewTemplate } from './AddNewTemplate';
import { MessageTemplateItem } from './MessageTemplateItem';

interface MessageTemplateListProps {
  onAddNew: () => void;
  onEdit: (item: MessageTemplateSimpleView) => void;
  templateList: MessageTemplateSimpleView[];
  haveViewSettingPermission: boolean;
  onSelect: (item: MessageTemplateSimpleView) => Promise<void>;
  loading?: boolean;
  onShowDeleteModal: (id: string) => void;
}

export const MessageTemplateList = memo<MessageTemplateListProps>((props) => {
  const { templateList, haveViewSettingPermission, onSelect, onAddNew, loading, onEdit, onShowDeleteModal } = props;

  return (
    <Switch>
      <Switch.Case if={templateList?.length}>
        <div>
          <Spin
            isLoading={loading}
            classNames={{
              base: 'moe-w-full moe-h-full',
              container: 'moe-w-full moe-h-full',
              iconContainer: '!moe-top-[200px]',
            }}
          >
            <div className="moe-flex moe-flex-col moe-gap-y-m">
              {templateList.map((item, index) => {
                return (
                  <Switch key={item.id}>
                    <Switch.Case if={item.isSystem}>
                      <MessageTemplateItem
                        item={item}
                        key={item.id}
                        onSelect={onSelect}
                        onShowDeleteModal={onShowDeleteModal}
                        onEdit={onEdit}
                        haveViewSettingPermission={haveViewSettingPermission}
                      />
                    </Switch.Case>
                    <Switch.Case else>
                      <WithPricingEnableUpgrade permission="savedReply" key={index} overrideEvent={'onCapture'}>
                        <MessageTemplateItem
                          item={item}
                          key={item.id}
                          onSelect={onSelect}
                          onShowDeleteModal={onShowDeleteModal}
                          onEdit={onEdit}
                          haveViewSettingPermission={haveViewSettingPermission}
                        />
                      </WithPricingEnableUpgrade>
                    </Switch.Case>
                  </Switch>
                );
              })}
            </div>
          </Spin>
        </div>
      </Switch.Case>
      <Switch.Case else>
        <div>
          <Spin
            isLoading={loading}
            classNames={{
              base: 'moe-w-full moe-h-full',
              container: 'moe-w-full moe-h-full',
              iconContainer: '!moe-top-[200px]',
            }}
          >
            <div className="moe-flex moe-flex-col moe-items-center">
              <img src={ImageOrangeOpenMouthCatPng} width={65} className="moe-mt-[23px]" />
              <div className="moe-text-[#666] moe-leading-[20px] moe-text-[14px] moe-mt-[16px] moe-mb=[8px]">
                No result found
              </div>
              <AddNewTemplate visible={haveViewSettingPermission} onAddNew={onAddNew} align="center" />
            </div>
          </Spin>
        </div>
      </Switch.Case>
    </Switch>
  );
});
