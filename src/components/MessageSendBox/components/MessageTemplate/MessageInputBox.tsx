import { type MessageTemplateSimpleView } from '@moego/api-web/moego/models/message/v1/message_template_models';
import { Form, cn, useController, type useForm } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useEffect, useMemo, useRef } from 'react';
import { insertText } from '../../../../container/settings/Agreement/components/EditTemplate';
import { getCustomerUpcomingUrl } from '../../../../store/grooming/grooming.actions';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { Condition } from '../../../Condition';
import { ALLOW_TEXT_LENGTH_TWOWAY, TEXT_MAX_LENGTH, getSmsSegments } from '../../components/MessageUtils';
import { type MessageSendBoxProps } from '../../type';
import { type ChannelEnum } from '../ChannelSelect/ChannelSelect';
import { MessageBoxBtnGroup } from '../MessageBoxBtnGroup';
import { MessageCostTooltip } from '../MessageCostTooltip';

type MessageInputBoxProps = MessageSendBoxProps & {
  templateContent: string;
  formRef: ReturnType<typeof useForm<MessageTemplateSimpleView>>;
  channel?: ChannelEnum;
};

export const MessageInputBox = memo<MessageInputBoxProps>((props) => {
  const {
    customerId,
    maxLength = TEXT_MAX_LENGTH,
    shareAppt = false,
    messagePlaceholder,
    formRef,
    templateContent,
    channel,
  } = props;
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const dispatch = useDispatch();

  const { field, fieldState } = useController({
    name: 'templateContent',
    control: formRef.control,
    defaultValue: '',
    rules: {
      required: true,
    },
  });

  const isValueInvalid = fieldState.invalid;
  const setTextContent = useLatestCallback((text: string) => {
    formRef.setValue('templateContent', text, { shouldDirty: true, shouldValidate: true });
  });

  const { remainLength: smsRemainLength, smsSegmentLength } = useMemo(
    () => getSmsSegments(templateContent?.trim(), ALLOW_TEXT_LENGTH_TWOWAY),
    [templateContent],
  );
  const handleInsertText = useLatestCallback(async (text: string) => {
    await Promise.resolve(); // 防止光标跳到行首
    const txt = insertText(text, inputRef.current);
    setTextContent(txt);
  });

  useEffect(() => {
    inputRef.current?.focus();
  }, [customerId]);

  useEffect(() => {
    if (shareAppt) {
      dispatch(getCustomerUpcomingUrl(customerId)).then((res) => setTextContent(insertText(res, inputRef.current)));
    }
  }, [shareAppt]);

  return (
    <div>
      <Form.Label label="Message" />
      <div className="send-box-wrapper">
        <div
          className={cn('send-box !moe-border !moe-border-button moe-pb-[16px]', {
            '!moe-border-danger': isValueInvalid,
          })}
        >
          <div className="input-wrapper !moe-pt-[16px]">
            <textarea
              className="moe-font-manrope moe-font-regular moe-text-regular-short"
              maxLength={maxLength}
              value={templateContent}
              onChange={(e) => field.onChange(e.target.value)}
              onBlur={() => field.onBlur()}
              placeholder={messagePlaceholder || 'Write a message'}
              ref={inputRef}
            />
            <Condition if={templateContent?.length > 0}>
              <MessageCostTooltip smsRemainLength={smsRemainLength} smsSegmentLength={smsSegmentLength} />
            </Condition>
          </div>
          <div className="widget-wrapper">
            <div className="widget-btn-group">
              <MessageBoxBtnGroup customerId={customerId} onSelect={handleInsertText} channel={channel} />
            </div>
          </div>
        </div>
        <Condition if={isValueInvalid}>
          <Form.HelpText errorMessage="Please enter content." className="moe-mt-xxs" />
        </Condition>
      </div>
    </div>
  );
});
