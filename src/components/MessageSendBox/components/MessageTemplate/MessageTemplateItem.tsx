import { type MessageTemplateSimpleView } from '@moego/api-web/moego/models/message/v1/message_template_models';
import { MinorEditOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { Heading, IconButton, Tag, Text } from '@moego/ui';
import React, { memo } from 'react';
import { Condition } from '../../../Condition';
import { usePricingPermissionAccess } from '../../../Pricing/pricing.hooks';

export interface MessageTemplateItemProps {
  item: MessageTemplateSimpleView;
  onSelect: (item: MessageTemplateSimpleView) => void;
  onEdit: (item: MessageTemplateSimpleView) => void;
  onShowDeleteModal: (id: string) => void;
  haveViewSettingPermission: boolean;
  onCapture?: () => void;
}

export const MessageTemplateItem = memo(function MessageTemplateItem(props: MessageTemplateItemProps) {
  const { item, onEdit, onSelect, onShowDeleteModal, haveViewSettingPermission, onCapture } = props;
  const { access } = usePricingPermissionAccess('savedReply');
  return (
    <div
      onClick={
        onCapture
          ? onCapture
          : () => {
              if (!item.isSystem) {
                onSelect(item);
              } else {
                onEdit(item);
              }
            }
      }
      className="moe-group moe-cursor-pointer hover:moe-bg-neutral-sunken-0 moe-p-8px-100 moe-rounded-8px-100 moe-mx-[-8px]"
    >
      <div className="moe-flex moe-items-center moe-h-[20px] moe-mb-[8px] moe-justify-between">
        <div className="moe-flex moe-items-center moe-gap-x-xs">
          <Heading size="5" className="moe-text-primary">
            {item.templateName}
          </Heading>
          <Condition if={item.isSystem}>
            <Tag variant="outlined" color="neutral" label="Appointment related" className="moe-max-w-[200px]" />
          </Condition>
        </div>
        <Condition if={haveViewSettingPermission && access}>
          <div className="moe-flex moe-items-center moe-gap-x-xs">
            <IconButton
              tooltip="Edit"
              size="s"
              icon={<MinorEditOutlined />}
              className="moe-shrink-0 moe-cursor-pointer group-hover:!moe-inline-flex !moe-hidden"
              onPress={() => onEdit(item)}
            ></IconButton>
            <Condition if={!item.isSystem}>
              <IconButton
                tooltip="Delete"
                size="s"
                className="moe-shrink-0 moe-cursor-pointer group-hover:!moe-inline-flex !moe-hidden"
                icon={<MinorTrashOutlined />}
                onPress={() => onShowDeleteModal(item.id)}
              ></IconButton>
            </Condition>
          </div>
        </Condition>
      </div>
      <Text className="moe-text-primary moe-line-clamp-3" variant="regular">
        {item.templateContent}
      </Text>
    </div>
  );
});
