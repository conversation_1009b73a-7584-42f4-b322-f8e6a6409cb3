import { MinorPlusOutlined } from '@moego/icons-react';
import { Button } from '@moego/ui';
import React, { memo } from 'react';
import { Condition } from '../../../../components/Condition';

interface AddNewTemplateProps {
  visible: boolean;
  onAddNew: () => void;
  align?: 'start' | 'center' | 'end';
}

export const AddNewTemplate = memo<AddNewTemplateProps>((props) => {
  const { visible, onAddNew, align = 'start' } = props;
  return (
    <Condition if={visible}>
      <Button
        variant="tertiary"
        align={align}
        icon={<MinorPlusOutlined />}
        onPress={onAddNew}
        size="s"
        className="moe-shrink-0"
      >
        Add new
      </Button>
    </Condition>
  );
});
