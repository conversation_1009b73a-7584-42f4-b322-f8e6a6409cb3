import { useFeature } from '@growthbook/growthbook-react';
import { type MessageTemplateSimpleView } from '@moego/api-web/moego/models/message/v1/message_template_models';
import { Alert, Drawer, Input, Modal, Tabs, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useState } from 'react';
import { useDebounce } from 'react-use';
import ImageDataEmptyPng from '../../../../assets/image/data-empty.png';
import { Switch } from '../../../../components/SwitchCase';
import { type AutoMessageTipsMetaData } from '../../../../container/settings/Settings/AutoMessageSetting/types';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import {
  deleteMessageTemplate,
  getMessageTemplates,
  getRenderedMessage,
} from '../../../../store/message/messageTemplate.actions';
import {
  selectEnterpriseTemplateMessageList,
  selectTemplateMessageCustomList,
  selectTemplateMessageSystemList,
} from '../../../../store/message/messageTemplate.selectors';
import { META_DATA_KEY_LIST } from '../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../store/metadata/metadata.hooks';
import { Z_INDEX_MODAL } from '../../../../style/_variables';
import { useBool } from '../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { Condition } from '../../../Condition';
import { useMessageSendContext } from '../../MessageSendContext';
import { AddNewTemplate } from './AddNewTemplate';
import { MessageTemplateList } from './MessageTemplateList';
interface MessageTemplateDrawerProps {
  visible?: boolean;
  onClose: () => void;
  onAddNew: () => void;
  onSelect: (message: string) => void;
  onEdit: (item: MessageTemplateSimpleView) => void;
  searchWord: string;
  setSearchWord: (searchWord: string) => void;
}

export interface MessageTemplateDrawerRef {
  refresh: () => void;
}

enum TabKey {
  Custom = 'Custom',
  System = 'System',
  Corporate = 'Corporate',
}

export const MessageTemplateDrawer = (props: MessageTemplateDrawerProps) => {
  const { visible, onClose, onAddNew, onSelect, onEdit, searchWord, setSearchWord } = props;
  const [data, setData, loading] = useMetaData<AutoMessageTipsMetaData>(META_DATA_KEY_LIST.AutoMessageTips);
  const dispatch = useDispatch();
  const [templateList, systemTemplateList, enterpriseTemplateList, rolePermission] = useSelector(
    selectTemplateMessageCustomList(),
    selectTemplateMessageSystemList(),
    selectEnterpriseTemplateMessageList(),
    selectCurrentPermissions(),
  );
  const haveViewSettingPermission = rolePermission.has('viewSetting');
  const deleteModalVisible = useBool(false);
  const enterpriseDemo = useFeature('enterprise_demo');
  const [currentTemplateId, setCurrentTemplateId] = useState<string>('');
  const {
    isMassText,
    messageSendBoxProps: { customerId } = {},
  } = useMessageSendContext();
  const [debouncedSearchWord, setDebouncedSearchWord] = useState<string>(searchWord);

  const fetchMessageTemplates = useSerialCallback(async () => {
    setDebouncedSearchWord(searchWord);
    await dispatch(getMessageTemplates({ keyword: searchWord }));
  });

  useDebounce(fetchMessageTemplates, 500, [searchWord]);

  const handleDelete = useSerialCallback(async () => {
    await dispatch(deleteMessageTemplate(currentTemplateId, searchWord));
    deleteModalVisible.close();
  });

  const handleEdit = (item: MessageTemplateSimpleView) => {
    onEdit(item);
  };

  const handleShowDeleteModal = (id: string) => {
    setCurrentTemplateId(id);
    deleteModalVisible.open();
  };

  const handleSelect = useSerialCallback(async (item: MessageTemplateSimpleView) => {
    let text = item.templateContent;
    if (!isMassText && customerId) {
      text = await dispatch(getRenderedMessage({ id: item.id, customerId: String(customerId) }));
    }
    onSelect?.(text);
    onClose?.();
  });

  const handleAlertClose = () => {
    setData({
      ...data,
      hideSystemSavedReplyTips: true,
    });
  };

  return (
    <>
      <Drawer
        // 这里设置这个的原因是内部 modal 打开的 emoji picker 如果不设置这个就无法滚动
        isBlockScroll={false}
        isOpen={visible}
        onClose={onClose}
        className="moe-w-[480px]"
        size="s"
        title={
          <div className="moe-flex moe-items-center">
            <div>Saved replies</div>
          </div>
        }
        zIndex={Z_INDEX_MODAL - 1}
        footer={null}
      >
        <Tabs
          classNames={{
            panel: 'moe-pt-m',
          }}
        >
          <Tabs.Item key={TabKey.Custom} label={TabKey.Custom}>
            <Switch>
              <Switch.Case if={!templateList?.length && !debouncedSearchWord && !fetchMessageTemplates.isBusy()}>
                <div className="moe-flex moe-flex-col moe-items-center moe-mt-[58px]">
                  <img src={ImageDataEmptyPng} className="moe-h-[59px] moe-w-[86px]" />
                  <div className="moe-text-[#ccc] moe-leading-[20px] moe-text-[16px] moe-mt-[15px] moe-mb-[7px]">
                    No saved replies
                  </div>
                  <AddNewTemplate visible={haveViewSettingPermission} onAddNew={onAddNew} />
                </div>
              </Switch.Case>
              <Switch.Case else>
                <div className="moe-mb-m moe-flex moe-justify-between moe-items-center moe-gap-x-8px-300">
                  <Input.Search
                    placeholder="Search"
                    onChange={(e) => {
                      setSearchWord(e);
                    }}
                    value={searchWord}
                    className="moe-w-full"
                  />
                  <AddNewTemplate visible={haveViewSettingPermission} onAddNew={onAddNew} />
                </div>
                <MessageTemplateList
                  templateList={templateList}
                  haveViewSettingPermission={haveViewSettingPermission}
                  onSelect={handleSelect}
                  onAddNew={onAddNew}
                  loading={handleSelect.isBusy() || fetchMessageTemplates.isBusy()}
                  onEdit={handleEdit}
                  onShowDeleteModal={handleShowDeleteModal}
                />
              </Switch.Case>
            </Switch>
          </Tabs.Item>
          <Tabs.Item key={TabKey.System} label={TabKey.System}>
            <Condition if={!loading && !data?.hideSystemSavedReplyTips}>
              <Alert
                title=""
                isBordered
                classNames={{ content: 'moe-items-center' }}
                onClose={handleAlertClose}
                className="moe-mb-m"
              >
                <Text variant="small" className="moe-text-primary">
                  System saved replies are pre-existing templates stored in the system. You can use or edit these
                  templates based on your needs.
                </Text>
              </Alert>
            </Condition>
            <MessageTemplateList
              templateList={systemTemplateList}
              haveViewSettingPermission={haveViewSettingPermission}
              onSelect={handleSelect}
              onAddNew={onAddNew}
              loading={handleSelect.isBusy() || fetchMessageTemplates.isBusy()}
              onEdit={handleEdit}
              onShowDeleteModal={handleShowDeleteModal}
            />
          </Tabs.Item>
          {enterpriseDemo?.value ? (
            <Tabs.Item key={TabKey.Corporate} label={TabKey.Corporate}>
              <Switch>
                <Switch.Case if={!enterpriseTemplateList?.length}>
                  <div className="moe-flex moe-flex-col moe-items-center moe-mt-[58px]">
                    <img src={ImageDataEmptyPng} className="moe-h-[59px] moe-w-[86px]" />
                    <div className="moe-text-[#ccc] moe-leading-[20px] moe-text-[16px] moe-mt-[15px] moe-mb-[7px]">
                      No enterprise replies
                    </div>
                  </div>
                </Switch.Case>
                <Switch.Case else>
                  <MessageTemplateList
                    templateList={enterpriseTemplateList}
                    haveViewSettingPermission={false}
                    onSelect={handleSelect}
                    onAddNew={() => void 0}
                    loading={handleSelect.isBusy() || fetchMessageTemplates.isBusy()}
                    onEdit={() => void 0}
                    onShowDeleteModal={() => void 0}
                  />
                </Switch.Case>
              </Switch>
            </Tabs.Item>
          ) : (
            <></>
          )}
        </Tabs>
      </Drawer>
      <Modal
        className="moe-w-[480px]"
        title="Delete saved reply"
        isOpen={deleteModalVisible.value}
        onClose={deleteModalVisible.close}
        onCancel={deleteModalVisible.close}
        onConfirm={handleDelete}
        confirmButtonProps={{
          color: 'danger',
        }}
        cancelText="No, keep"
        confirmText="Yes, delete"
      >
        <Text variant="regular" className="moe-text-primary">
          Are you sure to delete this saved reply?
        </Text>
      </Modal>
    </>
  );
};
