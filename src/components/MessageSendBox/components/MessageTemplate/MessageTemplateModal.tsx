import { type MessageTemplateSimpleView } from '@moego/api-web/moego/models/message/v1/message_template_models';
import { MinorInfoOutlined } from '@moego/icons-react';
import { Button, Form, Input, Modal, Text, Tooltip, useForm, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import {
  checkMessageTemplateNameExist,
  createMessageTemplate,
  getMessageTemplatePlaceholders,
  updateMessageTemplate,
  updateSystemMessageTemplate,
} from '../../../../store/message/messageTemplate.actions';
import { Z_INDEX_MODAL } from '../../../../style/_variables';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { Condition } from '../../../Condition';
import { WithPricingEnableUpgrade } from '../../../Pricing/WithPricingComponents';
import { useMessageSendContext } from '../../MessageSendContext';
import { type ChannelEnum } from '../ChannelSelect/ChannelSelect';
import { MessageInputBox } from './MessageInputBox';

type MessageTemplateModalProps = {
  visible: boolean;
  onModalClose: () => void;
  onSave?: () => void;
  onCreate?: () => void;
  searchWord: string;
  currentTemplateItem?: MessageTemplateSimpleView;
  channel?: ChannelEnum;
};
export type MessageTemplateModalRef = {
  setFieldValue: (params: MessageTemplateSimpleView) => void;
};

export const MessageTemplateModal = forwardRef<MessageTemplateModalRef, MessageTemplateModalProps>((props, ref) => {
  const { visible, onModalClose, currentTemplateItem, onSave, onCreate, searchWord = '', channel } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const form = useForm<MessageTemplateSimpleView>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      templateContent: '',
      templateName: '',
    },
  });
  const dispatch = useDispatch();
  const { updatedAt = '', updaterName, id, useCase } = currentTemplateItem || {};
  const { messageSendBoxProps } = useMessageSendContext();

  const [templateContent] = useWatch({
    control: form.control,
    name: ['templateContent', 'templateName'],
  });

  useEffect(() => {
    dispatch(
      getMessageTemplatePlaceholders(
        useCase
          ? {
              useCase,
            }
          : undefined,
      ),
    );
  }, [useCase]);

  const isSystemTemplate = !!currentTemplateItem?.isSystem;

  const handleSubmit = useSerialCallback(async () => {
    await form.handleSubmit(async (value) => {
      const templateContent = value?.templateContent?.trim();
      const templateName = value?.templateName?.trim();

      if (!templateContent || !templateName) {
        return;
      }

      if (id) {
        onSave?.();
        await dispatch(
          isSystemTemplate
            ? updateSystemMessageTemplate({ id, messageTemplateDef: { templateName, templateContent } })
            : updateMessageTemplate(searchWord, {
                id,
                messageTemplateDef: {
                  templateName,
                  templateContent,
                },
              }),
        );
      } else {
        onCreate?.();
        await dispatch(
          createMessageTemplate({
            messageTemplateDef: {
              templateName,
              templateContent,
            },
          }),
        );
      }
      handleClose();
    })();
  });

  const handleClose = () => {
    onModalClose();
    form.reset(
      {
        templateContent: '',
        templateName: '',
      },
      {
        keepErrors: false,
      },
    );
  };

  const handleSetFieldValue = (params: MessageTemplateSimpleView) => {
    const { templateContent, templateName } = params;
    setTimeout(() => {
      form.reset({ templateContent, templateName }, { keepDefaultValues: false, keepDirty: false, keepErrors: false });
    }, 0);
  };

  useImperativeHandle(ref, () => ({
    setFieldValue: handleSetFieldValue,
  }));

  const validateTitleConflict = async () => {
    const templateName = form.getValues('templateName');
    const { templateName: initTemplateName } = currentTemplateItem || {};

    if (!templateName) {
      return true;
    }

    try {
      const res = await dispatch(checkMessageTemplateNameExist(templateName));
      if (res?.exist && initTemplateName?.toLocaleLowerCase() !== templateName?.toLocaleLowerCase()) {
        return 'Template title already in use. Please choose a unique one.';
      }
    } catch (error) {}

    return true;
  };

  if (!messageSendBoxProps) {
    return null;
  }

  return (
    <Modal
      title={id ? 'Edit' : 'Add new'}
      onClose={handleClose}
      isOpen={visible}
      isMaskCloseable={false}
      isBlockScroll={false}
      className="moe-w-[540px]"
      zIndex={Z_INDEX_MODAL}
      footer={
        <div className="moe-flex moe-w-full moe-place-content-between moe-items-center moe-gap-s">
          <Button variant="secondary" onPress={handleClose}>
            Cancel
          </Button>
          {currentTemplateItem?.isSystem ? (
            <Button onPress={handleSubmit} isDisabled={handleSubmit.isBusy()} isLoading={handleSubmit.isBusy()}>
              Save
            </Button>
          ) : (
            <WithPricingEnableUpgrade permission="savedReply" overrideEvent={'onPress'}>
              <Button onPress={handleSubmit} isDisabled={handleSubmit.isBusy()} isLoading={handleSubmit.isBusy()}>
                Save
              </Button>
            </WithPricingEnableUpgrade>
          )}
        </div>
      }
    >
      <div className="message-template-container">
        <Condition if={currentTemplateItem?.isSystem}>
          <div className="moe-flex moe-items-center moe-gap-x-xxs moe-mb-m">
            <MinorInfoOutlined className="moe-text-tertiary" />
            <Text variant="caption" className="moe-text-tertiary">
              Appointment related template can’t be directly used in message center. Please send in the appointment
              details.
            </Text>
          </div>
        </Condition>
        <Form form={form} footer={null}>
          <div className="moe-relative">
            <Form.Item
              name="templateName"
              label="Title"
              rules={{
                required: 'Please enter a message title.',
                maxLength: 30,
                validate: async (value) => {
                  const pattern = /^[a-zA-Z]([a-zA-Z0-9---\s]+)?$/;
                  if (!pattern.test(value)) {
                    return 'The title must begin with a letter and may only contain letters, numbers, and hyphens.';
                  }
                  return await validateTitleConflict();
                },
              }}
            >
              <Input placeholder="Message title" maxLength={60} isDisabled={currentTemplateItem?.isSystem} />
            </Form.Item>
            <Condition if={currentTemplateItem?.isSystem}>
              <Tooltip content="Title cannot be edit" side="top">
                <div className="moe-w-full moe-absolute moe-h-[40px] moe-top-[25px]"></div>
              </Tooltip>
            </Condition>
          </div>

          <MessageInputBox
            {...messageSendBoxProps}
            formRef={form}
            templateContent={templateContent}
            allowAgreementVariable
            allowApptVariable
            allowPetParentPortalVariable
            allowPetParentAppDownloadVariable
            allowRequestCofVariable
            channel={channel}
          />
        </Form>
        <Condition if={updatedAt && updaterName}>
          <div className="moe-text-[14px] moe-leading-[18px] moe-text-[#999] moe-mt-[24px] moe-font-manrope">
            Last update: {business.formatDateTime(dayjs(updatedAt))} by {updaterName}
          </div>
        </Condition>
      </div>
    </Modal>
  );
});
