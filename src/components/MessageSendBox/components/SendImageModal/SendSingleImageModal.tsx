import { Modal, type UploadItem, UploadStatus, toast } from '@moego/ui';
import { clone } from 'lodash';
import React, { memo } from 'react';
import { downloadFromFile } from '../../../../utils/download';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { displaySize } from '../../../../utils/misc';
import { createUploadHandler } from '../../../Upload/AvatarUpload';
import { MoeGoUIUpload } from '../../../Upload/MoeGoUIUpload';
import { type PicInfo, SINGLE_IMAGE_MAX_SIZE_PER_IMAGE, type SendImageModalBaseProps } from './types';

/**
 * 需要用到压缩后的 fileSize 信息
 */
const uploadHandler = createUploadHandler({ withFileSize: true, compressStrategy: 'any' });

export interface SendSingleImageModalProps extends SendImageModalBaseProps {
  pic: UploadItem | undefined;
  setPic: (pic: UploadItem | undefined) => void;
}

export const SendSingleImageModal = memo<SendSingleImageModalProps>((props) => {
  const { onSend, onClose, visible, pic, setPic } = props;

  // 没有选择图片，或者照片不是 ready 的时候，发送按钮不可用
  const btnDisabled = !pic || (pic && pic.status !== UploadStatus.success);

  /**
   * 添加/删除照片 etc.
   * 此处的 fileList 一定只有一张图片或者没有图片
   */
  const handleChange = useLatestCallback((fileList: UploadItem[], file: UploadItem) => {
    const currentPic = clone(fileList?.[0]); // 返回的 item 引用总是不变，因此需要 clone 一份，否则最外层拿到的 pic.status 会不对
    if (!currentPic) {
      // 删除照片时，置空
      setPic(undefined);
      return;
    }

    // 当图片到达终态，check 压缩后是否依然超过限制
    if (currentPic.status === 'success' || currentPic.status === 'error') {
      const fileSize = (currentPic?.response as PicInfo)?.fileSize;
      const isWithinSize = !!fileSize && fileSize > 0 && fileSize <= SINGLE_IMAGE_MAX_SIZE_PER_IMAGE;
      if (isWithinSize) {
        // 没超过限制，直接 set
        setPic(currentPic);
      } else {
        // 超过限制，置空并弹窗提示
        toast({
          type: 'error',
          title: `Upload failed: the file size should be less than ${displaySize(SINGLE_IMAGE_MAX_SIZE_PER_IMAGE)}`,
        });
        setPic(undefined);
      }
    } else {
      // 非终态，直接 set
      setPic(currentPic);
    }
  });

  /**
   * 下载原图
   */
  const handleDownload = useLatestCallback((file: UploadItem) => {
    if (!file?.originFile) {
      return;
    }

    downloadFromFile(file.originFile, file.originFile?.name || 'image');
  });

  return (
    <Modal
      size="s"
      title="Send image"
      confirmText="Send"
      isOpen={visible}
      onClose={onClose}
      onConfirm={onSend}
      onCancel={onClose}
      confirmButtonProps={{
        isDisabled: btnDisabled,
      }}
    >
      <div className="moe-flex moe-items-center moe-justify-center">
        <MoeGoUIUpload
          value={pic ? [pic] : undefined}
          maxCount={1}
          isMultiple={false}
          variant="image-card"
          placeholderLabel="Upload image"
          onChange={handleChange}
          onDownload={handleDownload}
          customRequest={uploadHandler}
        />
      </div>
    </Modal>
  );
});
