import { Modal, Text, type UploadItem, UploadStatus, toast } from '@moego/ui';
import React, { memo } from 'react';
import { withPl } from '../../../../utils/calculator';
import { downloadFromFile } from '../../../../utils/download';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { displaySize } from '../../../../utils/misc';
import { createUploadHandler } from '../../../Upload/AvatarUpload';
import { MoeGoUIUpload } from '../../../Upload/MoeGoUIUpload';
import {
  MULTIPLE_IMAGES_COST_SMS_COUNT,
  MULTIPLE_IMAGES_MAX_COUNT,
  MULTIPLE_IMAGES_MAX_SIZE_PER_IMAGE,
  type PicInfo,
  type SendImageModalBaseProps,
} from './types';

/**
 * twilio MMS 不支持 webp 格式，只能选择其他 compressStrategy
 * https://www.twilio.com/docs/messaging/guides/accepted-mime-types
 */
const uploadHandler = createUploadHandler({ withFileSize: true, compressStrategy: 'any' });

export interface SendMultipleImagesModalProps extends SendImageModalBaseProps {
  picList: UploadItem[];
  setPicList: (picList: UploadItem[]) => void;
}

export const SendMultipleImagesModal = memo<SendMultipleImagesModalProps>((props) => {
  const { onSend, onClose, visible, picList, setPicList } = props;

  // 没有选择图片，或者不是所有照片都 ready 的时候，发送按钮不可用
  const btnDisabled = !picList?.length || picList?.some((item) => item.status !== UploadStatus.success);

  const smsCostText =
    picList.length === 1
      ? `${MULTIPLE_IMAGES_COST_SMS_COUNT} SMS will be used`
      : `Up to ${MULTIPLE_IMAGES_COST_SMS_COUNT * picList.length} SMS will be used`;

  // 添加/删除照片 etc.
  const handleChange = useLatestCallback((fileList: UploadItem[], file: UploadItem) => {
    // 当所有图片都到达终态，此时筛除压缩后依然超过限制的图片，并弹窗提示
    if (fileList.every((item) => item.status === 'success' || item.status === 'error')) {
      const withinSizePicList = fileList.filter((item) => {
        const fileSize = (item?.response as PicInfo)?.fileSize;
        const isWithinSize = !!fileSize && fileSize > 0 && fileSize <= MULTIPLE_IMAGES_MAX_SIZE_PER_IMAGE;
        return isWithinSize;
      });
      const exceedSizePicCount = fileList.length - withinSizePicList.length;
      if (exceedSizePicCount > 0) {
        toast({
          type: 'error',
          title: `${withPl(exceedSizePicCount, 'photo')} upload failed: the file size should be less than ${displaySize(MULTIPLE_IMAGES_MAX_SIZE_PER_IMAGE)}`,
        });
      }
      setPicList(withinSizePicList);
    } else {
      setPicList(fileList);
    }
  });

  const handleDownload = useLatestCallback((file: UploadItem) => {
    // 下载原图
    if (!file?.originFile) {
      return;
    }

    downloadFromFile(file.originFile, file.originFile?.name || 'image');
  });

  return (
    <Modal
      size="s"
      title="Send image(s)"
      confirmText="Send"
      isOpen={visible}
      onClose={onClose}
      onConfirm={onSend}
      onCancel={onClose}
      confirmButtonProps={{
        isDisabled: btnDisabled,
      }}
      showTertiaryButton={picList.length > 0}
      tertiaryText={<Text variant="small">{smsCostText}</Text>}
      tertiaryButtonProps={{
        className: 'moe-cursor-text moe-text-tertiary',
        classNames: {
          text: 'moe-border-b-0',
        },
      }}
    >
      <MoeGoUIUpload
        className="moe-mt-8px-150"
        maxCount={MULTIPLE_IMAGES_MAX_COUNT}
        isMultiple={true}
        variant="image-card"
        placeholderLabel="Upload image"
        placeholderDescription={`Up to ${MULTIPLE_IMAGES_MAX_COUNT}`}
        value={picList}
        onChange={handleChange}
        customRequest={uploadHandler}
        onExceedMaxCount={() => {
          toast({
            type: 'error',
            title: `Upload failed: Up to ${withPl(MULTIPLE_IMAGES_MAX_COUNT, 'photo', 'photos')} allowed.`,
          });
        }}
        onDownload={handleDownload}
      />
    </Modal>
  );
});
