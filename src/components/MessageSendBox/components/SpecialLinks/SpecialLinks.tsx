import { MinorLinkOutlined } from '@moego/icons-react';
import { Dropdown, IconButton } from '@moego/ui';
import React, { memo } from 'react';
import { MessageTestIds } from '../../../../config/testIds/message';
import { type MessageSendBoxProps } from '../../type';
import { type ChannelEnum } from '../ChannelSelect/ChannelSelect';
import { useRenderAgreementDropdown } from './agreement.hooks';
import { useRenderCardOnFileDropdown } from './cardOnFile.hooks';
import { useRenderIntakeFormDropdown } from './intakeForm.hooks';
import { useRenderMembershipDropdown } from './membership.hooks';
import { useRenderOnlineBookingDropdown } from './onlineBooking.hooks';
import { useRenderPetParentPortalAppDropdown } from './petParentPortalApp.hooks';
import { useRenderPetParentPortalWebDropdown } from './petParentPortalWeb.hooks';
import { type OnSelect } from './types';
import { useRenderUpcomingAppointmentDropdown } from './upcomingAppointment.hooks';

type GeneralProps = Pick<
  MessageSendBoxProps,
  | 'customerId'
  | 'allowOnlineBooking'
  | 'allowOnlineBookingVariable'
  | 'allowAgreement'
  | 'allowAgreementVariable'
  | 'allowAppt'
  | 'allowApptVariable'
  | 'allowIntakeForm'
  | 'allowIntakeFormVariable'
  | 'allowPetParentPortal'
  | 'allowPetParentPortalVariable'
  | 'allowPetParentAppDownload'
  | 'allowPetParentAppDownloadVariable'
  | 'allowRequestCof'
  | 'allowRequestCofVariable'
>;

export interface SpecialLinksProps extends GeneralProps {
  allowMembership?: boolean;
  allowMembershipVariable?: boolean;
  onSelect: OnSelect;
  showCopy?: boolean;
  channel?: ChannelEnum;
}

export const SpecialLinks = memo<SpecialLinksProps>(
  ({
    onSelect,
    customerId,
    allowOnlineBooking,
    allowOnlineBookingVariable,
    allowAgreement,
    allowAgreementVariable,
    allowAppt,
    allowApptVariable,
    allowIntakeForm,
    allowIntakeFormVariable,
    allowPetParentPortal,
    allowPetParentPortalVariable,
    allowPetParentAppDownload,
    allowPetParentAppDownloadVariable,
    allowMembership,
    allowMembershipVariable,
    allowRequestCof,
    allowRequestCofVariable,
    showCopy,
    channel,
  }) => {
    const renderOnlineBookingDropdown = useRenderOnlineBookingDropdown({
      allowOnlineBooking,
      allowOnlineBookingVariable,
      onSelect,
      showCopy,
    });
    const renderAgreementDropdown = useRenderAgreementDropdown({
      allowAgreement,
      allowAgreementVariable,
      onSelect,
      customerId,
      showCopy,
      channel,
    });
    const renderUpcomingAppointmentDropdown = useRenderUpcomingAppointmentDropdown({
      customerId,
      allowAppt,
      allowApptVariable,
      onSelect,
      showCopy,
    });
    const renderIntakeFormDropdown = useRenderIntakeFormDropdown({
      allowIntakeForm,
      allowIntakeFormVariable,
      onSelect,
      showCopy,
    });
    const renderPetParentPortalWebDropdown = useRenderPetParentPortalWebDropdown({
      customerId,
      allowPetParentPortal,
      allowPetParentPortalVariable,
      onSelect,
      showCopy,
    });
    const renderPetParentPortalAppDropdown = useRenderPetParentPortalAppDropdown({
      allowPetParentAppDownload,
      allowPetParentAppDownloadVariable,
      onSelect,
      showCopy,
    });
    const renderMembershipDropdown = useRenderMembershipDropdown({
      customerId,
      allowMembership,
      allowMembershipVariable,
      onSelect,
      showCopy,
    });
    const renderCardOnFileDropdown = useRenderCardOnFileDropdown({
      customerId,
      allowRequestCof,
      allowRequestCofVariable,
      onSelect,
      showCopy,
      channel,
    });

    return (
      <Dropdown side="top">
        <Dropdown.Trigger>
          <IconButton
            variant="primary"
            size="m"
            color="transparent"
            icon={<MinorLinkOutlined />}
            tooltip="Link"
            data-testid={MessageTestIds.MessageQuickSpecialLinkBtn}
          />
        </Dropdown.Trigger>
        <Dropdown.Menu selectionMode="none" autoFocus={false}>
          {renderOnlineBookingDropdown()}
          {renderAgreementDropdown()}
          {renderUpcomingAppointmentDropdown()}
          {renderIntakeFormDropdown()}
          {renderPetParentPortalWebDropdown()}
          {renderPetParentPortalAppDropdown()}
          {renderMembershipDropdown()}
          {renderCardOnFileDropdown()}
        </Dropdown.Menu>
      </Dropdown>
    );
  },
);
