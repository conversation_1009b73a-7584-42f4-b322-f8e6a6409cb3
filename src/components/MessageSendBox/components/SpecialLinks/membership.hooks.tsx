import { MinorMembershipOutlined } from '@moego/icons-react';
import { Button, Dropdown, Text, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { isNumber } from 'lodash';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useHistory } from 'react-router';
import { URL_CLIENT } from '../../../../config/host/const';
import { SubscriptionClient } from '../../../../middleware/clients';
import { PATH_ADD_OR_EDIT_MEMBERSHIP } from '../../../../router/paths';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import {
  getActiveMembershipList,
  getCustomerActiveSubscriptionList,
} from '../../../../store/membership/membership.actions';
import {
  selectActiveMembershipByCustomerId,
  selectCompanyActiveMembershipList,
} from '../../../../store/membership/membership.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { getMembershipTemplateWithVariable } from '../../../../utils/messageVariable';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { type OnSelect, SpecialLinkType } from './types';

export const useInitMembershipDropdown = ({
  customerId,
  allowMembership,
  allowMembershipVariable,
}: Pick<MessageSendBoxProps, 'customerId'> & {
  allowMembership?: boolean;
  allowMembershipVariable?: boolean;
}) => {
  const dispatch = useDispatch();

  useEffect(() => {
    if (isNormal(customerId) && !allowMembershipVariable) {
      dispatch(getCustomerActiveSubscriptionList(String(customerId)));
    }
    if (allowMembership) {
      dispatch(getActiveMembershipList());
    }
  }, [customerId, allowMembershipVariable, allowMembership]);
};

export const useHandleClickMembershipItem = ({
  customerId,
  allowMembershipVariable,
  onSelect,
}: Pick<MessageSendBoxProps, 'customerId'> & {
  onSelect: OnSelect;
  allowMembershipVariable?: boolean;
}) => {
  const [membershipList, businessId] = useSelector(selectCompanyActiveMembershipList, currentBusinessIdBox);

  return useCallback(
    async (membershipId: string) => {
      const membership = membershipList.find((membership) => membership.id === membershipId);
      if (!membership) {
        return;
      }
      if (allowMembershipVariable) {
        const template = getMembershipTemplateWithVariable(membership);
        onSelect(template);
        return;
      }
      const token = await createSellLink(customerId + '', membershipId, membership.revision, businessId + '');
      onSelect(` ${URL_CLIENT}/membership/buy/${token} `);
    },
    [allowMembershipVariable, membershipList, businessId, onSelect, customerId],
  );
};

export const useRenderMembershipDropdown = ({
  customerId,
  allowMembership,
  allowMembershipVariable,
  onSelect,
  showCopy: _showCopy,
}: Pick<MessageSendBoxProps, 'customerId'> & {
  onSelect: OnSelect;
  allowMembership?: boolean;
  allowMembershipVariable?: boolean;
  showCopy?: boolean;
}) => {
  const [membershipList, activeMembershipList, business] = useSelector(
    selectCompanyActiveMembershipList,
    selectActiveMembershipByCustomerId(String(customerId)),
    selectCurrentBusiness,
  );

  const history = useHistory();

  /**
   * The membership in this list should be disabled in the menu item.
   *
   * If the membership link is a placeholder, there is no need to filter the membership list. For example,
   * the membership list in the mass text.
   */
  // eslint-disable-next-line sonarjs/no-invariant-returns
  const activeMembershipListIdSet: Set<string> = useMemo(() => {
    const idSet = new Set<string>();
    if (allowMembershipVariable) {
      return idSet;
    }
    activeMembershipList.forEach((membership) => {
      membership.id && idSet.add(membership.id);
    });
    return idSet;
  }, [activeMembershipList, allowMembershipVariable]);

  const handleClickMembershipItem = useHandleClickMembershipItem({
    customerId,
    allowMembershipVariable,
    onSelect,
  });

  const renderMembershipDropdown = () => {
    const subMenuText = SpecialLinkType.mapLabels[SpecialLinkType.Membership].label;
    const emptyText = `You haven't set up memberships yet`;

    if (!allowMembership) {
      return <Dropdown.MenuItem textValue={subMenuText}></Dropdown.MenuItem>;
    }

    /**
     * 此处是 hack，因为 SubMenu 子组件不能是 <></>，用不了这种写法
     * <Dropdown.SubMenu>
     *    {
     *      membershipList.count() === 0
     *        ? null
     *        : <> {membershipList.toArray().map(_ => <Dropdown.MenuItem />)} </>
     *    }
     * </Dropdown.SubMenu>
     */
    const SPECIAL_ID = -1;
    const finalMembershipList = membershipList.count() === 0 ? [SPECIAL_ID] : membershipList.toArray();

    return (
      <Dropdown.SubMenu
        key={SpecialLinkType.Membership}
        textValue={subMenuText}
        title={subMenuText}
        icon={<MinorMembershipOutlined />}
      >
        {finalMembershipList.map((membership) => {
          if (!isNumber(membership)) {
            const membershipId = membership.id!;
            const isDisabled = membershipId && activeMembershipListIdSet.has(membershipId);

            const showCopy = isDisabled ? false : _showCopy;
            const membershipText = `${business.formatAmount(membership.price)}/${membership.billingCycleName()}`;

            if (isDisabled) {
              return (
                <Dropdown.MenuItem
                  isDisabled
                  key={`${SpecialLinkType.Membership}-${membershipId}`}
                  textValue={membershipText}
                >
                  <Tooltip content="Client already subscribed this membership." side="top">
                    <Text variant="small">{membership.name}</Text>
                    <Text variant="caption">{membershipText}</Text>
                  </Tooltip>
                </Dropdown.MenuItem>
              );
            }
            return (
              <Dropdown.MenuItem
                key={`${SpecialLinkType.Membership}-${membershipId}`}
                textValue={membershipText}
                title={membership.name}
                description={membershipText}
                onAction={() => {
                  reportData(ReportActionName.sellMembershipMessageLink);
                  handleClickMembershipItem(membershipId);
                }}
                classNames={
                  showCopy
                    ? {
                        container: 'moe-flex-row-reverse',
                        base: 'moe-py-[4px] moe-group',
                      }
                    : {}
                }
                icon={
                  showCopy ? (
                    <Copy
                      className="moe-opacity-0 group-hover:moe-opacity-100"
                      copyText={async () => {
                        reportData(ReportActionName.sellMembershipMessageLink);
                        const token = await createSellLink(
                          customerId + '',
                          membershipId,
                          membership.revision,
                          business.id + '',
                        );
                        return `${URL_CLIENT}/membership/buy/${token}`;
                      }}
                    />
                  ) : null
                }
              ></Dropdown.MenuItem>
            );
          }
          return (
            <Dropdown.MenuItem
              key={`${SpecialLinkType.Membership}-${SPECIAL_ID}`}
              textValue={emptyText}
              onAction={() => {
                history.push(PATH_ADD_OR_EDIT_MEMBERSHIP.build({ type: 'create' }));
              }}
            >
              <div className="moe-py-[6px] moe-px-spacing-xs moe-flex moe-flex-col moe-items-center">
                <Text variant="small">{emptyText}</Text>
                <Button variant="tertiary" size="s" className="moe-mt-m">
                  Setup
                </Button>
              </div>
            </Dropdown.MenuItem>
          );
        })}
      </Dropdown.SubMenu>
    );
  };

  useInitMembershipDropdown({ customerId, allowMembership, allowMembershipVariable });

  return renderMembershipDropdown;
};

const createSellLink = async (
  customerId: string,
  membershipId: string,
  membershipRevision: number,
  currentBusinessId: string,
) => {
  const res = await SubscriptionClient.createSellLink({
    sellLinkDef: {
      customerId,
      membershipId,
      membershipRevision,
    },
    businessId: currentBusinessId,
  });
  return res.publicToken;
};
