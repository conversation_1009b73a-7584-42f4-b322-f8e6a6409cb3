import { Dropdown, MinorFormOutlined } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useCallback, useEffect } from 'react';
import { URL_INTAKE_FORM } from '../../../../config/host/const';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { getIntakeFormFormList } from '../../../../store/intakeForm/intakeForm.actions';
import { type IntakeFormFormRecord, intakeFormFormMapBox } from '../../../../store/intakeForm/intakeForm.boxes';
import { selectBusinessIntakeFormFormList } from '../../../../store/intakeForm/intakeForm.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { getIntakeFormTemplateWithVariable } from '../../../../utils/messageVariable';
import { Condition } from '../../../Condition';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { type OnSelect, SpecialLinkType } from './types';

export const useInitIntakeFormDropdown = ({ allowIntakeForm }: Pick<MessageSendBoxProps, 'allowIntakeForm'>) => {
  const [businessId] = useSelector(currentBusinessIdBox);
  const dispatch = useDispatch();

  useEffect(() => {
    if (allowIntakeForm && isNormal(businessId)) {
      dispatch(getIntakeFormFormList());
    }
  }, [allowIntakeForm, businessId]);
};

export const useHandleClickIntakeFormItem = ({
  allowIntakeFormVariable,
  onSelect,
}: Pick<MessageSendBoxProps, 'allowIntakeFormVariable'> & {
  onSelect: OnSelect;
}) => {
  return useCallback(
    async (form: IntakeFormFormRecord) => {
      if (allowIntakeFormVariable) {
        const template = getIntakeFormTemplateWithVariable({ id: form.id, title: form.title });
        onSelect(template);
      } else {
        const urlLink = URL_INTAKE_FORM + form.previewLink();
        // prevent url from being unavailable due to continuous links
        onSelect(urlLink + '\u{20}');
      }
    },
    [allowIntakeFormVariable, onSelect],
  );
};

export const useRenderIntakeFormDropdown = ({
  allowIntakeForm,
  allowIntakeFormVariable,
  onSelect,
  showCopy,
}: Pick<MessageSendBoxProps, 'allowIntakeForm' | 'allowIntakeFormVariable'> & {
  onSelect: OnSelect;
  showCopy?: boolean;
}) => {
  const [intakeFormList, intakeFormFormMap] = useSelector(selectBusinessIntakeFormFormList, intakeFormFormMapBox);

  const handleClickIntakeFormItem = useHandleClickIntakeFormItem({
    allowIntakeFormVariable,
    onSelect,
  });

  const renderIntakeFormDropdown = () => {
    const subMenuText = SpecialLinkType.mapLabels[SpecialLinkType.IntakeForm].label;

    if (!allowIntakeForm) {
      return <Dropdown.MenuItem textValue={subMenuText}></Dropdown.MenuItem>;
    }

    return (
      <Dropdown.SubMenu
        key={SpecialLinkType.IntakeForm}
        textValue={subMenuText}
        title={subMenuText}
        icon={<MinorFormOutlined />}
      >
        {/* intake form 一定至少有一个，所以可以不用判断是否为空 */}
        {intakeFormList.toArray().map((id) => {
          const intakeForm = intakeFormFormMap.mustGetItem(id);
          const intakeFormText = intakeForm.title;

          return (
            <Dropdown.MenuItem
              key={`${SpecialLinkType.IntakeForm}-${intakeForm.id}`}
              textValue={intakeFormText}
              onAction={() => {
                handleClickIntakeFormItem(intakeForm);
              }}
              classNames={
                showCopy
                  ? {
                      base: 'moe-py-[4px] moe-group',
                    }
                  : {}
              }
            >
              <div className="moe-flex moe-items-center moe-font-manrope moe-text-s-20 moe-tracking-1 moe-font-regular moe-text-primary moe-w-full moe-gap-[4px]">
                {intakeFormText}
                <Condition if={showCopy}>
                  <Copy
                    className="moe-opacity-0 group-hover:moe-opacity-100"
                    copyText={() => {
                      const urlLink = URL_INTAKE_FORM + intakeForm.previewLink();
                      return urlLink;
                    }}
                  />
                </Condition>
              </div>
            </Dropdown.MenuItem>
          );
        })}
      </Dropdown.SubMenu>
    );
  };

  useInitIntakeFormDropdown({ allowIntakeForm });

  return renderIntakeFormDropdown;
};
