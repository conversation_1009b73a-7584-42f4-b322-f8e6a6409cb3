import { MinorUpcomingApptOutlined } from '@moego/icons-react';
import { Dropdown, Text, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useCallback, useEffect } from 'react';
import { getUpcomingAppointmentLink } from '../../../../store/grooming/grooming.actions';
import { selectUpcomingAppointmentLink } from '../../../../store/grooming/grooming.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { UpcomingApptLinkExp } from '../../../../utils/messageVariable';
import { Condition } from '../../../Condition';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { type OnSelect, SpecialLinkType } from './types';

export const useInitUpcomingAppointmentDropdown = ({
  customerId,
  allowAppt,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowAppt'>) => {
  const dispatch = useDispatch();

  useEffect(() => {
    if (allowAppt && isNormal(customerId)) {
      dispatch(getUpcomingAppointmentLink(customerId));
    }
  }, [allowAppt, customerId]);
};

export const useHandleClickUpcomingAppointmentItem = ({
  customerId,
  allowApptVariable,
  onSelect,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowApptVariable'> & {
  onSelect: OnSelect;
}) => {
  const [upcomingAppointmentLink] = useSelector(selectUpcomingAppointmentLink(customerId));

  return useCallback(async () => {
    if (allowApptVariable) {
      onSelect(`{${UpcomingApptLinkExp}}`);
    } else {
      onSelect(` ${upcomingAppointmentLink} `);
    }
  }, [allowApptVariable, onSelect, upcomingAppointmentLink]);
};

export const useRenderUpcomingAppointmentDropdown = ({
  customerId,
  allowAppt,
  allowApptVariable,
  onSelect,
  showCopy: _showCopy,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowAppt' | 'allowApptVariable'> & {
  onSelect: OnSelect;
  showCopy?: boolean;
}) => {
  const [upcomingAppointmentLink] = useSelector(selectUpcomingAppointmentLink(customerId));

  const handleClickUpcomingAppointmentItem = useHandleClickUpcomingAppointmentItem({
    customerId,
    allowApptVariable,
    onSelect,
  });

  const renderUpcomingAppointmentDropdown = () => {
    const isUnavailable = isNormal(customerId) && !upcomingAppointmentLink; // two way 且没有 upcoming link 时才显示不可用；two way 且有 upcoming link 或 mass text 场景下都显示可用
    const showCopy = isUnavailable ? false : _showCopy;
    const unavailableTip = 'No upcoming appointments';
    const menuText = SpecialLinkType.mapLabels[SpecialLinkType.UpcomingAppointment].label;

    if (!allowAppt) {
      return <Dropdown.MenuItem textValue={menuText}></Dropdown.MenuItem>;
    }

    return (
      <Dropdown.MenuItem
        key={`${SpecialLinkType.UpcomingAppointment}`}
        textValue={menuText}
        onAction={() => {
          handleClickUpcomingAppointmentItem();
        }}
        isDisabled={isUnavailable}
        icon={<MinorUpcomingApptOutlined />}
        classNames={
          showCopy
            ? {
                base: 'moe-py-[4px] moe-group',
              }
            : {}
        }
      >
        <Tooltip content={unavailableTip} side="top" isDisabled={!isUnavailable}>
          <Text variant="small" className="moe-flex moe-items-center moe-gap-[4px]">
            {menuText}
            <Condition if={showCopy}>
              <Copy className="moe-opacity-0 group-hover:moe-opacity-100" copyText={() => upcomingAppointmentLink} />
            </Condition>
          </Text>
        </Tooltip>
      </Dropdown.MenuItem>
    );
  };

  useInitUpcomingAppointmentDropdown({
    customerId,
    allowAppt,
  });

  return renderUpcomingAppointmentDropdown;
};
