import { MinorCAppOutlined } from '@moego/icons-react';
import { Dropdown, Text, Tooltip } from '@moego/ui';
import React, { useCallback } from 'react';
import { useBrandedAppConfig } from '../../../../store/branded/branded.hooks';
import { PetParentAppDownloadLinkExp } from '../../../../utils/messageVariable';
import { Condition } from '../../../Condition';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { type OnSelect, SpecialLinkType } from './types';

export const useInitPetParentPortalAppDropdown = () => {
  useBrandedAppConfig();
};

export const useHandleClickPetParentPortalAppItem = ({
  allowPetParentAppDownloadVariable,
  onSelect,
}: Pick<MessageSendBoxProps, 'allowPetParentAppDownloadVariable'> & {
  onSelect: OnSelect;
}) => {
  const brandedAppConfig = useBrandedAppConfig();

  return useCallback(async () => {
    if (allowPetParentAppDownloadVariable) {
      onSelect(`{${PetParentAppDownloadLinkExp}}`);
    } else {
      const downloadLink = brandedAppConfig.petParentDownloadLink;
      onSelect(` ${downloadLink} `);
    }
  }, [allowPetParentAppDownloadVariable, onSelect, brandedAppConfig]);
};

export const useRenderPetParentPortalAppDropdown = ({
  allowPetParentAppDownload,
  allowPetParentAppDownloadVariable,
  onSelect,
  showCopy: _showCopy,
}: Pick<MessageSendBoxProps, 'allowPetParentAppDownload' | 'allowPetParentAppDownloadVariable'> & {
  onSelect: OnSelect;
  showCopy?: boolean;
}) => {
  const brandedAppConfig = useBrandedAppConfig();

  const handleClickPetParentPortalAppItem = useHandleClickPetParentPortalAppItem({
    allowPetParentAppDownloadVariable,
    onSelect,
  });

  const renderPetParentPortalAppDropdown = () => {
    const isUnavailable = !brandedAppConfig.petParentDownloadLink;
    const showCopy = isUnavailable ? false : _showCopy;
    const unavailableTip = 'No Pet parent app download link';
    const menuText = SpecialLinkType.mapLabels[SpecialLinkType.PetParentPortalApp].label;

    if (!allowPetParentAppDownload) {
      return <Dropdown.MenuItem textValue={menuText}></Dropdown.MenuItem>;
    }

    return (
      <Dropdown.MenuItem
        key={`${SpecialLinkType.PetParentPortalApp}`}
        textValue={menuText}
        onAction={() => {
          handleClickPetParentPortalAppItem();
        }}
        isDisabled={isUnavailable}
        icon={<MinorCAppOutlined />}
        classNames={
          showCopy
            ? {
                base: 'moe-py-[4px] moe-group',
              }
            : {}
        }
      >
        <Tooltip
          className="moe-flex moe-items-center moe-gap-[4px]"
          content={unavailableTip}
          side="top"
          isDisabled={!isUnavailable}
        >
          <Text variant="small" className="moe-flex moe-items-center moe-gap-[4px]">
            {menuText}
            <Condition if={showCopy}>
              <Copy
                className="moe-opacity-0 group-hover:moe-opacity-100"
                copyText={() => brandedAppConfig.petParentDownloadLink}
              />
            </Condition>
          </Text>
        </Tooltip>
      </Dropdown.MenuItem>
    );
  };

  useInitPetParentPortalAppDropdown();

  return renderPetParentPortalAppDropdown;
};
