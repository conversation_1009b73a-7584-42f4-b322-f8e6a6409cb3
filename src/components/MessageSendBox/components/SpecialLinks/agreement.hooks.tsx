import { MinorContractOutlined } from '@moego/icons-react';
import { Button, Dropdown, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useCallback, useEffect } from 'react';
import { useHistory } from 'react-router';
import { URL_CLIENT } from '../../../../config/host/const';
import { PATH_AGREEMENT_SETTING, PATH_SIGN_AGREEMENT } from '../../../../router/paths';
import { getAgreementList, getAgreementTemplate } from '../../../../store/agreement/agreement.actions';
import { type AgreementRecord, agreementMapBox } from '../../../../store/agreement/agreement.boxes';
import { selectBusinessAgreements } from '../../../../store/agreement/agreement.selectors';
import { addSign } from '../../../../store/agreement/sign.actions';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import {
  AgreementLinkPrefix,
  getAgreementTemplateWithVariable,
  getAgreementVariable,
} from '../../../../utils/messageVariable';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { ChannelEnum } from '../ChannelSelect/ChannelSelect';
import { getAgreementSMSTemplate } from './agreement.action';
import { type OnSelect, SpecialLinkType } from './types';

export const useInitAgreementDropdown = ({ allowAgreement }: Pick<MessageSendBoxProps, 'allowAgreement'>) => {
  const [businessId] = useSelector(currentBusinessIdBox);
  const dispatch = useDispatch();

  useEffect(() => {
    if (allowAgreement && isNormal(businessId)) {
      dispatch(getAgreementList());
    }
  }, [allowAgreement, businessId]);
};

export const useHandleClickAgreementItem = ({
  customerId,
  allowAgreementVariable,
  onSelect,
  channel,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowAgreementVariable'> & {
  onSelect: OnSelect;
  channel?: ChannelEnum;
}) => {
  const dispatch = useDispatch();

  return useCallback(
    async (agreement: AgreementRecord) => {
      const params = {
        id: agreement.id,
        title: agreement.agreementHeader,
      };

      /**
       * - allowAgreementVariable 为 true 的情况下，都尽量用 variable
       *   - SMS channel 优先使用 customized template，其次用 variable
       *   - Pet parent app channel 无需考虑 customized template
       * - allowAgreementVariable 为 false 的情况下，优先用 customized template，其次用 URL
       */
      if (allowAgreementVariable) {
        if (channel === ChannelEnum.sms) {
          const variable = getAgreementVariable(params);
          const customizedTemplateRaw = (await dispatch(getAgreementTemplate(agreement.id)))?.smsBody ?? '';
          const customizedTemplate = customizedTemplateRaw.replace(
            new RegExp(`\\{${AgreementLinkPrefix}}`, 'g'),
            variable,
          );
          onSelect(customizedTemplate || variable);
        } else {
          const generalTemplate = getAgreementTemplateWithVariable(params);
          onSelect(generalTemplate);
        }
      } else {
        const record = await dispatch(addSign({ customerId, agreementId: agreement.id }));
        const customizedTemplate = await dispatch(getAgreementSMSTemplate(record.id));
        onSelect(customizedTemplate || ` ${URL_CLIENT}${PATH_SIGN_AGREEMENT.build({ encodeId: record.encodeId })} `);
      }
    },
    [allowAgreementVariable, onSelect, customerId, channel],
  );
};

export const useRenderAgreementDropdown = ({
  customerId,
  allowAgreement,
  allowAgreementVariable,
  onSelect,
  showCopy,
  channel,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowAgreement' | 'allowAgreementVariable'> & {
  onSelect: OnSelect;
  showCopy?: boolean;
  channel?: ChannelEnum;
}) => {
  const [agreementList, agreementMap] = useSelector(selectBusinessAgreements(), agreementMapBox);

  const history = useHistory();

  const handleClickAgreementItem = useHandleClickAgreementItem({
    allowAgreementVariable,
    onSelect,
    customerId,
    channel,
  });
  const dispatch = useDispatch();

  const renderAgreementDropdown = () => {
    const subMenuText = SpecialLinkType.mapLabels[SpecialLinkType.DigitalAgreement].label;
    const emptyText = 'No available agreement';

    if (!allowAgreement) {
      return <Dropdown.Item key={SpecialLinkType.DigitalAgreement} title={subMenuText}></Dropdown.Item>;
    }

    /**
     * 此处是 hack，因为 SubMenu 子组件不能是 <></>，用不了这种写法
     * <Dropdown.SubMenu>
     *    {
     *      agreementList.size > 0
     *        ? <> {agreementList.toArray().map(_ => <Dropdown.Item />)} </>
     *        : null
     *    }
     * </Dropdown.SubMenu>
     */
    const SPECIAL_ID = -1;
    const finalAgreementList = agreementList.size > 0 ? agreementList.toArray() : [SPECIAL_ID];

    return (
      <Dropdown.SubMenu
        key={SpecialLinkType.DigitalAgreement}
        textValue={subMenuText}
        title={subMenuText}
        icon={<MinorContractOutlined />}
      >
        {finalAgreementList.map((id) => {
          if (isNormal(id)) {
            const agreement = agreementMap.mustGetItem(id);
            const agreementText = agreement.agreementHeader;

            return (
              <Dropdown.Item
                key={`${SpecialLinkType.DigitalAgreement}-${agreement.id}`}
                textValue={agreementText}
                onAction={() => {
                  handleClickAgreementItem(agreement);
                }}
                classNames={{
                  base: showCopy ? 'moe-py-[4px] moe-group' : '',
                }}
              >
                <div className="moe-flex moe-items-center moe-font-manrope moe-text-s-20 moe-tracking-1 moe-font-regular moe-text-primary moe-w-full moe-gap-[4px]">
                  {agreementText}
                  {showCopy && (
                    <Copy
                      className="moe-opacity-0 group-hover:moe-opacity-100"
                      copyText={async () => {
                        const record = await dispatch(addSign({ customerId, agreementId: agreement.id }));
                        const smsTemplate = await dispatch(getAgreementSMSTemplate(record.id));
                        return (
                          smsTemplate || ` ${URL_CLIENT}${PATH_SIGN_AGREEMENT.build({ encodeId: record.encodeId })} `
                        );
                      }}
                    />
                  )}
                </div>
              </Dropdown.Item>
            );
          }
          return (
            <Dropdown.Item
              key={`${SpecialLinkType.DigitalAgreement}-${SPECIAL_ID}`}
              textValue={emptyText}
              onAction={() => {
                history.push(PATH_AGREEMENT_SETTING.build());
              }}
            >
              <div className="moe-py-[6px] moe-px-spacing-xs moe-flex moe-flex-col moe-items-center">
                <Text variant="small">{emptyText}</Text>
                <Button variant="tertiary" size="s" className="moe-mt-m">
                  Setup
                </Button>
              </div>
            </Dropdown.Item>
          );
        })}
      </Dropdown.SubMenu>
    );
  };

  useInitAgreementDropdown({ allowAgreement });

  return renderAgreementDropdown;
};
