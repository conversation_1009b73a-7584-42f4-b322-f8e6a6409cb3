import { MinorCAppOutlined } from '@moego/icons-react';
import { Dropdown, Text, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useCallback, useEffect } from 'react';
import { getPetParentPortalInviteLink } from '../../../../store/petParentPortal/petParentPortal.actions';
import { selectPetParentPortalInviteLink } from '../../../../store/petParentPortal/petParentPortal.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { PetParentPortalLinkExp } from '../../../../utils/messageVariable';
import { Condition } from '../../../Condition';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { type OnSelect, SpecialLinkType } from './types';

export const useInitPetParentPortalWebDropdown = ({
  customerId,
  allowPetParentPortal,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowPetParentPortal'>) => {
  const dispatch = useDispatch();

  useEffect(() => {
    if (allowPetParentPortal && isNormal(customerId)) {
      dispatch(getPetParentPortalInviteLink(customerId));
    }
  }, [allowPetParentPortal, customerId]);
};

export const useHandleClickPetParentPortalWebItem = ({
  customerId,
  allowPetParentPortalVariable,
  onSelect,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowPetParentPortalVariable'> & {
  onSelect: OnSelect;
}) => {
  const [inviteLink] = useSelector(selectPetParentPortalInviteLink(customerId));

  return useCallback(async () => {
    if (allowPetParentPortalVariable) {
      onSelect(`{${PetParentPortalLinkExp}}`);
    } else {
      onSelect(` ${inviteLink} `);
    }
  }, [allowPetParentPortalVariable, onSelect, inviteLink]);
};

export const useRenderPetParentPortalWebDropdown = ({
  customerId,
  allowPetParentPortal,
  allowPetParentPortalVariable,
  onSelect,
  showCopy: _showCopy,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowPetParentPortal' | 'allowPetParentPortalVariable'> & {
  onSelect: OnSelect;
  showCopy?: boolean;
}) => {
  const [inviteLink] = useSelector(selectPetParentPortalInviteLink(customerId));

  const handleClickPetParentPortalWebItem = useHandleClickPetParentPortalWebItem({
    customerId,
    allowPetParentPortalVariable,
    onSelect,
  });

  const renderPetParentPortalWebDropdown = () => {
    const isUnavailable = isNormal(customerId) && !inviteLink; // two way 且没有 invite link 时才显示不可用；two way 且有 invite link 或 mass text 场景下都显示可用
    const showCopy = isUnavailable ? false : _showCopy;
    const unavailableTip = 'No Pet parent portal invite link';
    const menuText = SpecialLinkType.mapLabels[SpecialLinkType.PetParentPortalWeb].label;

    if (!allowPetParentPortal) {
      return <Dropdown.MenuItem textValue={menuText}></Dropdown.MenuItem>;
    }

    return (
      <Dropdown.MenuItem
        key={`${SpecialLinkType.PetParentPortalWeb}`}
        textValue={menuText}
        onAction={() => {
          handleClickPetParentPortalWebItem();
        }}
        isDisabled={isUnavailable}
        icon={<MinorCAppOutlined />}
        classNames={
          showCopy
            ? {
                base: 'moe-py-[4px] moe-group',
              }
            : {}
        }
      >
        <Tooltip content={unavailableTip} side="top" isDisabled={!isUnavailable}>
          <Text variant="small" className="moe-flex moe-items-center moe-gap-[4px]">
            {menuText}
            <Condition if={showCopy}>
              <Copy className="moe-opacity-0 group-hover:moe-opacity-100" copyText={() => inviteLink} />
            </Condition>
          </Text>
        </Tooltip>
      </Dropdown.MenuItem>
    );
  };

  useInitPetParentPortalWebDropdown({ customerId, allowPetParentPortal });

  return renderPetParentPortalWebDropdown;
};
