import { MinorCofOutlined } from '@moego/icons-react';
import { Dropdown, Text, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useCallback } from 'react';
import { URL_CLIENT } from '../../../../config/host/const';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { generateCofRequestMessage } from '../../../../store/stripe/actions/public/stripe.actions';
import { CardOnFileLinkExp, getCardOnFileTemplateWithVariable } from '../../../../utils/messageVariable';
import { Condition } from '../../../Condition';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { ChannelEnum } from '../ChannelSelect/ChannelSelect';
import { getCardOnFilePath } from './cardOnFile.action';
import { type OnSelect, SpecialLinkType } from './types';

export const useHandleClickCardOnFileItem = ({
  customerId,
  allowRequestCofVariable,
  onSelect,
  channel,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowRequestCof' | 'allowRequestCofVariable'> & {
  onSelect: OnSelect;
  channel?: ChannelEnum;
}) => {
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness);

  return useCallback(async () => {
    if (allowRequestCofVariable) {
      if (channel === ChannelEnum.sms) {
        const template = `Hi {customerName}, please submit your card information through this secure link: {${CardOnFileLinkExp}}\n${business.businessName}`;
        onSelect(template);
      } else {
        const template = getCardOnFileTemplateWithVariable();
        onSelect(template);
      }
    } else {
      const msg = await dispatch(generateCofRequestMessage(customerId, allowRequestCofVariable));
      onSelect(msg);
    }
  }, [allowRequestCofVariable, customerId, onSelect, channel, business]);
};

export const useRenderCardOnFileDropdown = ({
  customerId,
  allowRequestCof,
  allowRequestCofVariable,
  onSelect,
  showCopy: _showCopy,
  channel,
}: Pick<MessageSendBoxProps, 'customerId' | 'allowRequestCof' | 'allowRequestCofVariable'> & {
  onSelect: OnSelect;
  showCopy?: boolean;
  channel?: ChannelEnum;
}) => {
  const [business] = useSelector(selectCurrentBusiness);

  const handleClickCardOnFileItem = useHandleClickCardOnFileItem({
    customerId,
    allowRequestCofVariable,
    onSelect,
    channel,
  });

  const renderCardOnFileDropdown = () => {
    const menuText = SpecialLinkType.mapLabels[SpecialLinkType.CardOnFile].label;
    const isUnavailable = !business.preferStripe();

    const showCopy = isUnavailable ? false : _showCopy;
    const dispatch = useDispatch();
    const unavailableTip =
      'This feature is MoeGo Pay exclusive. Please set MoeGo Pay as your payment processor to proceed.';

    if (!allowRequestCof) {
      return <Dropdown.MenuItem key={SpecialLinkType.CardOnFile} textValue={menuText}></Dropdown.MenuItem>;
    }

    return (
      <Dropdown.MenuItem
        key={`${SpecialLinkType.CardOnFile}`}
        textValue={menuText}
        onAction={() => {
          handleClickCardOnFileItem();
        }}
        isDisabled={isUnavailable}
        icon={<MinorCofOutlined />}
        classNames={
          showCopy
            ? {
                base: 'moe-py-[4px] moe-group',
              }
            : {}
        }
      >
        <Tooltip content={unavailableTip} side="top" isDisabled={!isUnavailable}>
          <Text variant="small" className="moe-flex moe-items-center moe-gap-[4px]">
            {menuText}
            <Condition if={showCopy}>
              <Copy
                className="moe-opacity-0 group-hover:moe-opacity-100"
                copyText={async () => {
                  const path = await dispatch(getCardOnFilePath(customerId));
                  return `${URL_CLIENT}${path}`;
                }}
              />
            </Condition>
          </Text>
        </Tooltip>
      </Dropdown.MenuItem>
    );
  };

  return renderCardOnFileDropdown;
};
