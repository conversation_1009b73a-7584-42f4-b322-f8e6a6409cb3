import { MinorOnlineBookingOutlined } from '@moego/icons-react';
import { Button, Dropdown, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useCallback } from 'react';
import { useHistory } from 'react-router';
import { PATH_ONLINE_BOOKING_SETTINGS } from '../../../../router/paths';
import { getAccountDetail } from '../../../../store/account/account.actions';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import {
  getOnlineBookingPreference,
  getOnlineBookingProfile,
} from '../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { selectOnlineBookingPreference } from '../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { getOnlineBookingTemplateWithVariable } from '../../../../utils/messageVariable';
import { Condition } from '../../../Condition';
import { Copy } from '../../../Copy/Copy';
import { type MessageSendBoxProps } from '../../type';
import { type OnSelect, SpecialLinkType } from './types';
import { getLocationDetail } from '../../../../store/business/location.actions';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { useBizIdReadyEffect } from '../../../../utils/hooks/useBizIdReadyEffect';

export const useInitOnlineBookingDropdown = ({
  allowOnlineBooking,
}: Pick<MessageSendBoxProps, 'allowOnlineBooking'>) => {
  const dispatch = useDispatch();
  const [businessId] = useSelector(currentBusinessIdBox);

  useBizIdReadyEffect(() => {
    if (allowOnlineBooking) {
      dispatch(getLocationDetail(businessId.toString()));
      dispatch(getAccountDetail());
      dispatch(getOnlineBookingPreference());
      dispatch(getOnlineBookingProfile());
    }
  }, [allowOnlineBooking]);
};

export const useHandleClickOnlineBookingItem = ({
  allowOnlineBookingVariable,
  onSelect,
}: Pick<MessageSendBoxProps, 'allowOnlineBookingVariable'> & {
  onSelect: OnSelect;
}) => {
  const [preference] = useSelector(selectOnlineBookingPreference);

  return useCallback(async () => {
    if (allowOnlineBookingVariable) {
      const template = getOnlineBookingTemplateWithVariable();
      onSelect(template);
    } else {
      const obUrl = preference.newOnlineBookingLink();
      onSelect(` ${obUrl} `);
    }
  }, [preference, allowOnlineBookingVariable, onSelect]);
};

export const useRenderOnlineBookingDropdown = ({
  allowOnlineBooking,
  allowOnlineBookingVariable,
  onSelect,
  showCopy,
}: Pick<MessageSendBoxProps, 'allowOnlineBooking' | 'allowOnlineBookingVariable' | 'showSpecialLinksCopyVariable'> & {
  onSelect: OnSelect;
  showCopy?: boolean;
}) => {
  const [preference, business] = useSelector(selectOnlineBookingPreference, selectCurrentBusiness);
  const history = useHistory();

  const handleClickOnlineBookingItem = useHandleClickOnlineBookingItem({
    allowOnlineBookingVariable,
    onSelect,
  });

  const renderOnlineBookingDropdown = () => {
    const subMenuText = SpecialLinkType.mapLabels[SpecialLinkType.OnlineBooking].label;
    const itemText = business.businessName;
    const emptyText = 'No available Online booking';
    const SPECIAL_ID = -1;

    if (!allowOnlineBooking) {
      return <Dropdown.MenuItem title={subMenuText}></Dropdown.MenuItem>;
    }

    return (
      <Dropdown.SubMenu
        key={SpecialLinkType.OnlineBooking}
        textValue={subMenuText}
        title={subMenuText}
        icon={<MinorOnlineBookingOutlined />}
      >
        {preference.isEnable > 0 ? (
          <Dropdown.MenuItem
            key={`${SpecialLinkType.OnlineBooking}-URL`}
            textValue={itemText}
            onAction={() => {
              handleClickOnlineBookingItem();
            }}
            classNames={
              showCopy
                ? {
                    base: 'moe-py-[4px] moe-group',
                  }
                : {}
            }
          >
            <div className="moe-flex moe-items-center moe-font-manrope moe-text-s-20 moe-tracking-1 moe-font-regular moe-text-primary moe-w-full moe-gap-[4px]">
              {itemText}
              <Condition if={showCopy}>
                <Copy
                  className="moe-opacity-0 group-hover:moe-opacity-100"
                  copyText={() => preference.newOnlineBookingLink()}
                />
              </Condition>
            </div>
          </Dropdown.MenuItem>
        ) : (
          <Dropdown.MenuItem
            key={`${SpecialLinkType.OnlineBooking}-${SPECIAL_ID}`}
            textValue={emptyText}
            onAction={() => {
              history.push(PATH_ONLINE_BOOKING_SETTINGS.build());
            }}
          >
            <div className="moe-py-[6px] moe-px-spacing-xs moe-flex moe-flex-col moe-items-center">
              <Text variant="small">{emptyText}</Text>
              <Button variant="tertiary" size="s" className="moe-mt-m">
                Setup
              </Button>
            </div>
          </Dropdown.MenuItem>
        )}
      </Dropdown.SubMenu>
    );
  };

  useInitOnlineBookingDropdown({ allowOnlineBooking });

  return renderOnlineBookingDropdown;
};
