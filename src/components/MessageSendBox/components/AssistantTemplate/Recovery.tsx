import { useDispatch, useSelector } from 'amos';
import React, { memo, useState } from 'react';
import { PATH_MASS_TEXT_CREATE, PATH_MESSAGE_CENTER } from '../../../../router/paths';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { type GetRecoverMassTemplateRes, getRecoverMassTemplate } from '../../../../store/message/message.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { useRouteQuery, useRouteState } from '../../../../utils/RoutePath';
import { useAsyncEffect } from '../../../../utils/hooks/useAsyncEffect';
import { Condition } from '../../../Condition';
import { StyledBorderGradientButton } from '../GradientButton.style';

interface RecoveryTemplateProps {
  onAdoptAnswer: (answer: string) => void;
}
export const RecoveryTemplate = memo((props: RecoveryTemplateProps) => {
  const { onAdoptAnswer } = props;
  const [businessId] = useSelector(currentBusinessIdBox);
  const { from } = useRouteState(PATH_MESSAGE_CENTER) || {};
  const { clientId: customerId } = useRouteQuery(PATH_MESSAGE_CENTER);
  const { needAbandoned } = useRouteQuery(PATH_MASS_TEXT_CREATE);
  const [state, setState] = useState<GetRecoverMassTemplateRes>();
  const dispatch = useDispatch();
  const visible = from === 'abandon_list' || Number(needAbandoned);

  const handleClick = () => {
    if (!state) {
      return;
    }
    const { length } = state;
    const randomIndex = Math.floor(Math.random() * length);
    onAdoptAnswer(state[randomIndex].content);
  };

  useAsyncEffect(async () => {
    if (!visible || !isNormal(businessId)) {
      return;
    }
    const customer = customerId ? { customerId: Number(customerId) } : {};
    const res = await dispatch(getRecoverMassTemplate({ type: 'recover_idea', ...customer }));
    setState(res);
  }, [visible, businessId]);

  return (
    <Condition if={visible && state?.length}>
      <StyledBorderGradientButton onClick={handleClick}>💡 Recovery template</StyledBorderGradientButton>
    </Condition>
  );
});
