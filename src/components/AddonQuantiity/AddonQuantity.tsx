import { Form, Input } from '@moego/ui';
import React from 'react';
import { DefaultQuantityPerDay } from '../ServiceApplicablePicker/utils/getDefaultService';

export interface AddonQuantityProps {
  isRequired?: boolean;
  isDisabled?: boolean;
  onChange: (v: number) => void;
  quantityPerDay?: number;
  inputNumberClassName?: string;
}

export const AddonQuantity = (props: AddonQuantityProps) => {
  const { isRequired, isDisabled, quantityPerDay, onChange, inputNumberClassName } = props;
  return (
    <div>
      <Form.Label isRequired={isRequired} className="moe-mb-8px-50">
        Quantity per day
      </Form.Label>
      <div className={inputNumberClassName}>
        <Input.Number
          value={quantityPerDay}
          maxValue={9}
          step={1}
          minValue={1}
          isDisabled={isDisabled}
          defaultValue={DefaultQuantityPerDay}
          onChange={(val) => {
            onChange(val || DefaultQuantityPerDay);
          }}
        />
      </div>
    </div>
  );
};
