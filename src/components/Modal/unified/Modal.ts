/**
 * 统一的新样式，后续的新页面和模块都尽量复用这里的样式。后续开发中遇到同样的组件的时候，建议和设计确认好和这里的样式是同样的。未来使用通用样式组件
 * 的时候可以直接基于这里的样式来封装。
 */

import styled from 'styled-components';
import { c_white } from '../../../style/_variables';
import { Button } from '../../Button/Button';
import { Modal as OriginalModal } from '../Modal';

export const Modal = styled(OriginalModal)`
  .modal-header {
    padding: 16px 32px;
  }
  .modal-header-close {
    right: 32px;
  }
  .modal-container {
    padding: 24px 32px;
  }
  .modal-footer {
    padding: 16px 32px;
  }
  .ant-form-item-label {
    min-width: 128px;
    font-weight: 500;
    color: #666;
    text-align: left;
  }
  .ant-input,
  &&& .ant-select-selector {
    font-weight: 500;
    font-size: 14px;
    color: #333;
    border: 1px solid #dee1e6;
    border-radius: 4px;
    overflow: hidden;
    &::placeholder,
    .ant-select-selection-placeholder {
      color: #ccc;
    }
  }
  .ant-select-selector {
    height: 32px !important;
  }
  .ant-select-selection-search-input {
    height: 30px !important;
  }
  .ant-select-selection-item {
    line-height: 30px !important;
  }
`;

export const FooterButtonsView = styled.div`
  display: flex;
  gap: 12px;
`;

export const FooterButton = styled(Button)<{ productButtonType?: 'cancel' | 'primary' | 'danger' }>`
  &&& {
    width: auto;
    padding: 7px 30px;
    border-radius: 56px;
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    border: ${({ productButtonType }) => (productButtonType === 'cancel' ? '1px solid #dee1e6' : 'none')};
    color: ${({ productButtonType }) => (productButtonType === 'cancel' ? '#333' : c_white)};
    background-color: ${({ productButtonType }) =>
      productButtonType === 'cancel'
        ? c_white
        : productButtonType === 'danger'
          ? '#d0021b'
          : 'var(--moe-color-bg-brand-bold)'};
  }
`;

export const FixedWidthFooterButton = styled(FooterButton)`
  &&& {
    width: 96px;
    padding: 7px 0;
  }
`;
