import React, { type FunctionComponent } from 'react';
import { type Optional } from 'utility-types';
import { useFloatableHost } from '../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';

export interface BaseModalProps {
  onClose?: (...args: any[]) => void;
}

/**
 * @example
 * const openModal = useModal(MyModal);
 *
 * openModal(myModalProps);
 */
export function useModal<T extends BaseModalProps>(Modal: FunctionComponent<T>) {
  const { mountModal } = useFloatableHost();

  const openModal = useLatestCallback((props?: Optional<T, 'onClose'>) => {
    const propsWithOnClose = {
      ...props,
      onClose: (v: unknown) => {
        props?.onClose?.(v);
        closeModal(v);
      },
    } as T;
    const { promise, closeFloatable: closeModal } = mountModal(<Modal {...propsWithOnClose} />);

    Object.assign(openModal, {
      close: closeModal,
    });
    return promise;
  });

  return openModal as typeof openModal & {
    close: (result?: unknown) => void;
  };
}
