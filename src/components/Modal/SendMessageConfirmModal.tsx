import { type Method } from '@moego/api-web/moego/models/message/v1/message_enums';
import { useDispatch, useSelector } from 'amos';
import { Form, Input, Radio, Select, Space } from 'antd';
import { type FormInstance } from 'antd/es/form';
import { useForm } from 'antd/es/form/Form';
import { type SelectProps, type SelectValue } from 'antd/es/select';
import classNames from 'classnames';
import copy from 'copy-to-clipboard';
import React, { memo, useEffect, useRef, useState } from 'react';
import { CSSTransition } from 'react-transition-group';
import { useAsync } from 'react-use';
import IconShape3xPng from '../../assets/icon/<EMAIL>';
import SvgIconCopyGreenSvg from '../../assets/svg/icon-copy-green.svg';
import { SignInput } from '../../container/Account/components/CoverLayout.style';
import { bodyCount } from '../../container/settings/Agreement/components/EditTemplate.options';
import { PATH_SETTING } from '../../router/paths';
import { AutoMessageType } from '../../store/autoMessage/autoMessage.boxes';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { getCustomerContactList } from '../../store/customer/customer.actions';
import { customerContactListBox, customerContactMapBox } from '../../store/customer/customerContact.boxes';
import { sendMessageToCustomer } from '../../store/message/message.actions';
import { useBool } from '../../utils/hooks/useBool';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { NewFeatureCustomScope } from '../ColClientSearch/WithNewFeature/NewFeatureCustomScope';
import { WithNewFeature } from '../ColClientSearch/WithNewFeature/WithNewFeature';
import { useDismissNewFeature } from '../ColClientSearch/WithNewFeature/WithNewFeature.hooks';
import { ImgIcon, SvgIcon } from '../Icon/Icon';
import { ALLOW_TEXT_LENGTH_TWOWAY } from '../MessageSendBox/components/MessageUtils';
import { toastApi } from '../Toast/Toast';
import { type ModalProps } from './Modal';
import {
  ModalView,
  SendMessageConfirmModalContentView,
  SendMessageTextTemplateInputCountView,
} from './SendMessageConfirmModal.styles';
import { FooterButton, FooterButtonsView } from './unified/Modal';

interface ContactsSelectProps extends Pick<SelectProps<SelectValue>, 'onChange' | 'value'> {
  customerId: number;
}

export const ContactsSelect = ({ customerId, value, onChange, ...rest }: ContactsSelectProps) => {
  const [business, contactList, contactMap] = useSelector(
    selectCurrentBusiness(),
    customerContactListBox.getList(customerId),
    customerContactMapBox,
  );
  const defaultValue = contactList.find((id) => !!contactMap.mustGetItem(id).isPrimary);
  const options = contactList
    .map((id, key, iter) => {
      const contact = contactMap.mustGetItem(id);
      return {
        value: id,
        label: `${contact.fullName()}, ${business.formatPhoneNumber(contact.phoneNumber)}${
          iter.size > 1 && contact.isPrimary ? ' (Primary contact)' : ''
        }`,
      };
    })
    .toArray();
  useEffect(() => {
    if (!value && defaultValue) {
      onChange?.(defaultValue, options);
    }
  }, [value, defaultValue]);
  return options.length > 1 ? (
    <Select className="rr-mask" options={options} value={value} onChange={onChange} {...rest} />
  ) : (
    <div className="!moe-text-[14px] font-medium !moe-leading-[18px] rr-mask">{options[0]?.label}</div>
  );
};

export type MessageSendByType = 'email' | 'message';
export type MessageSendByTypeForAgreement = Method.EMAIL | Method.SMS | Method.APP;

export interface SendMessageConfirmModalProps extends ModalProps {
  /**
   * Whether to use built in UI (which supports contact selection) to send message. False by default.
   * If true, {@link customerId} must be provided. {@link onConfirm} will still be called after the message is sent.
   */
  builtInMessageSender?: boolean;
  customerId?: number;
  title?: string;
  headline?: React.ReactNode;
  /**
   * If provided, {@link targetId} is also required, and the modal will send the message with the new API (which
   * supports target type).
   */
  targetType?: number;
  targetId?: number;
  message?: string | (() => string | Promise<string>);
  email?: string;
  visible: boolean;
  groomingId?: number;
  /**
   * Called before sending message. You could return false in this function to intercept the sending action (e.g. no
   * permission).
   */
  onBeforeSendMessage?: () => boolean;
  onConfirm: (type: MessageSendByType, email?: string) => void;
}

interface SendByInput {
  type: MessageSendByType;
  message: string;
  // Contact ID
  sendTo: number;
  email: string;
}

export const SendMessageConfirmModal = memo<SendMessageConfirmModalProps>(
  ({
    builtInMessageSender,
    customerId,
    title,
    headline,
    targetType,
    targetId,
    message = '',
    onBeforeSendMessage,
    onConfirm,
    onClose,
    email = '',
    visible,
    loading,
    ...props
  }) => {
    const dispatch = useDispatch();
    const [form] = useForm<SendByInput>();
    const formRef = useRef<FormInstance>(null);
    // TODO(Perqin, P2): Consider using Form.useWatch after upgrading to antd@4.20.0
    const emailVisible = useBool(false);
    const [messageForCount, setMessageForCount] = useState<string>();
    const dismissNewFeature = useDismissNewFeature('payOnlineMessageTemplate');

    const handleJumpToAutoMessage = () => {
      dismissNewFeature();
      // Open in new tab
      window.open(
        PATH_SETTING.queried({ anchorPoint: `message-${AutoMessageType.PayOnlineInvoice}` }, { panel: 'AutoMessage' }),
      );
    };

    const handleConfirm = useSerialCallback(async () => {
      const values = await form.validateFields();
      // TODO(Perqin, P2): Refactor to also send email within this component.
      if (builtInMessageSender && values.type === 'message') {
        const check = onBeforeSendMessage?.();

        if (check === false) {
          return;
        }
        await dispatch(
          sendMessageToCustomer(customerId!, values.message, values.sendTo, undefined, targetType, targetId),
        );

        toastApi.success('Message has been sent successfully!');
      }
      onConfirm(values.type, values.email);
    });

    const handleFormChange = (changeValues: Partial<SendByInput>) => {
      if ('type' in changeValues) {
        emailVisible.as(changeValues?.type === 'email');
      }
      if ('message' in changeValues) {
        setMessageForCount(changeValues.message);
      }
    };

    useEffect(() => {
      if (builtInMessageSender) {
        dispatch(getCustomerContactList(customerId!));
      }
    }, [builtInMessageSender]);

    useEffect(() => {
      if (!formRef.current) {
        return;
      }

      if (email && !form.getFieldsValue().email) {
        form.setFieldsValue({ email });
      }
    }, [email]);

    useAsync(async () => {
      if (!formRef.current) {
        return;
      }
      if (message && !form.getFieldsValue().message) {
        const nextMessage = typeof message === 'function' ? await message() : message;
        form.setFieldsValue({ message: nextMessage });
        setMessageForCount(nextMessage);
      }
    }, [message]);

    const modalContent = (
      <SendMessageConfirmModalContentView>
        {typeof headline === 'string' ? (
          <div className="moe-mb-[20px] moe-text-[14px] moe-leading-[18px] moe-text-[#666] moe-font-medium">
            {headline}
          </div>
        ) : (
          headline
        )}
        <Form
          form={form}
          ref={formRef}
          name={'email-confirm'}
          layout="vertical"
          initialValues={{
            type: 'message',
            message: '',
            sendTo: 0,
            email,
          }}
          onValuesChange={handleFormChange}
          onFinish={handleConfirm}
        >
          <Form.Item name={'type'}>
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={'message'}>Send by message</Radio>
                <Radio value={'email'}>Send by email</Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
          {/*
           * builtInMessageSender does not denote *Send by message*, don't be fooled 🤡
           * you should use emailVisible.value to determine whether to show the message form
           */}
          {builtInMessageSender && (
            <CSSTransition in={!emailVisible.value} timeout={200} classNames="fade">
              <div className="message-form-items">
                <Form.Item
                  label={
                    <>
                      <span>Message</span>
                      <span
                        className="!moe-cursor-pointer !moe-text-brand !moe-flex"
                        onClick={() => {
                          if (copy(form.getFieldsValue().message)) {
                            toastApi.success('Copied to clipboard successfully!');
                          }
                        }}
                      >
                        <SvgIcon src={SvgIconCopyGreenSvg} size={20} />
                        <span className="!moe-ml-[4px] !moe-leading-[18px] !moe-font-bold">Copy text</span>
                      </span>
                    </>
                  }
                  className={classNames('message-form-item', !emailVisible.value ? '' : 'hidden')}
                  extra={
                    <WithNewFeature feature="payOnlineMessageTemplate">
                      <div className="moe-mt-[8px] moe-text-[12px] moe-leading-[16px] moe-font-medium moe-text-[#999]">
                        Now you can set up the message template for the business at{' '}
                        <a onClick={handleJumpToAutoMessage}>Settings {'>'} Auto message</a>
                      </div>
                    </WithNewFeature>
                  }
                >
                  <Form.Item
                    name="message"
                    rules={[{ required: !emailVisible.value, message: 'Please input message!' }]}
                    noStyle
                  >
                    <Input.TextArea className="!moe-h-[130px] !moe-resize-none !moe-overflow-y-auto !moe-p-[12px]" />
                  </Form.Item>
                </Form.Item>
                <SendMessageTextTemplateInputCountView className={emailVisible.value ? 'hidden' : ''}>
                  {bodyCount(messageForCount || '', undefined, ALLOW_TEXT_LENGTH_TWOWAY)}
                </SendMessageTextTemplateInputCountView>
                <Form.Item
                  name="sendTo"
                  label="Send to"
                  className={!emailVisible.value ? '' : 'hidden'}
                  rules={[{ required: !emailVisible.value, message: 'Please select contact!' }]}
                >
                  <ContactsSelect customerId={customerId!} />
                </Form.Item>
              </div>
            </CSSTransition>
          )}
          <CSSTransition in={emailVisible.value} timeout={200} classNames="fade">
            <Form.Item
              name="email"
              className={emailVisible.value ? '' : 'hidden'}
              rules={[
                { required: emailVisible.value, message: 'Please input email!' },
                { type: 'email', message: 'Email address is invalid.' },
              ]}
            >
              <SignInput prefix={<ImgIcon src={IconShape3xPng} width={20} />} placeholder="Add customer email" />
            </Form.Item>
          </CSSTransition>
        </Form>
      </SendMessageConfirmModalContentView>
    );

    return (
      <ModalView
        visible={visible}
        title={title}
        onClose={onClose}
        loading={handleConfirm.isBusy() || loading}
        width={'480px'}
        footer={
          <FooterButtonsView>
            <FooterButton productButtonType="cancel" onClick={() => onClose()}>
              Cancel
            </FooterButton>
            <FooterButton onClick={handleConfirm}>Confirm</FooterButton>
          </FooterButtonsView>
        }
        {...props}
      >
        <NewFeatureCustomScope feature="payOnlineMessageTemplate">{modalContent}</NewFeatureCustomScope>
      </ModalView>
    );
  },
);
