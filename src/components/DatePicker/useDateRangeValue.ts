import dayjs from 'dayjs';
import { useMemo, useRef, useState } from 'react';
import { linear } from '../../store/utils/utils';
import {
  DateRangePreset,
  type TDateRangeCacheValue,
  type TDateRangeOption,
  type TDateRangeValue,
  dateRangePresets,
  getDateRangeConfig,
} from './DateRangePreset.utils';

export function useDateRangeValue(
  options: TDateRangeOption[] | TDateRangeOption[][],
  defaultValue: TDateRangeOption = dateRangePresets.customDate,
  cache?: TDateRangeCacheValue,
) {
  const ref = useRef<DateRangePreset | null>(null);
  const flatOptions: TDateRangeOption[] = useMemo(() => {
    return linear(options);
  }, [options]);

  if (!ref.current) {
    if (cache) {
      const { config, value } = cache;
      const matchOption = flatOptions.find((option) => {
        const optionDetail = getDateRangeConfig(option);
        return optionDetail.distance.join('_') === config.distance.join('_');
      });
      const _config = matchOption ? getDateRangeConfig(matchOption) : null;
      const valueArr: TDateRangeValue = [dayjs(value[0]), dayjs(value[1])];
      ref.current = new DateRangePreset(_config || config).mergeValue(valueArr);
    } else {
      ref.current = new DateRangePreset(defaultValue);
    }
  }

  const [dateRange, setDateRange] = useState<TDateRangeValue>(ref.current.value);
  return { dateRange, setDateRange, defaultRangeConfig: ref.current };
}
