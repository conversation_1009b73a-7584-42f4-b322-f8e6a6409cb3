import { MinorChevronDownOutlined, MinorChevronLeftOutlined, MinorChevronRightOutlined } from '@moego/icons-react';
import { Button, DatePicker, Heading, type HeadingProps, IconButton, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { FullWeekSimpleDayList } from '../../store/onlineBooking/onlineBooking.boxes';
import { isToday } from '../../utils/DateTimeUtil';
import { Condition } from '../Condition';

type DatePickerWithFastForwardProps = {
  value: Dayjs;
  onChange: (day: Dayjs) => void;
  className?: string;
  fontSize?: HeadingProps['size'];
};

export function DayDimensionDatePicker(props: DatePickerWithFastForwardProps) {
  const [business] = useSelector(selectCurrentBusiness);
  const { value, onChange, className, fontSize = '4' } = props;

  const isTodayFlag = useMemo(() => {
    return isToday(value);
  }, [value]);

  return (
    <div className={cn('moe-flex moe-items-center', className)}>
      <IconButton
        size="s"
        icon={<MinorChevronLeftOutlined></MinorChevronLeftOutlined>}
        onPress={() => {
          onChange(value.subtract(1, 'day'));
        }}
        aria-label="previous day"
      />
      <DatePicker value={value} onChange={(date) => (date ? onChange(date) : undefined)} format={business.dateFormat}>
        {() => (
          <div className="moe-flex moe-items-center moe-px-xs moe-cursor-pointer">
            <Heading size={fontSize} className="moe-text-primary moe-flex-shrink-0 moe-w-[164px] moe-whitespace-nowrap">
              {FullWeekSimpleDayList[value.day()]}, {business.formatDate(value)}
            </Heading>
            <MinorChevronDownOutlined className="moe-ml-xxs" />
          </div>
        )}
      </DatePicker>

      <IconButton
        size="s"
        icon={<MinorChevronRightOutlined></MinorChevronRightOutlined>}
        aria-label="next day"
        onPress={() => {
          onChange(value.add(1, 'day'));
        }}
      />
      <Condition if={!isTodayFlag}>
        <Button size="s" variant="secondary" className="moe-ml-s" onPress={() => onChange(dayjs())}>
          Today
        </Button>
      </Condition>
    </div>
  );
}
