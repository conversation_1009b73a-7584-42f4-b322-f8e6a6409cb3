import {
  But<PERSON>,
  DatePicker,
  Heading,
  IconButton,
  MinorChevronDownOutlined,
  MinorChevronLeftOutlined,
  MinorChevronRightOutlined,
} from '@moego/ui';
import { useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { Condition } from '../Condition';

export interface WeeklyDatePickerProps {
  onChange?: (date: Dayjs) => void;
  value: Dayjs;
}

export const WeeklyDatePicker = memo<WeeklyDatePickerProps>((props) => {
  const { onChange, value } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const startDateOfWeek = value.startOf('week');
  const endDateOfWeek = startDateOfWeek.endOf('week');
  const isCurrentDateInWeek = dayjs().isSame(startDateOfWeek, 'week');

  const handleChangeCalendarDate = (date: Dayjs) => {
    onChange?.(date);
  };

  const handlePrev = () => {
    handleChangeCalendarDate(value?.subtract(1, 'week') ?? null);
  };

  const handleNext = () => {
    handleChangeCalendarDate(value?.add(1, 'week') ?? null);
  };

  return (
    <div className="moe-flex moe-items-center">
      <div className="moe-flex moe-items-center">
        <IconButton size="s" icon={<MinorChevronLeftOutlined />} onPress={handlePrev} />
        <DatePicker mode={'week'} value={value} onChange={(startDate) => handleChangeCalendarDate(startDate!)}>
          {() => {
            const startDateFormat = startDateOfWeek?.format(business.dateFormatMD);
            const endDateFormat = endDateOfWeek?.format(business.dateFormat);

            return (
              <div className="moe-flex moe-items-center moe-px-xs moe-cursor-pointer">
                <Heading size="4" className="moe-text-primary">
                  {startDateFormat} - {endDateFormat}
                </Heading>
                <MinorChevronDownOutlined className="moe-ml-xxs" />
              </div>
            );
          }}
        </DatePicker>
        <IconButton size="s" icon={<MinorChevronRightOutlined />} onPress={handleNext} />
      </div>
      <Condition if={!isCurrentDateInWeek}>
        <Button variant="secondary" size="s" className="moe-ml-s" onPress={() => handleChangeCalendarDate(dayjs())}>
          Today
        </Button>
      </Condition>
    </div>
  );
});
