import dayjs, { type Dayjs, type OpUnitType, type QUnitType } from 'dayjs';
import { type EnumValues, createEnum } from '../../store/utils/createEnum';

const CUSTOM_DATE_VALUE = 999;
export type TDateRangeDetail = {
  distance: [number, 'day' | 'week' | 'month' | 'quarter' | 'year'];
  label?: string | ((date: dayjs.Dayjs, config: TDateRangeDetail) => string);
  toDate?: boolean; // end with today
};

type TDateRangeName =
  | 'yesterday'
  | 'today'
  | 'tomorrow'
  | 'lastWeek'
  | 'thisWeek'
  | 'nextWeek'
  | 'lastMonth'
  | 'thisMonth'
  | 'nextMonth'
  | 'lastQuarter'
  | 'thisQuarter'
  | 'nextQuarter'
  | 'lastYear'
  | 'thisYear'
  | 'nextYear'
  | 'weekToDate'
  | 'monthToDate'
  | 'quarterToDate'
  | 'yearToDate'
  | 'relativeDay'
  | 'relativeWeek'
  | 'relativeMonth'
  | 'relativeQuarter'
  | 'relativeYear'
  | 'customDate'
  | 'divider';

export const dateRangePresets = createEnum<TDateRangeName, number, Omit<TDateRangeDetail, 'type'>>({
  // day
  yesterday: [
    1,
    {
      label: 'Yesterday',
      distance: [-1, 'day'],
    },
  ],
  today: [
    2,
    {
      label: 'Today',
      distance: [0, 'day'],
    },
  ],
  tomorrow: [
    3,
    {
      label: 'Tomorrow',
      distance: [1, 'day'],
    },
  ],

  // week
  lastWeek: [
    4,
    {
      label: 'Last week',
      distance: [-1, 'week'],
    },
  ],
  thisWeek: [
    5,
    {
      label: 'This week',
      distance: [0, 'week'],
    },
  ],
  nextWeek: [
    6,
    {
      label: 'Next week',
      distance: [1, 'week'],
    },
  ],

  // month
  lastMonth: [
    7,
    {
      label: 'Last month',
      distance: [-1, 'month'],
    },
  ],
  thisMonth: [
    8,
    {
      label: 'This month',
      distance: [0, 'month'],
    },
  ],
  nextMonth: [
    9,
    {
      label: 'Next month',
      distance: [1, 'month'],
    },
  ],

  // quarter
  lastQuarter: [
    10,
    {
      label: 'Last quarter',
      distance: [-1, 'quarter'],
    },
  ],
  thisQuarter: [
    11,
    {
      label: 'This quarter',
      distance: [0, 'quarter'],
    },
  ],
  nextQuarter: [
    12,
    {
      label: 'Next quarter',
      distance: [1, 'quarter'],
    },
  ],

  // year
  lastYear: [
    13,
    {
      label: 'Last year',
      distance: [-1, 'year'],
    },
  ],
  thisYear: [
    14,
    {
      label: 'This year',
      distance: [0, 'year'],
    },
  ],
  nextYear: [
    15,
    {
      label: 'Next year',
      distance: [1, 'year'],
    },
  ],

  // 截止到当前时间，适合用在一些日志查询，有时间跨度限制考量的场景
  weekToDate: [
    16,
    {
      label: 'Week-to-date',
      distance: [0, 'week'],
      toDate: true,
    },
  ],
  monthToDate: [
    17,
    {
      label: 'Month-to-date',
      distance: [0, 'month'],
      toDate: true,
    },
  ],
  quarterToDate: [
    18,
    {
      label: 'Quarter-to-date',
      distance: [0, 'quarter'],
      toDate: true,
    },
  ],
  yearToDate: [
    19,
    {
      label: 'Year-to-date',
      distance: [0, 'year'],
      toDate: true,
    },
  ],

  // relative date/week/month/quarter/year
  // 上面的 last/this/next 都只是 relative 的特殊情况
  relativeDay: [
    20,
    {
      label: '',
      distance: [0, 'day'],
    },
  ],
  relativeWeek: [
    21,
    {
      label: '',
      distance: [0, 'week'],
    },
  ],
  relativeMonth: [
    22,
    {
      label: '',
      distance: [0, 'month'],
    },
  ],
  relativeQuarter: [
    23,
    {
      label: '',
      distance: [0, 'quarter'],
    },
  ],
  relativeYear: [
    24,
    {
      label: '',
      distance: [0, 'year'],
    },
  ],

  // 手动输入日期
  customDate: [
    CUSTOM_DATE_VALUE,
    {
      label: 'Custom',
      distance: [0, 'day'],
    },
  ],

  // 分隔线
  divider: [
    CUSTOM_DATE_VALUE + 1,
    {
      label: 'Divider',
      distance: [0, 'day'],
    },
  ],
});

export type TDayJsUnitType = OpUnitType & QUnitType;
export type TDateRangeOption = EnumValues<typeof dateRangePresets> | TDateRangeDetail; // 一个日期范围的类型，可以是预设的，也可以是自定义的
export type TDateRangeValue = [Dayjs, Dayjs];
export type TDateRangeCacheValue = {
  config: Omit<TDateRangeDetail, 'label'>; // 函数不能被缓存到路由里，这里要去掉label
  value: [number, number];
};

/**
 * get preset range config
 * @param config
 * @returns
 */
export function getDateRangeConfig(config: TDateRangeOption) {
  if (typeof config === 'number') {
    return {
      ...dateRangePresets.mapLabels[config],
    };
  }

  return config;
}

/**
 * 根据preset的配置，获取日期范围的值
 * 保持单一导出，不要导出这个函数
 * @param config
 * @returns
 */
function getRangeValues(option: TDateRangeOption): [Dayjs, Dayjs] {
  const config = getDateRangeConfig(option);
  const { distance, toDate } = config;
  const [distanceValue, unit] = distance as [number, TDayJsUnitType];
  let temp = dayjs();
  if (distanceValue) {
    temp = temp.add(distanceValue, unit);
  }

  const start = temp.startOf(unit);
  const end = toDate ? dayjs().endOf('day') : start.endOf(unit);
  return [start, end];
}

/**
 * 单个日期范围预设管理
 * label: 展示内容的生成
 * value: 范围值
 */
export class DateRangePreset {
  value: TDateRangeValue;
  config: TDateRangeDetail;
  label: string;
  key: string;
  isCustomDate: boolean;
  isDivider: boolean;

  static generateDefaultRangeValue(option: TDateRangeOption): TDateRangeValue {
    const config = getDateRangeConfig(option);
    return getRangeValues(config);
  }

  /**
   * 生成相对时间范围的选项
   * 比如：生成2个月前，3个月前....10个月前的选项
   * @param options
   */
  static generateRelativeOptions(options: {
    start: TDateRangeDetail['distance']['0'];
    step: number;
    total: number;
    unit: TDateRangeDetail['distance']['1'];
    label?: TDateRangeDetail['label'];
  }): TDateRangeDetail[] {
    const { start, step, total, unit, label } = options;
    return Array(total)
      .fill(start)
      .map((value, index) => value + index * step)
      .map((value) => {
        return {
          distance: [value, unit],
          label,
        };
      });
  }

  constructor(option: TDateRangeOption) {
    const config = getDateRangeConfig(option);
    this.config = config;
    this.value = getRangeValues(config);
    // distance 和 toDate，组合应当是唯一的，可为 key
    this.isCustomDate = config.label === dateRangePresets.mapLabels[dateRangePresets.customDate].label;
    this.isDivider = config.label === dateRangePresets.mapLabels[dateRangePresets.divider].label;
    this.key = this.genKey();
    this.label = this.genLabel();
  }

  mergeValue(value: TDateRangeValue) {
    this.value = value;
    return this;
  }

  private genKey() {
    if (this.isDivider) {
      return 'divider';
    }

    return this.config.distance.join('-') + (this.config.toDate ? '-end-today' : '-end-unit');
  }

  private genLabel(): string {
    let result = '';
    const config = this.config;
    const start = this.value[0];
    if (typeof config.label === 'function') {
      result = config.label(start, config);
    } else {
      result = config.label || '';
    }

    return result;
  }
}
