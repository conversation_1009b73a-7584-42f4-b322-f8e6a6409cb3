import { DownOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Divider, Dropdown, <PERSON>u, Space } from 'antd';
import classNames from 'classnames';
import React, { memo, useMemo } from 'react';
import { type Nullable } from '../../container/Payment/components/MoePay';
import { useBool } from '../../utils/hooks/useBool';
import { DateRangePreset, type TDateRangeOption, dateRangePresets } from './DateRangePreset.utils';

export interface DateRangePresetsProps {
  options: TDateRangeOption[] | TDateRangeOption[][];
  rangeConfig?: Nullable<DateRangePreset>;
  onChange: (selected: DateRangePreset) => void;
}

export const DateRangePresetsPicker = memo<DateRangePresetsProps>(({ rangeConfig, options, onChange }) => {
  const visible = useBool();
  const normalizedOptions: DateRangePreset[] = useMemo(() => {
    const result: DateRangePreset[] = [];
    const Ctor = DateRangePreset;
    options.forEach((op, index, array) => {
      if (Array.isArray(op)) {
        const subList = op.map((subOp) => new Ctor(subOp));
        result.push(...subList);
        if (index !== array.length - 1) {
          result.push(new Ctor(dateRangePresets.divider));
        }
      } else {
        result.push(new Ctor(op));
      }
    });

    return result;
  }, [options]);

  const handleSelectPreset = (item: DateRangePreset) => {
    visible.close();
    onChange(item);
  };

  const renderMenuItem = (item: DateRangePreset, index: number) => {
    if (item.isDivider) {
      return (
        <Menu.Item key={`divider-${index}`} className="!moe-m-0 !moe-p-0">
          <Divider style={{ margin: 0, position: 'absolute', left: 0, right: 0 }} />
        </Menu.Item>
      );
    }

    return (
      <Menu.Item
        className={item.key === rangeConfig?.key ? '!moe-bg-[#FFF7F0] !moe-font-bold' : ''}
        key={item.key}
        onClick={() => handleSelectPreset(item)}
      >
        {item.label}
      </Menu.Item>
    );
  };

  return (
    <Dropdown
      overlay={
        <Menu className="!moe-max-h-[328px] !moe-overflow-y-scroll">{normalizedOptions.map(renderMenuItem)}</Menu>
      }
      trigger={['click']}
      onVisibleChange={visible.as}
    >
      <Space>
        <Button
          className={classNames([
            '!moe-h-[40px] !moe-font-medium !moe-min-w-[134px] !moe-rounded-[4px] !moe-flex !moe-justify-between !moe-items-center',
            visible.value ? '!moe-text-[#999]' : '!moe-text-[#333]',
          ])}
        >
          <span>{rangeConfig?.label}</span>
          <DownOutlined />
        </Button>
      </Space>
    </Dropdown>
  );
});
