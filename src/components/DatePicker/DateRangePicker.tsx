import { useSelector } from 'amos';
import { Col, Row } from 'antd';
import { type Dayjs } from 'dayjs';
import { type RangeValue } from 'rc-picker/lib/interface';
import React, { forwardRef, useCallback, useImperativeHandle, useState } from 'react';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { DatePicker, type RangePickerProps } from './DatePicker';
import {
  DateRangePreset,
  type TDateRangeCacheValue,
  type TDateRangeOption,
  type TDateRangeValue,
  dateRangePresets,
} from './DateRangePreset.utils';
import { DateRangePresetsPicker } from './DateRangePresetPicker';

export interface DateRangePickerRef {
  getCacheValue: () => TDateRangeCacheValue;
}

export interface DateRangePickerProps {
  options: TDateRangeOption[] | TDateRangeOption[][];
  onChange?: (rangeValues: TDateRangeValue) => void;
  value: TDateRangeValue;
  defaultConfig: DateRangePreset;
  rangePickerProps?: RangePickerProps;
}

export const DateRangePicker = forwardRef<DateRangePickerRef, DateRangePickerProps>(
  ({ options, value, defaultConfig, onChange, rangePickerProps }, ref) => {
    const [business] = useSelector(selectCurrentBusiness());
    const [rangeConfig, setRangeConfig] = useState<DateRangePreset>(defaultConfig || null);
    const getRangeValues = (date: RangeValue<Dayjs> | DateRangePreset['value']) => {
      const start = date?.[0] ?? value[0];
      const end = date?.[1] ?? value[1];
      const range: [Dayjs, Dayjs] = [start, end];
      return range;
    };

    const handlePresetChange = (preset: DateRangePreset) => {
      const range = getRangeValues(preset.value);
      if (preset.isCustomDate) {
        setRangeConfig(preset.mergeValue(value));
      } else {
        setRangeConfig(preset);
        onChange?.(range);
      }
    };

    const handleCustomChange = (date: RangeValue<Dayjs>) => {
      const range = getRangeValues(date);

      if (!rangeConfig?.isCustomDate) {
        setRangeConfig(new DateRangePreset(dateRangePresets.customDate));
      }
      onChange?.(range);
    };

    const getCacheValue = useCallback(
      () => ({
        value: [value[0].valueOf(), value[1].valueOf()] as [number, number],
        config: {
          ...rangeConfig.config,
          label: void 0,
        },
      }),
      [rangeConfig, value],
    );

    useImperativeHandle(ref, () => ({
      getCacheValue,
    }));

    return (
      <Row align="middle">
        <Col style={{ marginRight: 16 }}>
          <DateRangePresetsPicker options={options} rangeConfig={rangeConfig} onChange={handlePresetChange} />
        </Col>

        <Col>
          <DatePicker.RangePicker
            style={{ height: 40 }}
            allowClear={false}
            format={business.dateFormat}
            size="large"
            value={value}
            onChange={handleCustomChange}
            {...rangePickerProps}
          />
        </Col>
      </Row>
    );
  },
);
