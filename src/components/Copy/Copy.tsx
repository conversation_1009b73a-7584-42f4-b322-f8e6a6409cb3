import { MinorCopyOutlined } from '@moego/icons-react';
import { useSerialCallback } from '@moego/tools';
import { Tooltip, type TooltipProps, cn } from '@moego/ui';
import copy from 'copy-to-clipboard';
import React from 'react';
import { Loading } from '../Loading/Loading';
import { toastApi } from '../Toast/Toast';

export interface CopyProps extends Omit<TooltipProps, 'children'> {
  copyText: string | (() => string) | (() => Promise<string>);
  children?: React.ReactNode;
  className?: string;
}

export const Copy: React.FC<CopyProps> = ({ content, className, copyText, children, ...rest }) => {
  const handlePointerUp = useSerialCallback(async (e) => {
    e.stopPropagation();

    const text = typeof copyText === 'function' ? await copyText() : copyText;
    setTimeout(() => {
      if (copy(text)) {
        toastApi.success('Copied successfully');
      }
    });
  });

  return (
    <Tooltip side="top" content={'Copy link'} {...rest}>
      <button
        className={cn(
          'moe-flex moe-items-center moe-justify-center moe-rounded-full moe-outline-none moe-w-[32px] moe-h-[32px] moe-text-primary moe-bg-[#00000000] enabled:hover:moe-bg-[#0000000D] enabled:active:moe-bg-[#0000001A] data-[open=true]:moe-bg-[#0000001A] data-[open=true]:enabled:hover:moe-bg-[#0000001A] disabled:moe-bg-[#00000000]',
          handlePointerUp.isBusy() && '!moe-opacity-100',
          className,
        )}
        onPointerUp={handlePointerUp}
      >
        {handlePointerUp.isBusy() ? <Loading loading={true} /> : children || <MinorCopyOutlined />}
      </button>
    </Tooltip>
  );
};
