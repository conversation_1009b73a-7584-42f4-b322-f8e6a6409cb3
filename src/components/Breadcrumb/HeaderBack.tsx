import { useSelector } from 'amos';
import React, { memo, type ReactNode } from 'react';
import { useHistory } from 'react-router';
import SvgIconArrowLeftSvg from '../../assets/svg/icon-arrow-left.svg';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { SvgIcon } from '../Icon/Icon';

export interface HeaderBackProps {
  title: ReactNode;
  path?: string;
  className?: string;
  showBizName?: boolean;
}

export const HeaderBack = memo<HeaderBackProps>(({ title, path, className, showBizName }) => {
  const history = useHistory();
  const [business] = useSelector(selectCurrentBusiness());
  const businessName = business?.businessName;

  const handleClick = () => {
    if (path) {
      history.push(path);
    } else {
      history.goBack();
    }
  };

  return (
    <div className={`moe-flex moe-items-center moe-cursor-pointer ${className}`} onClick={handleClick}>
      <SvgIcon src={SvgIconArrowLeftSvg} size={24} color="#333" className="moe-mr-[8px]" />
      <span className="moe-text-xl moe-font-bold moe-text-[#333]">
        {title}
        {showBizName && businessName ? ` for ${businessName}` : ''}
      </span>
    </div>
  );
});
