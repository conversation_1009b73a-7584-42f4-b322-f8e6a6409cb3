/*
 * @since 2020-09-30 16:22:59
 * <AUTHOR> <<EMAIL>>
 */

import React, { Fragment, memo, type ReactNode } from 'react';
import { BreadcrumbView } from './Breadcrumb.style';

export interface BreadcrumbProps {
  className?: string;
  children?: ReactNode;
  conj?: ReactNode;
}

export const Breadcrumb = memo<BreadcrumbProps>(({ className, children, conj = '-' }) => {
  return (
    <BreadcrumbView className={className}>
      {Array.isArray(children)
        ? children.map((c, i) =>
            c ? (
              <Fragment key={i}>
                {c}
                <span className="sep">{conj}</span>
              </Fragment>
            ) : null,
          )
        : children}
    </BreadcrumbView>
  );
});
