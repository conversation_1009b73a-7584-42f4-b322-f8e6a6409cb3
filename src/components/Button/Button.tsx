import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import classNames from 'classnames';
import React, { type AnchorHTMLAttributes, type ButtonHTMLAttributes, type CSSProperties, forwardRef } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { ButtonView } from './Button.style';

export type ButtonSize = 'lg' | 'sm' | 'normal' | 'h35';

export type ButtonRadius = 'normal' | 'circle' | 'shadow';

export type ButtonType =
  | 'primary'
  | 'default'
  | 'secondary'
  | 'danger'
  | 'link'
  | 'purple'
  | 'black'
  | 'white'
  | 'white-border';

export type ButtonHTMLType = 'submit' | 'reset' | 'button';

export interface BaseButtonProps {
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  size?: ButtonSize;
  btnType?: ButtonType;
  children: React.ReactNode;
  /**
   * 如果设置, 则会渲染为 Link 标签
   */
  link?: string;
  /**
   * 如果设置, 则会渲染为 a 标签
   */
  href?: string;
  fill?: boolean;
  icon?: React.ReactNode;
  style?: CSSProperties;
  buttonRadius?: ButtonRadius;
  htmlType?: ButtonHTMLType;
  hoverAnimation?: boolean;
  /**
   * 展示为 block
   */
  block?: boolean;
  /**
   * 如果为 true, 即使 disabled, 也会触发 click 事件
   */
  passiveDisabled?: boolean;
}

export type NativeProps = BaseButtonProps & ButtonHTMLAttributes<HTMLElement>;
export type AnchorButtonProps = BaseButtonProps & AnchorHTMLAttributes<HTMLElement>;
export type ButtonProps = Partial<NativeProps & AnchorButtonProps>;

export const Button = forwardRef<HTMLButtonElement & HTMLAnchorElement & Link, ButtonProps>((props, ref) => {
  const {
    btnType = 'default',
    disabled,
    size = 'normal',
    children,
    href,
    className,
    icon,
    fill = true,
    buttonRadius = 'normal',
    style,
    htmlType = 'button',
    hoverAnimation = true,
    link,
    loading = false,
    block,
    passiveDisabled,
    ...restProps
  } = props;
  // btn, btn-lg, btn-primary
  const btnRadiusClass: string =
    buttonRadius === 'normal'
      ? 'btn-radius-normal'
      : size !== 'normal'
        ? `btn-radius-${buttonRadius}-${size}`
        : `btn-radius-${buttonRadius}`;
  const btnColorType: string = fill
    ? `btn-${btnType}`
    : btnType === 'link'
      ? `btn-${btnType}`
      : `btn-${btnType}-unfilled`;
  const classes = classNames(
    'btn',
    btnRadiusClass,
    {
      [`btn-${size}`]: size,
      'button-hover-animation': hoverAnimation,
      disabled: disabled,
      'btn-link-underline': btnType === 'link' && !icon,
      loading,
      block,
    },
    btnColorType,
    className,
  );

  if (link) {
    return (
      <ButtonView as={Link} className={classes} ref={ref} to={link} style={style} {...restProps}>
        {icon}
        {children}
        {spin}
      </ButtonView>
    );
  }
  if (href) {
    return (
      <ButtonView as="a" className={classes} ref={ref} href={href} style={style} {...restProps}>
        {icon}
        {children}
        {spin}
      </ButtonView>
    );
  }
  return (
    <ButtonView
      className={classes}
      ref={ref}
      disabled={!passiveDisabled && (disabled || loading)}
      {...restProps}
      style={style}
      type={htmlType}
    >
      {icon}
      {children}
      {spin}
    </ButtonView>
  );
});

const spin = <Spin indicator={<LoadingOutlined />} spinning={true} />;

// FIX problem: tooltip does not show/disappear over disabled button
(Button as any).__ANT_BUTTON = true;

export const ButtonList = styled.div`
  margin: -4px -8px;
  > ${ButtonView} {
    margin: 4px 8px;
  }
`;
