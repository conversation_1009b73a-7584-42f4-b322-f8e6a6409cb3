/**
 * Antd Button with some MoeGo Custom style
 * Copy from src/container/Client/ClientList/components/ClientListHeader.style.ts
 * Looking forward to the new Component Library 🥺
 */
import { Button } from 'antd';
import { type ButtonProps } from 'antd/lib/button';
import { type FC } from 'react';
import styled from 'styled-components';
import { c_white } from '../../style/_variables';

const GeneralButton: FC<ButtonProps> = styled(Button)`
  &&& {
    border-radius: 56px;
    display: flex;
    align-items: center;
    cursor: pointer;
    white-space: nowrap;
    box-shadow: none;

    padding: 7px 12px;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;

    &:disabled {
      cursor: not-allowed;
    }

    &.large {
      padding: 10px 16px;
      font-size: 16px;
      line-height: 20px;
      height: 42px;
    }

    &.mini {
      padding: 6px 10px;
      font-size: 12px;
      line-height: 16px;
    }
  }
`;

export const PrimaryAntButton = styled(GeneralButton)`
  &&& {
    color: #fff !important;
    background-color: var(--moe-color-bg-brand-bold);
    border: 1px solid var(--moe-color-bg-brand-bold);

    &:hover {
      color: #fff !important;
      border-color: var(--moe-color-bg-brand-bold-hover);
      background-color: var(--moe-color-bg-brand-bold-hover);
    }
    &:disabled {
      color: #fff !important;
      border-color: var(--moe-color-bg-brand-bold-hover);
      background-color: var(--moe-color-bg-brand-bold-hover);
    }
  }
`;

export const DefaultAntButton = styled(GeneralButton)`
  &&& {
    color: #333;
    background-color: #fff;
    border: 1px solid #cdcdcd;

    &:hover {
      background-color: #f2f3f6;
    }
    &:disabled {
      background-color: #fff;
      color: #cccccc;
    }
  }
`;

export const DangerAntButton = styled(GeneralButton)`
  &&& {
    color: #fff !important;
    background-color: #d0021b;
    border: 1px solid #d0021b;

    &:hover {
      color: #fff !important;
      border-color: #9e0215;
      background-color: #9e0215;
    }
    &:disabled {
      color: #fff !important;
      border-color: #ec9aa4;
      background-color: #ec9aa4;
    }
  }
`;

export const OutlineAntButton = styled(GeneralButton)`
  &&& {
    min-width: 96px;
    height: 32px;
    color: #333;
    border: 1px solid #dee1e5;
    border-radius: 32px;
    padding: 7px 24px;
    font-weight: 700;
    box-shadow: none;
    background-color: ${c_white};
  }

  &&&.lg {
    height: 40px;
    font-size: 16px;
    line-height: 20px;
  }
`;
