/*
 * @since 2020-09-23 09:43:07
 * <AUTHOR> <<EMAIL>>
 */

import styled, { css } from 'styled-components';

import {
  mixin_button_hover_animation,
  mixin_button_size,
  mixin_button_style,
  mixin_button_style_unfilled,
} from '../../style/_mixin.style';
import {
  c_btn_border_radius,
  c_btn_border_radius_circle,
  c_btn_border_radius_circle_lg,
  c_btn_border_radius_circle_sm,
  c_btn_border_width,
  c_btn_color,
  c_btn_padding_x,
  c_btn_padding_x_lg,
  c_btn_padding_x_sm,
  c_btn_padding_y,
  c_btn_padding_y_lg,
  c_btn_padding_y_sm,
  c_danger,
  c_font_size_regular,
  c_font_size_regular_mini,
  c_font_weight_base,
  c_gray,
  c_normal,
  c_purple,
  c_secondary,
} from '../../style/_variables';

export const ButtonView = styled.button`
  position: relative;
  display: inline-block;
  font-weight: ${c_font_weight_base};
  color: ${c_btn_color};
  font-size: ${c_font_size_regular};
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  background-image: none;
  border: ${c_btn_border_width} solid transparent;
  ${mixin_button_size(c_btn_padding_y, c_btn_padding_x, c_font_size_regular)};
  cursor: pointer;
  outline: none;
  line-height: 1.2em;

  &.btn.block {
    display: block;
    width: 100%;
  }

  &.disabled,
  &[disabled] {
    cursor: not-allowed;
    opacity: 0.7;
    box-shadow: none;

    > * {
      pointer-events: none;
    }
  }

  > :nth-last-child(2):not(.ant-spin) {
    margin-right: 0;
  }

  > .ant-spin {
    display: none;
  }

  &.loading > .ant-spin {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.btn-radius-normal {
    border-radius: ${c_btn_border_radius};
  }

  &.btn-radius-circle {
    border-radius: ${c_btn_border_radius_circle};
  }

  &.btn-radius-circle-lg {
    border-radius: ${c_btn_border_radius_circle_lg};
  }

  &.btn-radius-circle-sm {
    border-radius: ${c_btn_border_radius_circle_sm};
  }

  &.btn-radius-circle-h35 {
    border-radius: 17.5px;
  }

  &.btn-lg {
    ${mixin_button_size(c_btn_padding_y_lg, c_btn_padding_x_lg, c_font_size_regular)};
  }

  &.btn-h35 {
    ${mixin_button_size('8px', '16px', '14px')};
    line-height: 17px;
  }

  &.btn-sm {
    ${mixin_button_size(c_btn_padding_y_sm, c_btn_padding_x_sm, c_font_size_regular_mini)};
  }

  &.btn-primary {
    color: var(--moe-color-bg-white);
    background-color: var(--moe-color-bg-brand-bold) !important;
    border-color: var(--moe-color-bg-brand-bold) !important;
    &:hover {
      color: var(--moe-color-bg-white);
      background-color: var(--moe-color-bg-brand-bold-hover) !important;
      border-color: var(--moe-color-bg-brand-bold-hover) !important;
    }
    &:focus,
    &.focus {
      color: var(--moe-color-bg-white);
      background-color: var(--moe-color-bg-brand-bold) !important;
      border-color: var(--moe-color-bg-brand-bold) !important;
    }
    &:active,
    &.active {
      color: var(--moe-color-bg-white);
      background-color: var(--moe-color-bg-brand-bold-press) !important;
      border-color: var(--moe-color-bg-brand-bold-press) !important;
    }
    &:disabled,
    &.disabled {
      color: var(--moe-color-bg-white);
      background-color: var(--moe-color-bg-brand-bold) !important;
      border-color: var(--moe-color-bg-brand-bold) !important;
    }
  }

  &.btn-primary-unfilled {
    color: var(--moe-color-bg-brand-bold);
    background: var(--moe-color-bg-white);
    border-color: var(--moe-color-bg-brand-bold);
    &:hover {
      color: var(--moe-color-bg-brand-bold-hover);
      border-color: var(--moe-color-bg-brand-bold-hover);
    }
    &:focus,
    &.focus {
      color: var(--moe-color-bg-brand-bold);
      background: var(--moe-color-bg-white);
      border-color: var(--moe-color-bg-brand-bold);
    }
    &:active,
    &.active {
      color: var(--moe-color-bg-brand-bold-press);
      background: var(--moe-color-bg-white);
      border-color: var(--moe-color-bg-brand-bold-press);
    }
    &:disabled,
    &.disabled {
      color: var(--moe-color-bg-brand-bold);
      background: var(--moe-color-bg-white);
      border-color: var(--moe-color-bg-brand-bold);
    }
  }

  &.btn-black-unfilled {
    ${mixin_button_style_unfilled(c_btn_color, '#666', '#666')};
  }

  &.btn-secondary {
    ${mixin_button_style(c_secondary, c_secondary, c_btn_color)};
  }

  &.btn-secondary-unfilled {
    ${mixin_button_style_unfilled(c_btn_color, c_secondary, c_secondary)};
  }

  &.btn-danger {
    ${mixin_button_style(c_danger, c_danger, c_btn_color)};
  }

  &.btn-danger-unfilled {
    ${mixin_button_style_unfilled(c_btn_color, c_danger, c_danger)};
  }

  &.btn-default {
    ${mixin_button_style(c_normal, c_normal, c_btn_color)};
  }

  &.btn-default-unfilled {
    ${mixin_button_style_unfilled(c_btn_color, c_normal, c_normal)};
  }

  &.btn-info {
    ${mixin_button_style(c_gray, c_gray, c_btn_color)};
  }

  &.btn-purple {
    ${mixin_button_style(c_purple, c_purple, c_btn_color)};
  }

  &.btn-info-unfilled {
    ${mixin_button_style_unfilled(c_btn_color, c_gray, c_gray)};
  }

  &.btn-link {
    ${mixin_button_style('transparent', 'transparent', c_btn_color)};
    ${mixin_button_size('0', '0', c_font_size_regular)};
    display: flex;
    align-items: center;
    box-shadow: none;
  }

  &.btn-link-underline {
    text-decoration: underline;
  }

  &.button-hover-animation {
    ${mixin_button_hover_animation};
  }

  &.btn-white,
  &.btn-white-border {
    background: #ffffff;
    box-shadow: none;
    border: 1px solid #cdcdcd;
    color: #2a2d34;
    transition: all 150ms ease-in-out;
  }
`;

export const primaryButtonWithDarkenStyle = css`
  height: 32px;
  padding: 7px 12px;
  border-radius: 56px;
  box-shadow: none;
  background-color: var(--moe-color-bg-brand-bold);

  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  font-size: 14px;
  line-height: 18px;
  font-weight: 700;
  color: #fff;

  outline: none;
  border: 0;
  transition: all 300ms ease-in-out;

  &:hover,
  &.active {
    color: white;
    background-color: var(--moe-color-bg-brand-bold-press);
  }
`;

export const NewBookingButton = styled.button`
  ${primaryButtonWithDarkenStyle}
  height: 29px;
  padding: 0 16px;
  font-size: 12px;
  font-weight: bold;
`;

export const AddButton = styled.span`
  cursor: pointer;
  color: var(--moe-color-icon-brand);
  font-size: 16px;
  font-weight: 600;
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  user-select: none;

  &:hover {
    color: var(--moe-color-text-brand-hover);
  }
`;
