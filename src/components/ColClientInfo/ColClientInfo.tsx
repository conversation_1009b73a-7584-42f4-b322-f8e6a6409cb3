import { useSelector } from 'amos';
import { Popconfirm, Tabs } from 'antd';
import classNames from 'classnames';
import React, { type FC, useEffect, useState } from 'react';
import IconIconCloseSvg from '../../assets/svg/icon-close.svg';
import { CofStatus } from '../../container/Payment/components/CofStatus';
import { type OpenApiModels } from '../../openApi/schema';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { Avatar } from '../Avatar/Avatar';
import { SvgIcon, type SvgIconProps } from '../Icon/Icon';
import { PanelAppointment } from './components/PanelAppointment';
import { ClientInfoExaTabs } from './components/PanelAppointment.style';
import { PanelClient } from './components/PanelClient';
import { PanelPayment } from './components/PanelPayment';
import { PanelPet } from './components/PanelPet';

type TNavbarItem = 'pet' | 'client' | 'payment' | 'appt';

interface INavbar {
  key: TNavbarItem;
  name: string;
}

interface IApptList {
  label: string;
  value: number;
  key: string;
}

export type TColClientInfoClient = OpenApiModels['GET/customer/booknew/detail']['Res']['data'];

interface ColClientInfoProps {
  antdTabs?: boolean;
  cancelable: boolean;
  propsClient: TColClientInfoClient;
  onCancel?: () => void; // Click On Cancel
  onBookAgain: (apptId: number) => void;
  avatarSize?: string;
  avatarClassNames?: string;
  clientNameClassNames?: string;
  closeIconProps: Pick<SvgIconProps, 'color' | 'size'>;
}

export const ColClientInfo: FC<ColClientInfoProps> = (props) => {
  const { propsClient, cancelable, onCancel, onBookAgain, antdTabs, avatarSize, closeIconProps } = props;
  const { customerId } = propsClient.customerDetail;
  const [business] = useSelector(selectCurrentBusiness());

  const [currentNavIndex, setCurrentNavIndex] = useState(-1);
  const navBarInitial: INavbar[] = [
    { key: 'appt', name: 'Appointment' },
    { key: 'pet', name: 'Pet' },
    { key: 'client', name: 'Client' },
    // { key: 'payment', name: 'Payments' },
  ];
  const [navbars, setNavbars] = useState([] as INavbar[]);
  const [panel, setPanel] = useState(<div></div>);
  const [client, setClient] = useState({} as TColClientInfoClient);
  const appListInitial: IApptList[] = [
    { label: 'Upcoming', value: 12, key: 'upcoming' },
    { label: 'Finished', value: 12, key: 'finished' },
    { label: 'Cancelled', value: 12, key: 'cancelled' },
    { label: 'No-show', value: 12, key: 'noshow' },
  ];
  const [apptList, setApptList] = useState(appListInitial);

  const handleClickClose = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const handleClickNavBar = (index: number) => {
    if (currentNavIndex === index) {
      return;
    }
    setCurrentNavIndex(index);
  };

  const rederPanel = () => {
    const currentNav = navbars[currentNavIndex];
    // console.log('render panel', currentNav, currentNavIndex, navbars)
    if (!currentNav || !currentNav.key) {
      return <div></div>;
    }
    let panel;
    switch (currentNav.key) {
      case 'pet':
        panel = <PanelPet client={client} petList={propsClient.petList} />;
        break;
      case 'client':
        panel = <PanelClient client={client} />;
        break;
      case 'payment':
        panel = <PanelPayment client={client} />;
        break;
      case 'appt':
        panel = <PanelAppointment client={client} onBookAgain={onBookAgain} />;
        break;
      default:
        panel = <div></div>;
        break;
    }
    setPanel(panel);
    return void 0;
  };

  useEffect(() => {
    setNavbars(navBarInitial);
  }, []);

  useEffect(() => {
    if (propsClient?.customerDetail?.customerId) {
      setClient(propsClient);
      setCurrentNavIndex(0);

      if (propsClient.appointmentInfo) {
        setApptList([
          { label: 'Upcoming', value: propsClient.appointmentInfo.upcoming, key: 'upcoming' },
          { label: 'Finished', value: propsClient.appointmentInfo.finished, key: 'finished' },
          { label: 'Cancelled', value: propsClient.appointmentInfo.cancelled, key: 'cancelled' },
          { label: 'No-show', value: propsClient.appointmentInfo.noShow, key: 'noshow' },
        ]);
      }
    }
  }, [propsClient?.customerDetail?.customerId]);

  useEffect(() => {
    rederPanel();
  }, [currentNavIndex]);

  if (!propsClient?.customerDetail?.customerId) {
    return null;
  }

  const customerName = [client?.customerDetail?.firstName, client?.customerDetail?.lastName]
    .filter((s) => !!s)
    .join(' ');

  return (
    <div className="fc-col-client-info-wrapper">
      <div className="client-wrapper">
        <div className="client-detail">
          <Avatar
            size={avatarSize ?? 'large'}
            className={classNames('client-detail-avatar', props.avatarClassNames)}
            src={propsClient.customerDetail?.avatarPath}
            info={propsClient.customerDetail}
          />
          <div className="client-detail-info">
            <div
              className="username"
              title={customerName}
              style={{
                color: client.customerDetail?.clientColor || '#2a2d34',
              }}
            >
              <p className={props.clientNameClassNames}>{customerName}</p>
              <CofStatus
                customerId={customerId}
                source="create-ticket-page"
                iconClassName="!moe-ml-[8px] !moe-flex-none"
              />
            </div>
            <div className="client-detail-info-row">
              <p className="phone-number rr-mask">{business.formatPhoneNumber(client.customerDetail?.phoneNumber)}</p>
              {/* <p className="tips">Details</p> */}
              <a
                className="tips"
                href={`/client/${client.customerDetail?.customerId}/overview`}
                target="_blank"
                rel="noreferrer"
              >
                Details
              </a>
            </div>
          </div>
        </div>
        <div className="appt-detail-list">
          {apptList.map((apptType, aIndex) => (
            <div className="appt-detail-list-item" key={aIndex}>
              <p className={apptType.key === 'noshow' ? 'numbers numbers-red' : 'numbers'}>{apptType.value}</p>
              <p className="desc">{apptType.label}</p>
            </div>
          ))}
        </div>
        {cancelable && (
          <Popconfirm
            placement="top"
            title="Do you want to reselect the client? Booking information will be cleared."
            onConfirm={handleClickClose}
            okText="Yes"
            cancelText="No"
          >
            <SvgIcon size={24} src={IconIconCloseSvg} className="icon-close" color="#4b4b4b" {...closeIconProps} />
          </Popconfirm>
        )}
      </div>
      {antdTabs && client.customerDetail ? (
        <ClientInfoExaTabs className="!moe-mt-[20px] !moe-bg-white">
          <Tabs defaultActiveKey="appt">
            <Tabs.TabPane tab="Appointment" key="appt">
              <PanelAppointment client={client} onBookAgain={onBookAgain} />
            </Tabs.TabPane>
            <Tabs.TabPane tab="Pet" key="pet">
              <PanelPet client={client} petList={propsClient.petList} />
            </Tabs.TabPane>
            <Tabs.TabPane tab="Client" key="client">
              <PanelClient client={client} />
            </Tabs.TabPane>
          </Tabs>
        </ClientInfoExaTabs>
      ) : (
        <>
          <div className="info-navbar">
            {navbars.map((navbar, nIndex) => (
              <div
                className={currentNavIndex === nIndex ? 'info-navbar-item info-navbar-item-active' : 'info-navbar-item'}
                key={nIndex}
                onClick={() => handleClickNavBar(nIndex)}
              >
                {navbar.name}
              </div>
            ))}
          </div>
          <div className="info-content-wrapper">{panel}</div>
        </>
      )}
    </div>
  );
};
