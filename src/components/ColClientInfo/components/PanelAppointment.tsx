import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useSelector } from 'amos';
import { Divider } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import unionBy from 'lodash/unionBy';
import React, { memo } from 'react';
import SvgIconClockGraySvg from '../../../assets/svg/icon-clock-gray.svg';
import SvgIconGroomGraySvg from '../../../assets/svg/icon-groom-gray.svg';
import SvgIconPawGraySvg from '../../../assets/svg/icon-paw-gray.svg';
import { useCreateTicketVersion } from '../../../container/CreateTicket/hooks/useCreateTicketVersion';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { staffMapBox } from '../../../store/staff/staff.boxes';
import { withPl } from '../../../utils/calculator';
import { Button } from '../../Button/Button';
import { SvgIcon } from '../../Icon/Icon';
import { modalApi } from '../../Modal/Modal';
import { type TColClientInfoClient } from '../ColClientInfo';
import { PanelAppointmentView } from './PanelAppointment.style';

export interface PanelAppointmentProps {
  client: TColClientInfoClient;
  onBookAgain: (apptId: number) => void;
}

export type TPanelKey = 'lastAppointment' | 'nextAppointment';
const PANEL_KEY: TPanelKey[] = ['lastAppointment', 'nextAppointment'];

export const PanelAppointment = memo<PanelAppointmentProps>(({ client, onBookAgain }) => {
  const [business, staffMap] = useSelector(selectCurrentBusiness, staffMapBox);
  const { isCreateTicketOldVersion, isCreateTicketV2Version } = useCreateTicketVersion();
  const handleClickBookAgain = () => {
    if (!client.lastAppointment.id) {
      return;
    }
    modalApi.confirm({
      title: 'Do you want to book again?',
      icon: <ExclamationCircleOutlined />,
      content: 'It will override your ticket form content.',
      okText: 'Yes',
      okType: 'primary',
      cancelText: 'No',
      onOk: () => {
        onBookAgain(client.lastAppointment.id);
      },
      onCancel: () => {
        console.log('Cancel');
      },
    });
  };

  const renderApptDateTime = (fields: TPanelKey) => {
    const date = client[fields].appointmentDate;
    const startTime = client[fields].appointmentStartTime;
    const endTime = client[fields].appointmentEndTime;
    if (!date || typeof startTime !== 'number' || typeof endTime !== 'number') {
      return '';
    }
    const start = dayjs(`${Math.floor(startTime / 60)}:${startTime % 60}`, 'HH:mm').format(business.timeFormat());
    const end = dayjs(`${Math.floor(endTime / 60)}:${endTime % 60}`, 'HH:mm').format(business.timeFormat());
    const formatedDate = business.formatFixedDate(date);
    return `${start}-${end} ${formatedDate}`;
  };

  const renderPetInfo = (fields: TPanelKey) => {
    const petSourceList = client.petList.map((pet) => pet.petDetail);
    const allPetApptList = client[fields].petDetails as TColClientInfoClient['lastAppointment']['petDetails'];
    if (!allPetApptList) {
      return '';
    }
    // 通过 petid 获取去重后的 pet list。
    const petApptList = unionBy(allPetApptList, 'petId');
    return petApptList
      .map((dpet) => {
        if (dpet.petName && dpet.petBreed) {
          return {
            petName: dpet.petName,
            breed: dpet.petBreed,
            id: dpet.petId,
          };
        }
        const petId = dpet.petId;
        const pet = petSourceList.find((pet) => pet.petId === petId);
        if (!pet?.petId) {
          return {
            petName: '',
            breed: '',
            id: -1,
          };
        }
        return {
          petName: pet.petName,
          breed: pet.breed,
          id: pet.petId,
        };
      })
      .filter((p) => p.id > 0)
      .map((p) => `${p.petName} (${p.breed})`)
      .join(',');
  };

  const renderServiceInfo = (fields: TPanelKey) => {
    const serviceDetailList = client[fields].petDetails as TColClientInfoClient['lastAppointment']['petDetails'];
    if (!serviceDetailList) {
      return '';
    }
    if (isCreateTicketV2Version) {
      return serviceDetailList
        .map((item) => {
          const { operationList } = item as any;
          const staffs: number[] =
            Array.isArray(operationList) && operationList.length > 0
              ? operationList.map((i) => i.staffId)
              : [item.staffId];
          const staffNames = staffs.map((i) => staffMap.mustGetItem(i).firstName);
          // 拼接
          // ['a', 'b', 'c'] -> a, b and c
          // ['a', 'b'] -> a and b
          const byStaffNames = staffNames.join(',\u{20}').replace(/(.*),\s(.*)$/, '$1 and $2');
          return {
            serviceName: item.serviceName,
            price: item.servicePrice,
            time: item.serviceTime,
            byStaffNames,
          };
        })
        .map((item, index) => (
          <div className="appt-service-row" key={index}>
            <p className="appt-serivce-row-p !moe-mb-[4px]">{item.serviceName}</p>
            <p className="appt-serivce-row-p !moe-text-[14px] !moe-text-[#999] !moe-font-medium">
              {business.formatAmount(item.price)}&nbsp;&nbsp;{withPl(item.time, 'min')} by {item.byStaffNames}
            </p>
          </div>
        ));
    }
    return serviceDetailList
      .map((item) => ({
        serviceName: item.serviceName,
        price: item.servicePrice,
        time: item.serviceTime,
      }))
      .map((item, index) => (
        <div className="appt-service-row" key={index}>
          <p className="appt-serivce-row-p">{item.serviceName}</p>
          <p className="appt-serivce-row-p">
            {withPl(item.time, 'min')}&nbsp;&nbsp;{`${business.currencySymbol}${item.price}`}
          </p>
        </div>
      ));
  };

  return (
    <PanelAppointmentView>
      {PANEL_KEY.map((apptType, index) => (
        <React.Fragment key={apptType}>
          <div className="appt-wrapper">
            <div className="appt-wrapper-header">
              <p className="appt-title">{apptType === 'lastAppointment' ? 'Last' : 'Next'} appointment</p>
              {apptType === 'lastAppointment' && client.lastAppointment.id && (
                <Button
                  style={{
                    color: '#2a2d34',
                    background: '#FFF',
                    padding: '6px 16px',
                    border: '1px solid #2a2d34',
                  }}
                  buttonRadius="circle"
                  size="lg"
                  onClick={handleClickBookAgain}
                >
                  Book again
                </Button>
              )}
            </div>
            <div className="appt-wrapper-content">
              {client[apptType].id > 0 ? (
                <>
                  <div className="appt-info-row">
                    <div className="appt-info-row-label">
                      {isCreateTicketOldVersion && (
                        <SvgIcon
                          src={SvgIconClockGraySvg}
                          style={{
                            height: '12px',
                            width: '12px',
                            marginRight: '8px',
                          }}
                        />
                      )}
                      Date{isCreateTicketOldVersion && ':'}
                    </div>
                    <div className="appt-info-row-value">{renderApptDateTime(apptType)}</div>
                  </div>
                  <div className="appt-info-row">
                    <div className="appt-info-row-label">
                      {isCreateTicketOldVersion && (
                        <SvgIcon
                          src={SvgIconPawGraySvg}
                          style={{
                            height: '12px',
                            width: '13px',
                            marginRight: '8px',
                          }}
                        />
                      )}
                      Pet{isCreateTicketOldVersion && ':'}
                    </div>
                    <div className="appt-info-row-value">{renderPetInfo(apptType)}</div>
                  </div>
                  <div className="appt-info-row">
                    <div className="appt-info-row-label">
                      {isCreateTicketOldVersion && (
                        <SvgIcon
                          src={SvgIconGroomGraySvg}
                          style={{
                            height: '11.4px',
                            width: '9.7',
                            marginRight: '8px',
                          }}
                        />
                      )}
                      Service{isCreateTicketOldVersion && ':'}
                    </div>
                    <div
                      className={classNames(
                        'appt-info-row-value',
                        isCreateTicketV2Version ? '!moe-flex !moe-flex-col !moe-gap-y-[12px]' : undefined,
                      )}
                    >
                      {renderServiceInfo(apptType)}
                    </div>
                  </div>
                </>
              ) : (
                <div className="!moe-text-[#666]">
                  {apptType === 'lastAppointment' ? 'No previous appointments' : 'No upcoming appointments'}
                </div>
              )}
            </div>
          </div>
          {isCreateTicketV2Version && index !== PANEL_KEY.length - 1 && (
            <div className="!moe-px-[24px]">
              <Divider style={{ margin: 0, borderTop: '1px solid #E6E6E6' }} />
            </div>
          )}
        </React.Fragment>
      ))}
    </PanelAppointmentView>
  );
});
