import { useSelector } from 'amos';
import React, { type FC } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type TColClientInfoClient } from '../ColClientInfo';

interface PanelPaymentProps {
  client: TColClientInfoClient;
}

export const PanelPayment: FC<PanelPaymentProps> = (props) => {
  const { client } = props;
  const [business] = useSelector(selectCurrentBusiness);

  if (!client?.customerDetail?.customerId) {
    return null;
  }

  return (
    <div className="payment-panel-wrapper">
      <div className="payment-row">
        <p className="payment-row-title">Total Paid</p>
        <p className="payment-row-value">
          {business.currencySymbol}
          {client.payments.totalPaid}
        </p>
      </div>
      <div className="payment-row">
        <p className="payment-row-title">Unpaid</p>
        <p className="payment-row-danger-value">
          {business.currencySymbol}
          {client.payments.totalOutstandingBalance}
        </p>
      </div>
      <div className="payment-row">
        <p className="payment-row-title">Total payment amount</p>
        <p className="payment-row-value">
          {business.currencySymbol}
          {client.payments.outstandingBalanceDue || 0}
        </p>
      </div>
      {/* <div className="payment-row">
        <p className="payment-row-title">Outstanding balance due</p>
        <p className="payment-row-danger-value">
          {business.currencySymbol}
          {client.paymentStat?.outstandingBalanceDue}
        </p>
      </div>
      <div className="payment-row">
        <p className="payment-row-title">Available credit</p>
        <p className="payment-row-value">
          {business.currencySymbol}
          {client.paymentStat?.availableCredit}
        </p>
      </div> */}
    </div>
  );
};
