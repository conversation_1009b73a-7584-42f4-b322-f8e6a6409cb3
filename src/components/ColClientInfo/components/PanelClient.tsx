import { useSelector } from 'amos';
import React, { type FC, useEffect, useState } from 'react';
import SvgBusinessIconPhoneSvg from '../../../assets/svg/business-icon-phone.svg';
import SvgIconLocationGraySvg from '../../../assets/svg/icon-location-gray.svg';
import SvgIconMailSvg from '../../../assets/svg/icon-mail.svg';
import SvgIconNoteGraySvg from '../../../assets/svg/icon-note-gray.svg';
import SvgIconPaymentCardGraySvg from '../../../assets/svg/icon-payment-card-gray.svg';
import { useCreateTicketVersion } from '../../../container/CreateTicket/hooks/useCreateTicketVersion';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { printAddress } from '../../../store/customer/customerAddress.selectors';
import { WithPermission } from '../../GuardRoute/WithPermission';
import { SvgIcon } from '../../Icon/Icon';
import { type TColClientInfoClient } from '../ColClientInfo';

interface PanelClientProps {
  client: TColClientInfoClient;
}

interface INoteListItem {
  isShowComplete: boolean;
}

export type TClientNoteUnion = TColClientInfoClient['customerDetail']['noteList'][0] & INoteListItem;

export const PanelClient: FC<PanelClientProps> = (props) => {
  const { client } = props;
  const { isCreateTicketOldVersion } = useCreateTicketVersion();
  const [noteList, setNoteList] = useState([] as TClientNoteUnion[]);
  const [isShowMoreNote, setIsShowMoreNote] = useState(true);
  const [business] = useSelector(selectCurrentBusiness);

  const NOTE_SHOW_LENGTH = 2;

  const toggleNoteShowMoreList = () => {
    setIsShowMoreNote(!isShowMoreNote);
  };

  const toggleNoteMoreVisible = (noteIndex: number) => {
    const newList = [...noteList];
    newList[noteIndex].isShowComplete = !newList[noteIndex].isShowComplete;
    setNoteList(newList);
  };

  useEffect(() => {
    if (client.customerDetail && client.customerDetail.noteList) {
      const newNoteList = client.customerDetail.noteList.map((note) => {
        const obj: TClientNoteUnion = Object.assign({}, note, {
          isShowComplete: note.note.length < 117,
        });
        return obj;
      });
      setNoteList(newNoteList);
      setIsShowMoreNote(newNoteList.length <= NOTE_SHOW_LENGTH);
    }
  }, [client.customerDetail]);

  useEffect(() => {}, [noteList]);

  if (!client?.customerDetail?.customerId) {
    return null;
  }

  return (
    <div className="client-panel-wrapper">
      {isCreateTicketOldVersion && (
        <div className="client-name-card">
          {client.customerDetail.firstName} {client.customerDetail.lastName}
        </div>
      )}
      <div className="client-detail">
        <div className="client-detail-row">
          <p className="row-label">
            {isCreateTicketOldVersion && <SvgIcon src={SvgBusinessIconPhoneSvg} className="row-label-icon" />}
            Number
          </p>
          <p className="row-value-text">{client.customerDetail.phoneNumber}</p>
        </div>
        {client.customerDetail.email ? (
          <div className="client-detail-row">
            <p className="row-label">
              {isCreateTicketOldVersion && <SvgIcon src={SvgIconMailSvg} className="row-label-icon" />}
              Email
            </p>
            <p className="row-value-text">{client.customerDetail.email}</p>
          </div>
        ) : (
          ''
        )}
        <div className="client-detail-row">
          <p className="row-label">
            {isCreateTicketOldVersion && <SvgIcon src={SvgIconLocationGraySvg} className="row-label-icon" />}
            Address
          </p>
          <p className="row-value-text rr-mask">{printAddress(client.customerDetail?.address)}</p>
        </div>
        <div className="client-detail-row">
          <div className="row-label">
            {isCreateTicketOldVersion && <SvgIcon src={SvgIconPaymentCardGraySvg} className="row-label-icon" />}
            Payment
          </div>
          <div className="row-value-text">
            <WithPermission permissions="accessClientTotalPaidInfo">
              <div className="payment-row">
                <p className="payment-row-label">Total paid:</p>
                <p className="payment-row-value">{`${business.currencySymbol}${client.payments.totalPaid}`}</p>
              </div>
            </WithPermission>
            <div className="payment-row">
              <p className="payment-row-label">Unpaid:</p>
              <p
                className="payment-row-value"
                style={{ color: '#d0021b' }}
              >{`${business.currencySymbol}${client.payments.totalOutstandingBalance}`}</p>
            </div>
            <WithPermission permissions="accessClientTotalPaidInfo">
              <div className="payment-row">
                <p className="payment-row-label">Total payment:</p>
                <p className="payment-row-value">{`${business.currencySymbol}${client.payments.totalPaid}`}</p>
              </div>
            </WithPermission>
          </div>
        </div>
        <div className="client-detail-row">
          <p className="row-label">
            {isCreateTicketOldVersion && <SvgIcon src={SvgIconNoteGraySvg} className="row-label-icon" />}
            Private notes
          </p>
          <div className="row-value-notes">
            {noteList.map((note, nIndex) =>
              isShowMoreNote || nIndex < NOTE_SHOW_LENGTH ? (
                <div className="note" key={nIndex}>
                  <p className={note.isShowComplete ? 'note-text' : 'note-text note-text-hidden'}>{note.note}</p>
                  {note.isShowComplete ? (
                    ''
                  ) : (
                    <p className="show-more" onClick={() => toggleNoteMoreVisible(nIndex)}>
                      more
                    </p>
                  )}
                </div>
              ) : (
                <div key={nIndex}></div>
              ),
            )}
            {!isShowMoreNote ? (
              <p className="show-more-notes" onClick={toggleNoteShowMoreList}>
                Show more
              </p>
            ) : (
              ''
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
