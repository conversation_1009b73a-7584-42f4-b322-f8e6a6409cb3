import { useSelector } from 'amos';
import { Divider } from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import React, { type FC, useEffect, useState } from 'react';
import SvgIconBirthdayGraySvg from '../../../assets/svg/icon-birthday-gray.svg';
import SvgIconGenderGraySvg from '../../../assets/svg/icon-gender-gray.svg';
import SvgIconNoteGraySvg from '../../../assets/svg/icon-note-gray.svg';
import SvgIconPawGraySvg from '../../../assets/svg/icon-paw-gray.svg';
import SvgIconVaccinationGraySvg from '../../../assets/svg/icon-vaccination-gray.svg';
import SvgIconWeightGraySvg from '../../../assets/svg/icon-weight-gray.svg';
import { PetCodeIcon } from '../../../container/Client/ClientInfo/Pets/components/PetCodeTag';
import { useCreateTicketVersion } from '../../../container/CreateTicket/hooks/useCreateTicketVersion';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { PetLifeStatus } from '../../../store/pet/pet.boxes';
import { Avatar } from '../../Avatar/Avatar';
import { SvgIcon } from '../../Icon/Icon';
import { PetCodeGroupTooltip } from '../../PetInfo/PetCodeGroupTooltip';
import { PetDeactivateReason, PetDeactivateReasonMap } from '../../PetInfo/hooks/useClientPets';
import { type TColClientInfoClient } from '../ColClientInfo';

interface PanelPetProps {
  petList: TColClientInfoClient['petList'];
  client: TColClientInfoClient;
}

export const PanelPet: FC<PanelPetProps> = (props) => {
  const { petList, client } = props;

  // console.log('PanelPet -> ', petList)

  interface IPetExtend {
    isShowMoreNote: boolean;
  }

  interface IPetNoteUI {
    isShowComplete: boolean;
  }

  interface IPetNoteUnion {
    petNoteList: [IPetNoteUI & TColClientInfoClient['petList'][0]['petNoteList'][0]];
  }

  type TPetUnion = IPetExtend & IPetNoteUnion & TColClientInfoClient['petList'][0];
  const [list, setList] = useState([] as TPetUnion[]);
  const [business] = useSelector(selectCurrentBusiness);
  const { isCreateTicketV2Version, isCreateTicketOldVersion } = useCreateTicketVersion();
  const NOTE_SHOW_LENGTH = 2;

  const returnPetAvatarConfig = (pet: TColClientInfoClient['petList'][0]['petDetail']) => {
    return pet.petTypeId === 1 ? 'dog' : pet.petTypeId === 2 ? 'cat' : 'other';
  };

  const togglePetNoteVisible = (noteIndex: number, petIndex: number, status: boolean) => {
    const newList = [...list];
    newList[petIndex].petNoteList[noteIndex].isShowComplete = !status;
    setList(newList);
  };

  const toggleNoteShowMoreList = (petIndex: number) => {
    const newList = [...list];
    newList[petIndex].isShowMoreNote = !newList[petIndex].isShowMoreNote;
    setList(newList);
  };

  const openNewPetWindow = (pet: TPetUnion) => {
    window.open(
      window.location.origin + `/client/${client.customerDetail.customerId}/pets/${pet.petDetail.petId}`,
      '_blank',
    );
  };

  useEffect(() => {
    if (petList) {
      setList(
        petList.map((pet) => {
          const obj = _.merge({}, pet, {
            isShowMoreNote: pet.petNoteList.length <= NOTE_SHOW_LENGTH,
          }) as TPetUnion;
          obj.petNoteList.forEach((note, nIndex) => {
            obj.petNoteList[nIndex]['isShowComplete'] = note.note.length < 117;
          });
          return obj;
        }),
      );
    }
  }, [petList]);

  if (!client?.customerDetail?.customerId) {
    return null;
  }

  return (
    <div className={classNames('pet-panel-list normal-scrollbar', isCreateTicketV2Version ? '!moe-h-auto' : undefined)}>
      {list.map((pet, petIndex) => (
        <React.Fragment key={petIndex}>
          <div className="pet-panel-list-item">
            <div className="pet-card">
              <Avatar
                size={isCreateTicketV2Version ? '56px' : 'large'}
                className="pet-card-avatar"
                src={pet.petDetail.avatarPath}
                type={returnPetAvatarConfig(pet.petDetail)}
                onClick={() => openNewPetWindow(pet)}
                {...(pet.petDetail.lifeStatus === PetLifeStatus.PassAway && {
                  passAway: true,
                  tooltip:
                    PetDeactivateReasonMap[
                      pet.petDetail.deactivateReason in PetDeactivateReason
                        ? (pet.petDetail.deactivateReason as PetDeactivateReason)
                        : PetDeactivateReason.PASS_AWAY
                    ],
                })}
              />
              <div className="pet-card-content">
                <div className="pet-name" onClick={() => openNewPetWindow(pet)}>
                  {pet.petDetail.petName}
                </div>
                <PetCodeGroupTooltip codesIdList={pet.petCodeList.map((p) => p.petCodeId)} petId={pet.petDetail.petId}>
                  <div className="pet-petcode">
                    {pet.petCodeList.map((p, pIndex) => (
                      <PetCodeIcon
                        key={pIndex}
                        petId={pet.petDetail.petId}
                        code={{
                          ...p,
                          id: +p.petCodeId,
                        }}
                      />
                    ))}
                  </div>
                </PetCodeGroupTooltip>
              </div>
            </div>
            <div className="pet-detail">
              <div className="pet-detail-row">
                <p className="row-label">
                  {isCreateTicketOldVersion && <SvgIcon src={SvgIconPawGraySvg} className="row-label-icon" />}
                  <span className="row-label-text">Breed</span>
                </p>
                <p className="row-value-text">{pet.petDetail.breed}</p>
              </div>
              <div className="pet-detail-row">
                <p className="row-label">
                  {isCreateTicketOldVersion && <SvgIcon src={SvgIconGenderGraySvg} className="row-label-icon" />}
                  <span className="row-label-text">Gender</span>
                </p>
                {pet.petDetail.gender ? (
                  <p className="row-value-text">{pet.petDetail.gender === 1 ? 'Male' : 'Female'}</p>
                ) : (
                  <p className="row-value-text">N/A</p>
                )}
              </div>
              <div className="pet-detail-row">
                <p className="row-label">
                  {isCreateTicketOldVersion && <SvgIcon src={SvgIconBirthdayGraySvg} className="row-label-icon" />}
                  <span className="row-label-text">Birthday</span>
                </p>
                {pet.petDetail.birthday ? (
                  <p className="row-value-text">{business.formatFixedDate(pet.petDetail.birthday)}</p>
                ) : (
                  <p className="row-value-text">N/A</p>
                )}
              </div>
              <div className="pet-detail-row">
                <p className="row-label">
                  {isCreateTicketOldVersion && <SvgIcon src={SvgIconWeightGraySvg} className="row-label-icon" />}
                  <span className="row-label-text">Weight</span>
                </p>
                {pet.petDetail.weight ? (
                  <p className="row-value-text">
                    {pet.petDetail.weight} {business.unitOfWeight}
                  </p>
                ) : (
                  <p className="row-value-text">N/A</p>
                )}
              </div>
              <div className="pet-detail-row">
                <p className="row-label">
                  {isCreateTicketOldVersion && <SvgIcon src={SvgIconVaccinationGraySvg} className="row-label-icon" />}
                  <span className="row-label-text">Vaccinations</span>
                </p>
                {pet.vaccineStatus.haveVaccineBinding ? (
                  pet.vaccineStatus.haveExpired ? (
                    <p className="row-value-text row-value-text-red" onClick={() => openNewPetWindow(pet)}>
                      Expired
                    </p>
                  ) : (
                    <p className="row-value-text row-value-text-green" onClick={() => openNewPetWindow(pet)}>
                      Available
                    </p>
                  )
                ) : (
                  <p className="row-value-text">N/A</p>
                )}
              </div>
              <div className="pet-detail-row">
                <p className="row-label">
                  {isCreateTicketOldVersion && <SvgIcon src={SvgIconNoteGraySvg} className="row-label-icon" />}
                  <span className="row-label-text">Pet notes</span>
                </p>
                <div className="row-value-notes">
                  {pet.petNoteList.map((note, nIndex) =>
                    pet.isShowMoreNote || nIndex < NOTE_SHOW_LENGTH ? (
                      <div className="note" key={nIndex}>
                        <p className={note.isShowComplete ? 'note-text' : 'note-text note-text-hidden'}>{note.note}</p>
                        {note.isShowComplete ? (
                          ''
                        ) : (
                          <p
                            className="show-more"
                            onClick={() => togglePetNoteVisible(nIndex, petIndex, note.isShowComplete)}
                          >
                            {/* text-lint ignore */}
                            more
                          </p>
                        )}
                      </div>
                    ) : (
                      <div key={nIndex}></div>
                    ),
                  )}
                  {!pet.isShowMoreNote ? (
                    <p className="show-more-notes" onClick={() => toggleNoteShowMoreList(petIndex)}>
                      Show more
                    </p>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            </div>
          </div>
          {isCreateTicketV2Version && petIndex !== list.length - 1 && (
            <div className="!moe-px-[24px]">
              <Divider style={{ margin: 0, borderTop: '1px solid #E6E6E6' }} />
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};
