import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Checkbox, type ClassConfig, Markup, LegacySelect as Select, Text } from '@moego/ui';
import { type SelectAllProps } from '@moego/ui/dist/esm/components/LegacySelect/ReactSelect';
import { type BaseWrapperSlotsClasses } from '@moego/ui/dist/esm/components/LegacySelect/Select.style';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCompanyCareTypeNameMap, selectSceneCareTypeAsOptions } from '../../store/careType/careType.selectors';
import { type Scene } from '../../store/service/scene.enum';

export interface CareTypeMultiSelectorProps {
  careTypes: ServiceItemType[];
  onChange: (careTypes: ServiceItemType[]) => void;
  scene?: Scene;
  displayLabel?: boolean;
  classNames?: ClassConfig & BaseWrapperSlotsClasses;
}

export const CareTypeMultiSelector = memo<CareTypeMultiSelectorProps>((props) => {
  const { careTypes, onChange, displayLabel, classNames, scene = undefined } = props;
  const [careTypeOptions, companyCareTypeNameMap] = useSelector(
    selectSceneCareTypeAsOptions(scene),
    selectCompanyCareTypeNameMap,
  );
  return (
    <Select
      label={displayLabel ? 'Care type:' : undefined}
      options={careTypeOptions}
      isMultiple
      isSearchable={false}
      showSelectAll
      placeholder="Select"
      value={careTypes}
      onChange={(values) => {
        onChange(values as ServiceItemType[]);
      }}
      renderTagModeValues={(values) => {
        const isSelectAll = values.length === careTypeOptions.length;
        const careTypeLabels = values.map((option) => companyCareTypeNameMap.getName(option.value as ServiceItemType));
        return (
          <Text variant="regular-short" ellipsis>
            {isSelectAll ? 'All care types' : careTypeLabels.join(', ')}
          </Text>
        );
      }}
      renderSelectAll={({ isSelected, isIndeterminate }: SelectAllProps) => {
        const OptionText = isSelected || isIndeterminate ? Markup : Text;
        return (
          <div className="moe-flex moe-flex-col moe-items-start moe-justify-center">
            <Checkbox
              className="group-hover/select-all:moe-stroke-bg-brand-bold"
              isSelected={isSelected}
              isIndeterminate={isIndeterminate && !isSelected}
            >
              <OptionText as={'span'} variant="small">
                All care types
              </OptionText>
            </Checkbox>
          </div>
        );
      }}
      classNames={{
        formItemWrapper: 'moe-flex-row moe-inline-flex moe-ml-s',
        formItemLabel: 'moe-mr-[8px]',
        control: 'moe-w-[188px]',
        valueContainer: '!moe-flex-nowrap',
        ...classNames,
      }}
    />
  );
});
