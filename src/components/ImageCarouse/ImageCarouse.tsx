import { Carousel } from 'antd';
import classNames from 'classnames';
import React, { type CSSProperties, type ReactNode } from 'react';
import { ImageCarouselBox } from './ImageCarouse.style';

export interface ImageCarouseImgConfig {
  imgUrl: string;
  title: ReactNode;
  description: string;
  link?: ReactNode;
  titleStyle?: CSSProperties;
  descriptionStyle?: CSSProperties;
  bgBoxStyle?: CSSProperties;
}

export interface ImageCarouseProps {
  className?: string;
  itemClassName?: string;
  imgConfigList: ImageCarouseImgConfig[];
}

export function ImageCarouse({ imgConfigList, itemClassName, className }: ImageCarouseProps) {
  return (
    <ImageCarouselBox className={className}>
      <Carousel autoplay>
        {imgConfigList.map((item) => {
          return (
            <div key={item.imgUrl}>
              <div
                className={classNames('carousel-item', itemClassName)}
                style={{
                  backgroundImage: `url(${item.imgUrl})`,
                  ...item.bgBoxStyle,
                }}
              >
                <h3 className="title" style={item.titleStyle}>
                  {item.title}
                </h3>
                <div className="description" style={item.descriptionStyle}>
                  {item.description}
                </div>
                {item.link && <div className="!moe-mt-[16px]">{item.link}</div>}
              </div>
            </div>
          );
        })}
      </Carousel>
    </ImageCarouselBox>
  );
}
