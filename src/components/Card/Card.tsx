import classNames from 'classnames';
import React, { forwardRef, memo, type PropsWithChildren, type ReactNode } from 'react';
import { ScrollerProvider } from '../../layout/components/ScrollerProvider';
import { CardDesc, CardHeader, CardHeaderSwitch, CardView, FlexCenterBetween, SwitchCardView } from './Card.style';

type TSize = 'large' | 'small';

export interface CardProps {
  title?: React.ReactNode;
  className?: string;
  headerClassName?: string;
  bodyClassName?: string;
  extra?: React.ReactNode;
  style?: React.CSSProperties;
  headerStyle?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
  size?: TSize;
  showHeader?: boolean;
  bodyScroll?: boolean;
  mini?: boolean;
}

export interface SwitchCardProps {
  title: string;
  className?: string;
  switchNode?: ReactNode;
  desc?: ReactNode;
  extra?: ReactNode;
  style?: React.CSSProperties;
  children?: ReactNode;
}

export const Card = forwardRef<HTMLDivElement, PropsWithChildren<CardProps>>((props, ref) => {
  const {
    style,
    headerStyle,
    bodyStyle,
    className,
    bodyClassName,
    headerClassName,

    title = '',
    extra = null,
    children,
    size,
    showHeader = false,
    bodyScroll = false,
    mini,
  } = props;
  const classnames = classNames(
    'card',
    {
      [`card-${size}`]: size,
      mini,
    },
    className,
  );

  const headerClassnames = classNames('card-header', children ? '' : 'null-children', headerClassName);
  const bodyClassnames = classNames('card-container', bodyClassName);

  return (
    <CardView ref={ref} className={classnames} style={style}>
      {title || showHeader ? (
        <div
          className={headerClassnames}
          style={{
            height: typeof title === 'string' ? 52 : 'auto',
            ...headerStyle,
          }}
        >
          {typeof title === 'string' ? (
            <p className="card-header-title" data-title={title}>
              {title}
            </p>
          ) : (
            title
          )}
          <div className="card-header-right">{extra}</div>
        </div>
      ) : null}
      {children ? (
        bodyScroll ? (
          <ScrollerProvider className={bodyClassnames} style={bodyStyle}>
            {children}
          </ScrollerProvider>
        ) : (
          <div className={bodyClassnames} style={bodyStyle}>
            {children}
          </div>
        )
      ) : null}
    </CardView>
  );
});

export const SwitchCard = memo<SwitchCardProps>(
  ({ className, title, switchNode, desc, extra, style, children = null }) => (
    <SwitchCardView className={className} style={style}>
      <FlexCenterBetween>
        <CardHeaderSwitch>
          <CardHeader>{title}</CardHeader>
          {switchNode ?? null}
        </CardHeaderSwitch>
        {extra ?? null}
      </FlexCenterBetween>
      <CardDesc>{desc}</CardDesc>
      {children}
    </SwitchCardView>
  ),
);
