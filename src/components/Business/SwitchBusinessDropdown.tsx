/**
 * @since 2023-12-20
 * <AUTHOR>
 * @description Available for new account structure and has more than one location
 * and has permission to switch business
 */
import { cn } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useEffect, useState } from 'react';
import { switchBusiness } from '../../store/business/business.actions';
import { useBool } from '../../utils/hooks/useBool';
import { useDrawerVisible } from '../../utils/hooks/useDrawerVisible';
import { Condition } from '../Condition';
import { Loading } from '../Loading/Loading';
import { useNewAccountStructure } from '../WithFeature/useNewAccountStructure';
import { SingleLocationDropdown, type SingleLocationDropdownProps } from './SingleLocationDropdown';
import { ALL_LOCATIONS } from './SingleLocationSelector';
import { useAutoFallbackLocationValue } from './hooks/useAutoFallbackLocationValue';

export const SwitchBusinessDropdown = memo<
  SingleLocationDropdownProps & {
    onBeforeChange?: (id: string) => void;
  }
>((props) => {
  const { onChange, onBeforeChange, scene = 'working', isDisabled } = props;
  const dispatch = useDispatch();
  const { isNewAndHasMultipleLocation } = useNewAccountStructure(scene);
  const [local, setLocal] = useState<string | undefined>(undefined);
  const [locationId] = useAutoFallbackLocationValue({
    preferred: 'current',
    scene,
  });
  const drawerVisible = useDrawerVisible();
  const loading = useBool(false);

  useEffect(() => {
    setLocal(locationId);
  }, [locationId]);

  const setBusiness = async (id: string) => {
    if (id === ALL_LOCATIONS) {
      throw new Error('Cannot switch to all location');
    }
    if (id) {
      loading.open();
      try {
        await onBeforeChange?.(id);
        await dispatch(switchBusiness(Number(id)));
        setLocal(id);
        onChange?.(id);
      } finally {
        loading.close();
      }
    }
  };

  return (
    <>
      <Condition if={isNewAndHasMultipleLocation}>
        <SingleLocationDropdown
          {...props}
          isDisabled={drawerVisible || isDisabled || loading.value}
          scene={scene}
          className={cn('moe-ml-s', props.className)}
          value={local || locationId}
          onChange={setBusiness}
        />
        <Condition if={loading.value}>
          <Loading loading></Loading>
        </Condition>
      </Condition>
    </>
  );
});
