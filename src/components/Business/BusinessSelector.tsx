import { MinorLocationOutlined } from '@moego/icons-react';
import { Text } from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import { useSelector } from 'amos';
import React from 'react';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { selectLocationDetailByBusiness } from '../../store/business/location.selectors';
import { useNewAccountStructure } from '../WithFeature/useNewAccountStructure';
import { type Option } from './SingleLocationDropdown';
import { SwitchBusinessDropdown } from './SwitchBusinessDropdown';

export const BusinessSelector = () => {
  const [locationDetail, business] = useSelector(selectLocationDetailByBusiness(), selectCurrentBusiness());
  const { isSingleLocation, isNewAndHasMultipleLocation } = useNewAccountStructure('working');

  const getLabel = (option: Option) => {
    return option.label + (option.phone ? `: ${business.formatPhoneNumber(option.phone)}` : '');
  };

  const formatOption = useMemoizedFn((option: Option) => {
    return (
      <div className="moe-flex moe-items-center moe-gap-[10px]">
        <span className="ml-2">{getLabel(option)}</span>
      </div>
    );
  });

  if (isNewAndHasMultipleLocation) {
    return (
      <div className="moe-flex">
        <SwitchBusinessDropdown
          scene="working"
          formatOptionTitle={formatOption}
          formatSelectedOptionTitle={getLabel}
          classNames={{
            menu: { base: 'moe-max-w-[400px] moe-min-w-[220px] !moe-w-fit' },
            text: '!moe-max-w-[350px]',
          }}
        />
      </div>
    );
  } else if (isSingleLocation) {
    return (
      <div className={'moe-inline-flex moe-gap-xxs moe-items-center moe-cursor-pointer'}>
        <MinorLocationOutlined className="moe-w-8px-200" />
        <div className="moe-flex moe-flex-column moe-flex-1 moe-text-primary">
          <Text
            variant="regular-short"
            className={'moe-max-w-[400px] moe-truncate moe-text-primary moe-mb-0 rr-mask'}
            ellipsis={{
              tooltip: {
                align: 'center',
                side: 'top',
              },
            }}
          >
            {locationDetail.name}
            {locationDetail.twilioPhoneNumber
              ? `: ${business.formatPhoneNumber(locationDetail.twilioPhoneNumber)}`
              : ''}
          </Text>
        </div>
      </div>
    );
  }
  return null;
};
