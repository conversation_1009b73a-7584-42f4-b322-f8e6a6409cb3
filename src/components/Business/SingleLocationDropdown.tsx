import { MinorChevronDownOutlined, MinorLocationOutlined } from '@moego/icons-react';
import { Dropdown, type DropdownProps, Text, cn } from '@moego/ui';
import React, { memo, type ReactNode, useMemo } from 'react';
import { OverviewTestIds } from '../../config/testIds/overview';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { type RolePermissionSceneProps, useNewAccountStructure } from '../WithFeature/useNewAccountStructure';
import { ALL_LOCATIONS, ALL_LOCATION_NAME, type SingleLocationSelectorProps } from './SingleLocationSelector';

export interface Option {
  value: string;
  label: string;
  description?: string;
  phone?: string;
}

export interface SingleLocationDropdownProps
  extends Partial<Omit<SingleLocationSelectorProps, keyof RolePermissionSceneProps | 'classNames'>>,
    RolePermissionSceneProps {
  zIndex?: DropdownProps['zIndex'];
  isDisabled?: boolean;
  formatOptionTitle?: (option: Option) => ReactNode;
  formatSelectedOptionTitle?: (option: Option) => ReactNode;
  classNames?: {
    dropdown?: React.ComponentProps<typeof Dropdown>['classNames'];
    menu?: React.ComponentProps<typeof Dropdown.Menu>['classNames'];
    text?: string;
  };
}

export const SingleLocationDropdown = memo<SingleLocationDropdownProps>((props) => {
  const {
    allLocationsLabel = ALL_LOCATION_NAME,
    descriptionList,
    className,
    zIndex,
    scene,
    showAll = false,
    isDisabled,
    formatOptionTitle,
    formatSelectedOptionTitle,
    classNames,
  } = props;
  const { locationList, locationMap } = useNewAccountStructure(scene);
  const [value, setValue] = useControllableValue<string | undefined>(props);
  const businessOptions = useMemo(() => {
    const list = locationList
      .map<Option>((business) => {
        const description = descriptionList?.find((item) => item.value === business.id)?.description;
        return {
          value: business.id,
          label: business.name,
          description,
          phone: business.twilioPhoneNumber,
        };
      })
      .toJSON();
    if (showAll) {
      const description = descriptionList?.find((item) => item.value === ALL_LOCATIONS)?.description;
      list.unshift({
        value: ALL_LOCATIONS,
        label: allLocationsLabel as string,
        description,
      });
    }
    return list;
  }, [locationList, showAll]);

  const localBusinessName = useMemo(() => {
    if (value === ALL_LOCATIONS) {
      return allLocationsLabel;
    }
    if (value) {
      if (formatSelectedOptionTitle) {
        return formatSelectedOptionTitle(businessOptions.find((item) => item.value === value) as Option);
      }
      return locationMap.mustGetItem(value).name;
    }
    return 'Please select location';
  }, [value, locationMap]);

  return (
    <Dropdown
      classNames={classNames?.dropdown}
      defaultOpen={false}
      side="bottom"
      align="start"
      zIndex={zIndex}
      isDisabled={isDisabled}
    >
      <Dropdown.Trigger>
        <div
          className={cn(
            'moe-inline-flex moe-gap-xxs moe-items-center moe-cursor-pointer',
            isDisabled && 'moe-cursor-not-allowed',
            className,
          )}
          data-testid={OverviewTestIds.OverviewSwitchBusinessBtn}
        >
          <MinorLocationOutlined />
          <div className="moe-flex moe-flex-column moe-flex-1 moe-text-primary">
            <Text
              variant="regular-short"
              className={cn('moe-max-w-[200px] moe-truncate moe-text-primary moe-mb-0', classNames?.text)}
              ellipsis={{
                tooltip: {
                  align: 'center',
                  side: 'top',
                },
              }}
            >
              {localBusinessName}
            </Text>
          </div>
          <MinorChevronDownOutlined />
        </div>
      </Dropdown.Trigger>
      <Dropdown.Menu
        onAction={(key) => setValue(key as string)}
        selectedKeys={value ? [value] : undefined}
        className="moe-w-[220px]"
        classNames={classNames?.menu}
      >
        {Array.from(businessOptions).map((item) => {
          return (
            <Dropdown.MenuItem
              key={item.value}
              description={item.description}
              textValue={item.label}
              data-testid={OverviewTestIds.OverviewSwitchBusinessOption}
            >
              {formatOptionTitle ? formatOptionTitle(item) : item.label}
            </Dropdown.MenuItem>
          );
        })}
      </Dropdown.Menu>
    </Dropdown>
  );
});
