/**
 * @since 2023/12/13 4:14
 * <AUTHOR>
 * get default value for single selector
 */

import { useSelector } from 'amos';
import { useEffect, useState } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useNewAccountStructure } from '../../WithFeature/useNewAccountStructure';
import { type UseLocationValueOptions, getLocationValue } from './useLocationValue';

/**
 * get default value for single selector
 * @param options default value rules, edit value defaults
 * @param initializedCallback callback function，after the initial value is confirmed.
 */
export function useAutoFallbackLocationValue(options: UseLocationValueOptions) {
  const { preferred, value, scene } = options;
  const [locationId, setLocationId] = useState('');
  const { locationIds } = useNewAccountStructure(scene);
  const [currentBusiness] = useSelector(selectCurrentBusiness());

  useEffect(() => {
    const nextId = getLocationValue({
      preferred,
      value,
      locationIds,
      currentBusinessId: currentBusiness.id,
    });

    if (nextId !== locationId) {
      setLocationId(nextId);
    }
  }, [currentBusiness.id, locationIds, preferred, value]);

  return [locationId];
}
