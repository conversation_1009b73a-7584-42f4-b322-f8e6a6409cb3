/**
 * @since 2023/12/13 4:14
 * <AUTHOR>
 * get location value for single selector, with prefer default value
 */

import { useSelector } from 'amos';
import { useEffect, useRef, useState } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { ID_ANONYMOUS, ID_INVALID, isNormal } from '../../../store/utils/identifier';
import { type RolePermissionSceneProps, useNewAccountStructure } from '../../WithFeature/useNewAccountStructure';

export interface UseLocationValueOptions extends RolePermissionSceneProps {
  /**
   * first: first business
   * current: global current business
   * scoped: extended from scoped value
   */
  preferred: 'first' | 'current' | 'scoped';
  value?: string;
  /**
   * use for delay backfill case
   * - loading
   * - form is ready
   * - once modal visible
   */
  ready?: boolean;
}

interface GetNextValueOptions extends Pick<UseLocationValueOptions, 'preferred' | 'value'> {
  locationIds: string[];
  currentBusinessId: number;
}

export function getLocationValue(options: GetNextValueOptions) {
  const { preferred, value, locationIds, currentBusinessId } = options;
  let nextId = '';
  switch (preferred) {
    case 'first':
      const firstValue = locationIds[0];
      if (firstValue) {
        nextId = firstValue;
      }
      break;
    case 'current':
      const currentId = isNormal(currentBusinessId) ? currentBusinessId + '' : '';
      if (currentId) {
        nextId = currentId;
      }
      break;
    case 'scoped':
    default:
      if (strictValid(value)) {
        nextId = value;
      }
      break;
  }

  return nextId;
}

/**
 * incase some default value is invalid, for example, default business id is -1, to string is '-1'
 */
function strictValid(v?: string): v is Exclude<string, ''> {
  return !['', '' + ID_ANONYMOUS, '' + ID_INVALID, '' + ID_ANONYMOUS, 'null', 'undefined'].includes(v + '');
}

/**
 * get default value for single selector
 * @param options default value rules, edit value defaults
 * @param initializedCallback callback function，after the initial value is confirmed.
 */
export function useDefaultLocationValue(options: UseLocationValueOptions, initializedCallback?: (v: string) => void) {
  const { preferred, value, ready = true, scene } = options;
  const [locationId, setLocationId] = useState('');
  const initialized = useRef(false);
  const { locationIds } = useNewAccountStructure(scene);
  const [currentBusiness] = useSelector(selectCurrentBusiness());

  useEffect(() => {
    if (locationId) {
      return;
    }
    const nextId = getLocationValue({
      preferred,
      value,
      locationIds,
      currentBusinessId: currentBusiness.id,
    });
    if (nextId !== locationId) {
      setLocationId(nextId);
    }
  }, [currentBusiness.id, locationIds, preferred, value]);

  useEffect(() => {
    if (ready && !initialized.current && locationId) {
      initializedCallback?.(locationId);
      initialized.current = true;
    }
  }, [ready, locationId]);

  return [locationId, setLocationId] as ReturnType<typeof useState<string>>;
}
