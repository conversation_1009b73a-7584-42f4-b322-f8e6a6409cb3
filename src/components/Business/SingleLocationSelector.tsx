import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import React, { useMemo } from 'react';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { memoForwardRef } from '../../utils/react';
import { type RolePermissionSceneProps, useNewAccountStructure } from '../WithFeature/useNewAccountStructure';

export const ALL_LOCATIONS = 'all';

export type ALL_LOCATIONS_TYPE = 'all';

export const ALL_LOCATION_NAME = 'All businesses';

export type LocationSelectorValue = string | ALL_LOCATIONS_TYPE;

export interface LocationSelectorOptionDescription {
  value: LocationSelectorValue;
  description: string;
}
export interface SingleLocationSelectorProps
  extends Pick<SelectProps, 'menuPosition' | 'label' | 'isRequired' | 'isReadOnly' | 'isClearable' | 'isLoading'>,
    RolePermissionSceneProps {
  value?: LocationSelectorValue;
  onChange?: (value: LocationSelectorValue) => void;
  defaultValue?: LocationSelectorValue;
  className?: string;
  showAll?: boolean;
  allLocationsLabel?: React.ReactNode;
  customLabel?: (value: string, label: string) => React.ReactNode;
  descriptionList?: LocationSelectorOptionDescription[];
  isSearchable?: SelectProps['isSearchable'];
  classNames?: SelectProps['classNames'];
  menuPortalTarget?: SelectProps['menuPortalTarget'];
  isOpen?: SelectProps['isOpen'];
  onOpenChange?: SelectProps['onOpenChange'];
  placeholder?: SelectProps['placeholder'];
}

export const SingleLocationSelector = memoForwardRef(function SingleLocationSelector(
  props: SingleLocationSelectorProps,
  ref,
) {
  const {
    className = '',
    scene,
    showAll,
    allLocationsLabel = ALL_LOCATION_NAME,
    customLabel,
    descriptionList,
    ...restProps
  } = props;

  const [value, setValue] = useControllableValue<string | undefined>(props);
  const { locationList } = useNewAccountStructure(scene);
  const businessOptions = useMemo(() => {
    const list = locationList
      .map((business) => {
        const description = descriptionList?.find((item) => item.value === business.id)?.description;
        return {
          value: business.id,
          label: business.name,
          description,
        };
      })
      .toJSON();
    if (showAll) {
      const description = descriptionList?.find((item) => item.value === ALL_LOCATIONS)?.description;
      list.unshift({
        value: ALL_LOCATIONS,
        label: ALL_LOCATION_NAME,
        description,
      });
    }
    return list;
  }, [locationList, showAll, allLocationsLabel]);

  const handleRenderItem = useLatestCallback((option: { label: string; value: string }) => {
    const { label, value } = option;
    if (value === ALL_LOCATIONS) {
      return allLocationsLabel as string;
    }
    if (customLabel) {
      return customLabel(value, label) as string;
    }

    return label;
  });

  return (
    <Select
      ref={ref as unknown as any}
      {...restProps}
      className={className}
      value={value}
      options={businessOptions}
      onChange={setValue}
      formatOptionLabel={handleRenderItem}
    />
  );
});
