import { LeftOutlined, RightOutlined } from '@ant-design/icons';

import dayjs, { type Dayjs } from 'dayjs';
import React, { useEffect, useMemo, useRef } from 'react';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { isSameDay } from '../../utils/utils';
import { StyledAntdCalendar } from './CalendarInput.style';
import { cn } from '@moego/ui';

export interface CalendarInputProps {
  value?: Dayjs[];
  onChange?: (value: Dayjs[]) => void;
  disabledDate?: (date: Dayjs) => boolean;
  onlyFuture?: boolean;
  defaultValue?: Dayjs;
  isDisabled?: boolean;
}

export const CalendarInput: React.FC<CalendarInputProps> = (props) => {
  const { onlyFuture = true, defaultValue, isDisabled } = props;
  const [state, setState] = useControllableValue<Dayjs[] | undefined>(props, {
    defaultValue: [],
  });

  const allDisabled = useRef(true);
  const changeFn = useRef<((d: Dayjs) => void) | null>(null);
  const isOnPanelChangeRef = useRef(false);
  const handleCalendarSelect = useLatestCallback((date: Dayjs) => {
    if (isOnPanelChangeRef.current) return;
    const oldState = state || [];
    if (oldState.some((d) => isSameDay(d, date))) {
      setState([...oldState.filter((d) => !isSameDay(d, date))]);
      return;
    } else if (oldState.length >= 100) {
      return;
    }
    setState([...oldState, date]);
  });

  const disabledDate = useMemo(() => {
    if (onlyFuture) {
      return (d: Dayjs) => d < dayjs().startOf('day');
    }
    return props.disabledDate || (() => true);
  }, [onlyFuture, props]);
  const wrapDisabledDate = useLatestCallback((day: Dayjs) => {
    const disabled = disabledDate(day);

    if (!disabled && allDisabled.current) {
      allDisabled.current = false;
    }
    return disabled;
  });

  useEffect(() => {
    // 如果所有日期都被禁用，则切换到下个月
    if (allDisabled.current) {
      changeFn.current?.(dayjs().set('month', dayjs().month() + 1));
    }
  }, []);

  useEffect(() => {
    // 只能选择当天之后的日期
    onlyFuture && setState((oldState) => oldState?.filter((d) => d >= dayjs().startOf('day')) || []);
  }, [onlyFuture]);

  return (
    <div
      className={cn(
        'moe-border-solid moe-border-[1px] moe-border-collapse moe-border-[#dee1e5] moe-rounded-[8px]',
        'moe-px-[32px] moe-py-[24px] moe-bg-white moe-select-none',
      )}
    >
      <StyledAntdCalendar
        key={defaultValue?.format(DATE_FORMAT_EXCHANGE)}
        fullscreen={false}
        {...(defaultValue && { defaultValue })}
        headerRender={({ value, onChange }) => {
          const currentMonth = value.format('MMMM YYYY');
          const changeMonth = (num: number) => {
            const month = value.month() + num;
            onChange(value.set('month', month));
          };
          if (!changeFn.current) {
            changeFn.current = onChange;
          }
          return (
            <div className="moe-mb-[24px]">
              <div className="moe-flex moe-justify-between moe-items-center">
                <LeftOutlined
                  width={16}
                  className={cn({ '!moe-text-disabled': isDisabled })}
                  onClick={() => {
                    if (isDisabled) return;
                    changeMonth(-1);
                  }}
                />
                <span className="moe-text-sm moe-font-[700]">{currentMonth}</span>
                <RightOutlined
                  width={16}
                  className={cn({ '!moe-text-disabled': isDisabled })}
                  onClick={() => {
                    if (isDisabled) return;
                    changeMonth(1);
                  }}
                />
              </div>
            </div>
          );
        }}
        onSelect={handleCalendarSelect}
        // hack: panel 切换时也会触发一次 onSelect，这里在 panel 触发变化时打个标记，让 onSelect 忽略这种情况
        onPanelChange={() => {
          isOnPanelChangeRef.current = true;
          setTimeout(() => {
            isOnPanelChangeRef.current = false;
          }, 0);
        }}
        dateCellRender={(date) => {
          // 不支持选择开始时间前的日期
          // antd 的 disabledDate 有 bug，默认会选中今天，且无法直接去掉，所以覆盖 dateCellRender
          const cannotSelect = disabledDate(date);
          if (cannotSelect || isDisabled) {
            return (
              <div
                className={cn(
                  'moe-flex moe-items-center moe-justify-center moe-rounded-full',
                  'moe-w-[24px] moe-h-[24px] moe-text-xs',
                  'moe-absolute moe-left-0 moe-top-0 moe-bg-[#fff] moe-text-[#a8a8a8]',
                )}
              >
                {date.format('DD')}
              </div>
            );
          }

          const isSelected = state?.some((d) => isSameDay(d, date));
          if (!isSelected) return undefined;
          return (
            <div
              className={cn(
                'moe-flex moe-items-center moe-justify-center moe-rounded-full',
                'moe-w-[24px] moe-h-[24px] moe-text-xs',
                'moe-absolute moe-left-0 moe-top-0 moe-bg-brand-bold moe-text-[#fff]',
              )}
            >
              {date.format('DD')}
            </div>
          );
        }}
        disabledDate={wrapDisabledDate}
      />
    </div>
  );
};
