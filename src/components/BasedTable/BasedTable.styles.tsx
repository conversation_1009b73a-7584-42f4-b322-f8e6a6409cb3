import styled from 'styled-components';

export const BasedTableView = styled.div`
  overflow-x: auto;
  
  table {
    width: 100%;

    .table-head,
    .table-row,
    .table-expanded-row {
      th, td {
        border-bottom: 1px solid #e6e6e6;
      }
    }

    /** 
     * 单独实现 table 头的底部分割线。当 table 头变成 sticky 时，th 的 border-bottom 会消失，因为 border-collapse: collapse;
     * 所以这里的边框和下一行的边框会共享。
    */
    thead .table-head th {
      border: 0;
      padding: 0;
      position: relative;
      z-index: 1;

      > .table-divider {
        height: 1px;
        width: 100%;
        border-bottom: 1px solid #e6e6e6;
        position: absolute;
        bottom: 0;
      }

      > .table-th-container {
        padding: 8px;
      }
    }

    .table-expand-relative-row:hover + .table-expanded-row {
      background-color: var(--moe-color-bg-neutral-sunken-0);
    }

    .table-expand-relative-row > td {
      padding-top: 16px;
      padding-bottom: 12px;
    }

    .table-expanded-row {
      padding-left: 16px;
      td {
        padding-top: 0px;
        padding-bottom: 16px;
      }
    }

    .table-row > td {
      padding-top: 16px;
      padding-bottom: 16px;
    }

    th,
    td {
      padding: 8px 8px;
      vertical-align: top;

      &:first-child {
        padding-left: 16px;
        min-width: 160px;
      }

      &:last-child {
        padding-right: 0;
      }
    }
  }
`;
