import { Checkbox, type ColumnSort, type ExpandedStateList, type SortDirection, cn } from '@moego/ui';
import { type VirtualItem, useVirtualizer } from '@tanstack/react-virtual';
import { isFunction, isNil } from 'lodash';
import React, { type ForwardedRef, useEffect, useMemo, useRef, useState } from 'react';
import { Condition } from '../../components/Condition';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { memoForwardRef, mergeRefs } from '../../utils/react';
import { BasedTableView } from './BasedTable.styles';

import {
  commonStyle,
  ensureRowState,
  flatTableRowShow,
  getRootTable,
  handleToggleTableGroupSelected,
  handleToggleTableRowSelected,
  rowStateMapToArray,
} from './BasedTable.utils';
import { BasedTableRow } from './BasedTableRow';
import { HeaderCell } from './components/HeaderCell';
import { DIRECTION } from './components/SortingIcon';
import { type BasedTableProps, type TableRowData } from './types';

export const BasedTable = memoForwardRef(function BasedTable<T>(
  props: BasedTableProps<T>,
  forwardedRef: ForwardedRef<HTMLDivElement>,
) {
  const {
    columns,
    rowKey,
    expandable,
    expandedRowRender,
    className = '',
    onRowClick,
    data = [],
    noDataArea: noDataLine,
    rowSelection,
    groupData,
    rowExpansion,
    stickyHeader,
    isVirtual,
    containerClassName,
    scrollYContainer,
    isLoading,
    onSortingChange,
  } = props;
  const [expandedRowKeys, setExpandedRowKeys] = React.useState<ExpandedStateList>({});
  const [sorting, setSorting] = useState<ColumnSort>();
  const handleToggleExpandedRowKeys = useLatestCallback((key: string) => {
    setExpandedRowKeys({ key: !expandedRowKeys[key] });
  });

  useEffect(() => {
    setExpandedRowKeys((rowExpansion?.expandedRowIds as ExpandedStateList) || {});
  }, [expandable, expandedRowRender, data, rowKey]);

  const isSelectable = !isNil(rowSelection);
  const selectedRowIdMap = useMemo(() => {
    return ensureRowState(rowSelection?.selectedRowIds);
  }, [rowSelection?.selectedRowIds]);

  const selectedGroupIdMap = useMemo(() => {
    return ensureRowState(rowSelection?.selectedGroupIds);
  }, [rowSelection?.selectedGroupIds]);

  const selectedAllGroupIdMap = useMemo(() => {
    return ensureRowState(rowSelection?.selectedAllGroupIds);
  }, [rowSelection?.selectedAllGroupIds]);

  const tableRoot = useMemo(() => {
    return getRootTable(data, rowKey, groupData, rowExpansion, rowSelection);
  }, [data, groupData, rowExpansion]);

  const tableRow = useMemo(() => {
    const cache: TableRowData<T>[] = [];
    flatTableRowShow(tableRoot.children, cache);
    return cache;
  }, [tableRoot]);

  const isGroupTable = rowExpansion?.type === 'group';

  const stickyHeaderClass = stickyHeader ? '!moe-sticky moe-top-0 moe-bg-white' : '';

  const parent = useRef<HTMLDivElement>(null);
  const virtualizer = useVirtualizer({
    count: tableRow.length,
    overscan: 5,
    getScrollElement: () => scrollYContainer ?? parent.current,
    estimateSize: () => 70,
  });
  const virtualRows = virtualizer.getVirtualItems();

  const tableTotalSize = virtualizer.getTotalSize();

  const enableVirtual = isVirtual && tableRow.length > 60;

  const baseTableStyle = useMemo<React.CSSProperties | undefined>(() => {
    if (!enableVirtual) {
      return;
    }
    return {
      height: `${tableTotalSize}px`,
      position: 'relative',
      overflow: 'visible',
    };
  }, [tableTotalSize, enableVirtual]);

  const currentScrollPosition = virtualRows[0]?.start ?? 0;

  const TableStyle = useMemo<React.CSSProperties | undefined>(() => {
    if (!enableVirtual) {
      return;
    }
    return {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      transform: `translateY(${currentScrollPosition}px)`,
    };
  }, [currentScrollPosition, enableVirtual]);

  const tableItemData = enableVirtual ? virtualRows : tableRow;

  if (tableRow.length > 0) {
    return (
      <div ref={mergeRefs(parent, forwardedRef)} className={containerClassName}>
        <BasedTableView className={className} style={baseTableStyle}>
          <table style={TableStyle}>
            <thead>
              <tr className="table-head">
                <Condition if={isGroupTable}>
                  <th className={stickyHeaderClass} style={commonStyle}>
                    <div className="table-divider"></div>
                    <div className="table-th-container"></div>
                  </th>
                </Condition>
                <Condition if={isSelectable}>
                  <th style={commonStyle} className={cn(stickyHeaderClass, 'moe-select-none')}>
                    <div className="table-divider"></div>

                    <div className="moe-flex moe-justify-center table-th-container">
                      <Checkbox
                        isIndeterminate={tableRoot.isIndeterminate}
                        isSelected={tableRoot.isSelected}
                        onChange={(value) => {
                          const newIdMap = handleToggleTableRowSelected(
                            tableRoot,
                            value,
                            value ? selectedRowIdMap : {},
                          );
                          const newGroupIdMap = handleToggleTableGroupSelected(
                            tableRoot,
                            value,
                            value ? selectedAllGroupIdMap : {},
                          );

                          rowSelection?.onChange(newIdMap, {
                            selectionType: 'all',
                            isSelected: value,
                            selectedList: rowStateMapToArray(newIdMap),
                            unselectedList: [],
                            rowId: tableRoot.id,
                            selectedGroupIds: newGroupIdMap,
                            selectedAllGroupIds: newGroupIdMap,
                          });
                        }}
                      />
                    </div>
                  </th>
                </Condition>
                {columns.map((column, index) => {
                  const { id } = column;
                  const isCurrentSorting = sorting?.id === id;
                  const direction = isCurrentSorting ? (sorting?.desc ? DIRECTION.DESC : DIRECTION.ASC) : undefined;

                  return (
                    <HeaderCell
                      key={index}
                      column={column}
                      index={index}
                      stickyHeaderClass={stickyHeaderClass}
                      direction={direction as SortDirection}
                      onSort={(id) => {
                        const desc = id === sorting?.id ? !sorting?.desc : false;
                        setSorting({ id, desc });
                        onSortingChange?.({ id, desc });
                      }}
                    />
                  );
                })}
              </tr>
            </thead>

            <tbody>
              {tableItemData.map((row, index) => {
                const rowIndex = (row as VirtualItem)?.index ?? index;
                const item = tableRow[rowIndex];
                const original = item.type === 'row' ? item.original : undefined;
                const isExpandable = original ? !!expandable?.(original) : false;
                const key = original ? (rowKey ? rowKey(original) : `${rowIndex}`) : item.id;
                const isExpanded = expandedRowKeys[key];

                return (
                  <BasedTableRow<T>
                    key={rowIndex}
                    rowKey={key}
                    dataList={tableRow}
                    isExpanded={isExpanded}
                    data={item}
                    columns={columns}
                    isExpandable={isExpandable}
                    hasExpandableColum={isGroupTable}
                    handleToggleExpandedRowKeys={handleToggleExpandedRowKeys}
                    expandedRowRender={expandedRowRender}
                    onClick={onRowClick}
                    isSelectable={isSelectable}
                    virtualizer={enableVirtual ? virtualizer : undefined}
                    virtualIndex={rowIndex}
                    rowExpansion={rowExpansion}
                    isLoading={isLoading}
                    handleToggleSelectRow={(key, value) => {
                      const newRowIdMap = handleToggleTableRowSelected(
                        item,
                        value,
                        selectedRowIdMap,
                        rowSelection?.selectGroupRowMap?.[key], // 收起的列可能拿不到下钻的数据，这里将曾经存储的数据作为补充数据传入
                      );
                      let newGroupIdMap = handleToggleTableGroupSelected(item, value, selectedGroupIdMap);
                      let newSelectedAllGroupIdMap = handleToggleTableGroupSelected(item, value, selectedAllGroupIdMap);

                      if (item.type === 'row' && !value) {
                        const parentGroup = tableRow.find((row) => row.id === item.parentId);
                        newSelectedAllGroupIdMap = parentGroup
                          ? handleToggleTableGroupSelected(parentGroup, value, newSelectedAllGroupIdMap)
                          : newSelectedAllGroupIdMap;

                        const selectedRows = parentGroup?.children.filter((child) => child.isSelected);
                        // 判断当前如果为最后一个曾选中的 row，则取消 selected group 状态
                        if (
                          selectedRows?.length === 1 &&
                          selectedRows.find((row) => row.id === item.id) &&
                          parentGroup
                        ) {
                          newGroupIdMap = handleToggleTableGroupSelected(parentGroup, value, newGroupIdMap);
                        }
                      }

                      rowSelection?.onChange(newRowIdMap, {
                        selectionType: 'row',
                        isSelected: value,
                        selectedList: rowStateMapToArray(newRowIdMap),
                        unselectedList: [],
                        rowId: key,
                        selectedGroupIds: newGroupIdMap,
                        selectedAllGroupIds: newSelectedAllGroupIdMap,
                      });
                    }}
                  />
                );
              })}
            </tbody>
          </table>
        </BasedTableView>
      </div>
    );
  }

  return (
    <div
      className="moe-rounded-l moe-bg-neutral-sunken-0 moe-py-[24px] moe-flex moe-items-center moe-justify-center"
      ref={forwardedRef}
    >
      {isFunction(noDataLine) ? noDataLine() : noDataLine}
    </div>
  );
}) as <T>(props: BasedTableProps<T> & { ref?: ForwardedRef<HTMLDivElement> }) => React.JSX.Element;
