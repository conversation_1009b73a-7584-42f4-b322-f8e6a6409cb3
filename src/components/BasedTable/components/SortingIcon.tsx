import { type SortDirection } from '@moego/ui';
import React from 'react';

export interface SortingIconProps {
  direction?: SortDirection | false;
}

export const DIRECTION = {
  ASC: 'asc',
  DESC: 'desc',
};

export const SortingIcon: React.FC<SortingIconProps> = (props) => {
  const { direction } = props;
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="moe-min-w-[20px]"
    >
      <path
        d="M13.0979 9.00025H6.90194C6.15094 9.00025 5.72994 8.24625 6.19394 7.73225L9.29194 4.29925C9.65294 3.90025 10.3469 3.90025 10.7079 4.29925L13.8059 7.73225C14.2699 8.24625 13.8489 9.00025 13.0979 9.00025Z"
        className={direction === DIRECTION.ASC ? 'moe-fill-brand' : 'moe-fill-gray-400'}
      />
      <path
        d="M6.90194 11.0002H13.0979C13.8489 11.0002 14.2699 11.7542 13.8059 12.2682L10.7079 15.7012C10.3469 16.1002 9.65294 16.1002 9.29194 15.7012L6.19394 12.2682C5.72994 11.7542 6.15094 11.0002 6.90194 11.0002Z"
        className={direction === DIRECTION.DESC ? 'moe-fill-brand' : 'moe-fill-gray-400'}
      />
    </svg>
  );
};
