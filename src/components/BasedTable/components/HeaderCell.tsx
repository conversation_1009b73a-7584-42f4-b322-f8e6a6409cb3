import { type SortDirection, cn } from '@moego/ui';
import React from 'react';
import { Condition } from '../../Condition';
import { type BasedTableColumnProps } from '../types';
import { SortingIcon } from './SortingIcon';

interface HeaderCellProps<T> {
  column: BasedTableColumnProps<T>;
  stickyHeaderClass: string;
  index: number;
  direction?: SortDirection;
  onSort?: (id: string) => void;
}

export const HeaderCell = <T,>(props: HeaderCellProps<T>) => {
  const { column, stickyHeaderClass, index, direction, onSort } = props;

  const handleSort = () => {
    if (column.enableSorting && column.id) {
      onSort?.(column.id!);
    }
  };

  return (
    <th
      style={{
        width: column.width,
        minWidth: column.minWidth ?? column.width,
        maxWidth: column.maxWidth,
      }}
      className={cn(
        'moe-text-[14px] moe-leading-[18px] moe-font-bold moe-text-secondary moe-whitespace-nowrap',
        stickyHeaderClass,
      )}
    >
      <div className="table-divider"></div>

      <div
        className={cn('table-th-container !moe-px-spacing-s moe-flex moe-items-center', {
          'moe-cursor-pointer': column.enableSorting,
        })}
        onClick={handleSort}
      >
        {column.renderHeader ? column.renderHeader(column, index) : column.title}
        <Condition if={column.enableSorting && column.id}>
          <div className="moe-ml-8px-100 moe-h-[20px] moe-w-[20px]">
            <SortingIcon direction={direction} />
          </div>
        </Condition>
      </div>
    </th>
  );
};
