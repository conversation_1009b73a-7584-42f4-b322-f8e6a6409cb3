import { cloneDeep } from 'lodash';
import { type GroupExpansion, type TableGroupData, type TableRowData, type TableRowSelection } from './types';

/**
 * 若为 rowIds 则转换为 rowState
 */
export function ensureRowState(rowStateOrIds?: Record<string, boolean> | string[]) {
  if (!rowStateOrIds) {
    return {};
  }

  if (Array.isArray(rowStateOrIds)) {
    return rowStateOrIds.reduce(
      (acc, id) => {
        acc[id] = true;
        return acc;
      },
      {} as Record<string, boolean>,
    );
  }

  return rowStateOrIds;
}

export function rowStateMapToArray(rowState?: Record<string, boolean>): string[] {
  return Object.entries(rowState || {}).reduce((acc, [id, isSelected]) => {
    if (isSelected) {
      acc.push(id);
    }
    return acc;
  }, [] as string[]);
}

export function setNewRowState(rowState: Record<string, boolean> | undefined, newValue: Record<string, boolean>) {
  const newState = cloneDeep(rowState || {});
  Object.keys(newValue).forEach((key) => {
    const value = !!newValue[key];
    if (value) {
      newState[key] = value;
    } else {
      delete newState[key];
    }
  });
  return newState;
}

function isAllChildrenSelected<T>(children: TableRowData<T>[]) {
  const haveChildrenSelected = children.some((child) => child.isSelected);
  const haveChildrenUnSelected = children.some((child) => !child.isSelected);
  const haveChildrenIndeterminate = children.some((child) => child.isIndeterminate);
  const isSelected =
    children.length > 0 && haveChildrenSelected && !haveChildrenUnSelected && !haveChildrenIndeterminate;
  const isIndeterminate =
    (children.length > 0 && haveChildrenSelected && haveChildrenUnSelected) || haveChildrenIndeterminate;

  return {
    isSelected,
    isIndeterminate,
  };
}

/**
 * 将 table 的数据结构统一转化成 tree 结构。并计算 row 的状态（checkbox 是否选中，是否展开）
 *
 * group 的 checkbox 状态是根据 row 的状态动态计算。
 *
 */
export function getRootTable<T>(
  tableData: T[],
  getKey: ((row: T) => string) | undefined,
  groupData?: TableGroupData<T>[] | undefined,
  rowExpansion?: GroupExpansion<T> | undefined,
  rowSelection?: TableRowSelection<T> | undefined,
): TableRowData<T> {
  const expandedRowIds = ensureRowState(rowExpansion?.expandedRowIds);
  const selectedRowIds = ensureRowState(rowSelection?.selectedRowIds);
  const selectedGroupIds = ensureRowState(rowSelection?.selectedGroupIds);
  const selectedAllGroupIds = ensureRowState(rowSelection?.selectedAllGroupIds);
  let children: TableRowData<T>[] = [];
  if (rowExpansion?.type === 'group') {
    children =
      groupData?.map<TableRowData<T>>((data) => {
        const key = data.groupId;
        const groupChildren = data.items.map<TableRowData<T>>((item) => {
          const childKey = getKey?.(item) || '';

          return {
            type: 'row',
            isIndeterminate: false,
            isSelected: !!selectedRowIds[childKey],
            isExpanded: !!expandedRowIds[childKey],
            id: childKey,
            groupValue: '',
            original: item,
            children: [],
            parentId: key,
          };
        });

        let { isIndeterminate, isSelected } = isAllChildrenSelected(groupChildren);

        if (selectedAllGroupIds[key]) {
          isSelected = true;
          isIndeterminate = false;
        } else if (selectedGroupIds[key] && groupChildren?.length === 0) {
          isSelected = false;
          isIndeterminate = true;
        }

        return {
          type: 'group',
          id: key,
          groupValue: data.groupValue,
          children: groupChildren,
          isSelected,
          isIndeterminate,
          isExpanded: expandedRowIds[key],
        };
      }) || [];
  } else {
    children = tableData.map<TableRowData<T>>((data) => {
      const key = getKey?.(data) || '';
      return {
        type: 'row',
        isIndeterminate: false,
        isSelected: !!selectedRowIds[key],
        isExpanded: !!expandedRowIds[key],
        id: key,
        groupValue: '',
        original: data,
        children: [],
      };
    });
  }

  const { isIndeterminate, isSelected } = isAllChildrenSelected(children || []);

  const root: TableRowData<T> = {
    type: 'root',
    id: 'root',
    groupValue: '',
    children: children || [],
    isSelected,
    isIndeterminate,
    isExpanded: true,
  };

  return root;
}

export function flatTableRowShow<T>(root: TableRowData<T>[], row: TableRowData<T>[]) {
  root.forEach((data) => {
    row.push(data);
    if (data.isExpanded) {
      flatTableRowShow(data.children, row);
    }
  });
}

export function getAllTableRowId<T>(root: TableRowData<T>) {
  let res: string[] = [];
  if (root.type === 'row') {
    res.push(root.id);
  } else {
    root.children.forEach((child) => {
      res = res.concat(getAllTableRowId(child));
    });
  }
  return res;
}

export function getAllTableGroupId<T>(root: TableRowData<T>) {
  let res: string[] = [];
  if (root.type === 'group') {
    res.push(root.id);
  } else if (root.type === 'root') {
    root.children.forEach((child) => {
      res = res.concat(getAllTableGroupId(child));
    });
  }
  return res;
}

export function handleToggleTableRowSelected<T>(
  row: TableRowData<T>,
  value: boolean,
  selectedIdMap: Record<string, boolean>,
  supplementedId: string[] = [],
) {
  const allId = [...getAllTableRowId(row), ...supplementedId];
  const newIdMap: Record<string, boolean> = {};

  allId.forEach((id) => {
    newIdMap[id] = value;
  });

  return setNewRowState(selectedIdMap, newIdMap);
}

export function handleToggleTableGroupSelected<T>(
  row: TableRowData<T>,
  value: boolean,
  selectedIdMap: Record<string, boolean>,
) {
  const allId = getAllTableGroupId(row);
  const newIdMap: Record<string, boolean> = {};

  allId.forEach((id) => {
    newIdMap[id] = value;
  });

  return setNewRowState(selectedIdMap, newIdMap);
}

export const commonStyle: React.CSSProperties = {
  width: 52,
  minWidth: 0,
  verticalAlign: 'middle',
};
