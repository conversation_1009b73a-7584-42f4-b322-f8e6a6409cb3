import { getRootTable } from './BasedTable.utils';

describe('base table', () => {
  it('convert data to inner structure', () => {
    const structure = getRootTable([{ key: '1', name: 'hello' }], (row) => row.key, undefined, undefined, undefined);

    expect(structure).toEqual({
      type: 'root',
      id: 'root',
      groupValue: '',
      children: [
        {
          type: 'row',
          isIndeterminate: false,
          isSelected: false,
          isExpanded: false,
          id: '1',
          groupValue: '',
          original: { key: '1', name: 'hello' },
          children: [],
        },
      ],
      isSelected: false,
      isIndeterminate: false,
      isExpanded: true,
    });
  });

  it('convert group data to inner structure', () => {
    const structure = getRootTable(
      [],
      (row) => row.key,
      [
        {
          groupId: '1',
          groupValue: 'test',
          items: [
            {
              key: '2',
              value: 'hello',
            },
          ],
        },
      ],
      {
        type: 'group',
      },
      { selectedRowIds: {}, onChange: () => {} },
    );

    expect(structure).toEqual({
      type: 'root',
      id: 'root',
      groupValue: '',
      children: [
        {
          type: 'group',
          id: '1',
          groupValue: 'test',
          children: [
            {
              type: 'row',
              parentId: '1',
              isIndeterminate: false,
              isSelected: false,
              isExpanded: false,
              id: '2',
              groupValue: '',
              original: { key: '2', value: 'hello' },
              children: [],
            },
          ],
          isExpanded: undefined,
          isSelected: false,
          isIndeterminate: false,
        },
      ],
      isSelected: false,
      isIndeterminate: false,
      isExpanded: true,
    });
  });

  it('group data should be selected', () => {
    const structure = getRootTable(
      [],
      (row) => row.key,
      [
        {
          groupId: '1',
          groupValue: 'test',
          items: [
            {
              key: '2',
              value: 'hello',
            },
          ],
        },
      ],
      {
        type: 'group',
      },
      { selectedRowIds: { '2': true }, onChange: () => {} },
    );

    expect(structure).toEqual({
      type: 'root',
      id: 'root',
      groupValue: '',
      children: [
        {
          type: 'group',
          id: '1',
          groupValue: 'test',
          children: [
            {
              type: 'row',
              parentId: '1',
              isIndeterminate: false,
              isSelected: true,
              isExpanded: false,
              id: '2',
              groupValue: '',
              original: { key: '2', value: 'hello' },
              children: [],
            },
          ],
          isExpanded: undefined,
          isSelected: true,
          isIndeterminate: false,
        },
      ],
      isSelected: true,
      isIndeterminate: false,
      isExpanded: true,
    });
  });

  it('group data should be indeterminate', () => {
    const structure = getRootTable(
      [],
      (row) => row.key,
      [
        {
          groupId: '1',
          groupValue: 'test',
          items: [
            {
              key: '2',
              value: 'hello',
            },
            {
              key: '3',
              value: 'world',
            },
          ],
        },
      ],
      {
        type: 'group',
      },
      { selectedRowIds: { '2': true }, onChange: () => {} },
    );

    expect(structure).toEqual({
      type: 'root',
      id: 'root',
      groupValue: '',
      children: [
        {
          type: 'group',
          id: '1',
          groupValue: 'test',
          children: [
            {
              type: 'row',
              parentId: '1',
              isIndeterminate: false,
              isSelected: true,
              isExpanded: false,
              id: '2',
              groupValue: '',
              original: { key: '2', value: 'hello' },
              children: [],
            },
            {
              type: 'row',
              parentId: '1',
              isIndeterminate: false,
              isSelected: false,
              isExpanded: false,
              id: '3',
              groupValue: '',
              original: { key: '3', value: 'world' },
              children: [],
            },
          ],
          isExpanded: undefined,
          isSelected: false,
          isIndeterminate: true,
        },
      ],
      isSelected: false,
      isIndeterminate: true,
      isExpanded: true,
    });
  });

  it('group data should be indeterminate for selected data and unSelected data', () => {
    const structure = getRootTable(
      [],
      (row) => row.key,
      [
        {
          groupId: '1',
          groupValue: 'test',
          items: [
            {
              key: '2',
              value: 'hello',
            },
            {
              key: '3',
              value: 'world',
            },
          ],
        },
        {
          groupId: '4',
          groupValue: 'test4',
          items: [
            {
              key: '5',
              value: 'hello5',
            },
            {
              key: '6',
              value: 'world6',
            },
          ],
        },
      ],
      {
        type: 'group',
      },
      { selectedRowIds: { '2': true, '3': true }, onChange: () => {} },
    );

    expect(structure).toEqual({
      type: 'root',
      id: 'root',
      groupValue: '',
      children: [
        {
          type: 'group',
          id: '1',
          groupValue: 'test',
          children: [
            {
              type: 'row',
              parentId: '1',
              isIndeterminate: false,
              isSelected: true,
              isExpanded: false,
              id: '2',
              groupValue: '',
              original: { key: '2', value: 'hello' },
              children: [],
            },
            {
              type: 'row',
              parentId: '1',
              isIndeterminate: false,
              isSelected: true,
              isExpanded: false,
              id: '3',
              groupValue: '',
              original: { key: '3', value: 'world' },
              children: [],
            },
          ],
          isExpanded: undefined,
          isSelected: true,
          isIndeterminate: false,
        },
        {
          type: 'group',
          id: '4',
          groupValue: 'test4',
          children: [
            {
              type: 'row',
              parentId: '4',
              isIndeterminate: false,
              isSelected: false,
              isExpanded: false,
              id: '5',
              groupValue: '',
              original: { key: '5', value: 'hello5' },
              children: [],
            },
            {
              type: 'row',
              parentId: '4',
              isIndeterminate: false,
              isSelected: false,
              isExpanded: false,
              id: '6',
              groupValue: '',
              original: { key: '6', value: 'world6' },
              children: [],
            },
          ],
          isExpanded: undefined,
          isSelected: false,
          isIndeterminate: false,
        },
      ],
      isSelected: false,
      isIndeterminate: true,
      isExpanded: true,
    });
  });
});
