import { MinorChevronDownOutlined } from '@moego/icons-react';
import { Text, cn } from '@moego/ui';
import { Collapse } from 'antd';
import { isUndefined } from 'lodash';
import React, { memo } from 'react';
import SvgIconArrowSvg from '../../assets/svg/icon-arrow.svg';
import { usePropsState } from '../../utils/hooks/usePropsState';
import { SvgIcon } from '../Icon/Icon';
import { CollapseSimpleView, FixedWrapper, PanelHeader } from './CollapseSimple.style';

export interface CollapseSimpleProps {
  title?: React.ReactNode;
  children: React.ReactNode;
  rotate?: number;
  expand?: boolean;
  defaultExpand?: boolean;
  useNewIcon?: boolean;
  iconClassName?: string;
  // Panel 内放 Form.Item 时需要
  forceRenderPanel?: boolean;
  className?: string;
  headerClass?: string;
  classNames?: {
    title?: string;
    header?: string;
    headerWrapper?: string;
    headerContent?: string;
    collapse?: string;
  };
  extra?: React.ReactNode;
  style?: React.CSSProperties;
  customHeader?: (props: {
    isExpand: boolean;
    setIsExpand: React.Dispatch<React.SetStateAction<boolean | undefined>>;
  }) => React.ReactNode;
  onChange?: (expand: boolean) => void;
}

const { Panel } = Collapse;

export const CollapseSimple = memo<CollapseSimpleProps>(
  ({
    title,
    children,
    rotate = 180,
    expand,
    defaultExpand = false,
    forceRenderPanel = false,
    className,
    headerClass,
    classNames,
    extra,
    style,
    customHeader,
    onChange,
    useNewIcon = false,
    iconClassName,
  }) => {
    const [isExpand, setIsExpand] = usePropsState(expand);

    const isActive = isUndefined(isExpand) ? defaultExpand : isExpand;

    const renderHeader = () => {
      return (
        <PanelHeader className={classNames?.headerWrapper}>
          <FixedWrapper
            className={cn(headerClass || '', classNames?.header)}
            onClick={() => {
              setIsExpand(!isActive);
              onChange?.(!isActive);
            }}
          >
            {useNewIcon ? (
              <>
                <Text variant="small" className={classNames?.headerContent}>
                  {title}
                </Text>
                <MinorChevronDownOutlined
                  rotate={isActive ? rotate : 0}
                  className={cn('moe-text-icon-primary', iconClassName)}
                />
              </>
            ) : (
              <>
                <span className={cn('collapse-title', classNames?.title)}>{title}</span>
                <SvgIcon
                  src={SvgIconArrowSvg}
                  size={11}
                  style={{ transform: `rotate(${isActive ? rotate : 0}deg)` }}
                  className={cn('accordion-space-arrow', iconClassName)}
                />
              </>
            )}
          </FixedWrapper>
          {extra}
        </PanelHeader>
      );
    };

    return (
      <CollapseSimpleView style={style} className={cn(className, { active: isActive })}>
        <Collapse bordered={false} activeKey={isActive ? ['1'] : void 0} className={cn(classNames?.collapse)}>
          <Panel
            header={customHeader ? customHeader({ isExpand: isActive, setIsExpand }) : renderHeader()}
            key="1"
            showArrow={false}
            forceRender={forceRenderPanel}
          >
            {children}
          </Panel>
        </Collapse>
      </CollapseSimpleView>
    );
  },
);
