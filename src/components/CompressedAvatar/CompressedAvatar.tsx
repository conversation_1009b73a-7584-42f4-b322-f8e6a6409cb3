import {
  Avatar,
  type AvatarBusinessProps,
  type AvatarPetProps,
  type AvatarProps,
  type AvatarStaffProps,
  type AvatarVanProps,
} from '@moego/ui';
import { type AvatarClientProps } from '@moego/ui/dist/esm/components/Avatar/AvatarClient';
import React from 'react';
import { forwardRef } from 'react';
import { ImgSizeMap, getCompressedImgUrl } from '../../utils/getCompressedImgUrl';

type CompressedAvatarProps = {
  compressedSize?: keyof typeof ImgSizeMap;
};

const CompressedAvatarBase = forwardRef<HTMLDivElement, AvatarProps & CompressedAvatarProps>(
  ({ src, compressedSize, ...props }, forwardedRef) => (
    <Avatar src={getCompressedImgUrl(src || '', ImgSizeMap[compressedSize || 'W_128'])} ref={forwardedRef} {...props} />
  ),
);

export const CompressedAvatarClient = forwardRef<HTMLDivElement, AvatarClientProps & CompressedAvatarProps>(
  ({ src, compressedSize, ...props }, forwardedRef) => (
    <Avatar.Client
      src={getCompressedImgUrl(src || '', ImgSizeMap[compressedSize || 'W_128'])}
      ref={forwardedRef}
      {...props}
    />
  ),
);

export const CompressedAvatarBusiness = forwardRef<HTMLDivElement, AvatarBusinessProps & CompressedAvatarProps>(
  ({ src, compressedSize, ...props }, forwardedRef) => (
    <Avatar.Business
      src={getCompressedImgUrl(src || '', ImgSizeMap[compressedSize || 'W_128'])}
      ref={forwardedRef}
      {...props}
    />
  ),
);

export const CompressedAvatarPet = forwardRef<HTMLDivElement, AvatarPetProps & CompressedAvatarProps>(
  ({ src, compressedSize, ...props }, forwardedRef) => (
    <Avatar.Pet
      src={getCompressedImgUrl(src || '', ImgSizeMap[compressedSize || 'W_128'])}
      ref={forwardedRef}
      {...props}
    />
  ),
);

export const CompressedAvatarStaff = forwardRef<HTMLDivElement, AvatarStaffProps & CompressedAvatarProps>(
  ({ src, compressedSize, ...props }, forwardedRef) => (
    <Avatar.Staff
      src={getCompressedImgUrl(src || '', ImgSizeMap[compressedSize || 'W_128'])}
      ref={forwardedRef}
      {...props}
    />
  ),
);

export const CompressedAvatarVan = forwardRef<HTMLDivElement, AvatarVanProps & CompressedAvatarProps>(
  ({ src, compressedSize, ...props }, forwardedRef) => (
    <Avatar.Van
      src={getCompressedImgUrl(src || '', ImgSizeMap[compressedSize || 'W_128'])}
      ref={forwardedRef}
      {...props}
    />
  ),
);

type CompressedAvatarComponent = React.ForwardRefExoticComponent<
  AvatarProps & CompressedAvatarProps & React.RefAttributes<HTMLDivElement>
> & {
  Business: typeof CompressedAvatarBusiness;
  Pet: typeof CompressedAvatarPet;
  Staff: typeof CompressedAvatarStaff;
  Van: typeof CompressedAvatarVan;
  Client: typeof CompressedAvatarClient;
};

export const CompressedAvatar = CompressedAvatarBase as CompressedAvatarComponent;

CompressedAvatar.Business = CompressedAvatarBusiness;
CompressedAvatar.Pet = CompressedAvatarPet;
CompressedAvatar.Staff = CompressedAvatarStaff;
CompressedAvatar.Van = CompressedAvatarVan;
CompressedAvatar.Client = CompressedAvatarClient;

CompressedAvatar.displayName = 'CompressedAvatar';
CompressedAvatarBusiness.displayName = 'CompressedAvatar.Business';
CompressedAvatarPet.displayName = 'CompressedAvatar.Pet';
CompressedAvatarStaff.displayName = 'CompressedAvatar.Staff';
CompressedAvatarVan.displayName = 'CompressedAvatar.Van';
CompressedAvatarClient.displayName = 'CompressedAvatar.Client';
