import { Dropdown, Menu } from 'antd';
import classNames from 'classnames';
import React, { type PropsWithChildren, type ReactNode, memo } from 'react';
import SvgIconArrowSvg from '../../assets/svg/icon-arrow.svg';
import SvgIconRemoveSvg from '../../assets/svg/icon-remove.svg';
import { SvgIcon } from '../Icon/Icon';

export interface FilterWrapperDropdownProps {
  title: string;
  dropdownValue: ReactNode;
  disabled?: boolean;
  showClearIcon?: boolean;
  shortOverlay?: boolean;
  className?: string;
  visible?: boolean;
  onVisibleChange?: (visible: boolean) => void;
  onClear?: () => void;
}

export const FilterWrapperDropdown = memo(
  ({
    title,
    dropdownValue,
    disabled,
    showClearIcon,
    shortOverlay,
    className,
    onVisibleChange,
    onClear,
    children,
    visible,
  }: PropsWithChildren<FilterWrapperDropdownProps>) => {
    const visibleProps = visible !== undefined ? { visible } : {};

    return (
      <div
        className={classNames(
          '!moe-pr-[12px] !moe-text-[#333 !moe-text-[14px] !moe-font-medium !moe-rounded-[100px] !moe-border !moe-border-solid !moe-whitespace-nowrap',
          '!moe-border-[#b0cefb] !moe-bg-[#ebf3fe]',
          className,
        )}
      >
        <Dropdown
          disabled={disabled}
          className="!moe-inline-block !moe-pl-[12px] !moe-leading-[30px] !moe-cursor-pointer"
          trigger={['click']}
          {...visibleProps}
          overlay={
            <Menu className={`!moe-p-[16px] !moe-rounded-[8px] ${shortOverlay ? '!moe-w-[320px]' : ''}`}>
              <div className="!moe-mb-[16px] !moe-text-sm !moe-font-bold">{title}</div>
              {children}
            </Menu>
          }
          onVisibleChange={onVisibleChange}
        >
          <span>
            <span>
              {title}
              {dropdownValue || dropdownValue === 0 ? ':' : ''}
            </span>
            <span className="!moe-ml-[4px] !moe-text-[#3985f5]">{dropdownValue}</span>
            {!disabled && <SvgIcon src={SvgIconArrowSvg} size={9} className="!moe-ml-[8px]" />}
          </span>
        </Dropdown>
        {showClearIcon && (
          <SvgIcon src={SvgIconRemoveSvg} color="#666" size={16} className="!moe-cursor-pointer" onClick={onClear} />
        )}
      </div>
    );
  },
);
