import { SubscriptionModelStatus } from '@moego/api-web/moego/models/membership/v1/subscription_models';
import { MajorMembership, MinorMembership, MajorMembershipGrey, MinorMembershipGrey } from '@moego/icons-react';
import { Heading, Text, Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type ReactNode, type PropsWithChildren } from 'react';
import { ApptTestIds } from '../../config/testIds/apptDrawer';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { selectActiveMembershipSubscriptionList } from '../../store/membership/membership.selectors';

interface MembershipIdentifyProps {
  className?: string;
  customerId: string | number;
  isDisabledTooltip?: boolean;
  isMajorIcon?: boolean;
  fallbackIcon?: ReactNode;
}

const onStopPropagation = (e: React.MouseEvent) => {
  e.stopPropagation();
};

export const MembershipIdentify = React.memo((props: PropsWithChildren<MembershipIdentifyProps>) => {
  const { customerId, className, isDisabledTooltip, isMajorIcon, fallbackIcon } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const [membershipSubscriptionList] = useSelector(selectActiveMembershipSubscriptionList(String(customerId)));

  // If no active membership, return fallbackIcon or null
  if (!membershipSubscriptionList.length) {
    return fallbackIcon != null ? <>{fallbackIcon}</> : null;
  }

  const allPaused = membershipSubscriptionList.every(
    ({ subscription }) => subscription.status === SubscriptionModelStatus.PAUSED,
  );

  const renderContent = () => {
    return (
      <div onClick={onStopPropagation}>
        <div className="moe-flex moe-justify-between moe-mb-8px-200">
          <Heading size="5">Memberships</Heading>
        </div>
        {membershipSubscriptionList.map(({ membership, subscription }) => {
          // The subscription is active and not canceling
          const isRenewing = !subscription.cancelAtPeriodEnd;
          const isPaused = subscription.status === SubscriptionModelStatus.PAUSED;
          let subscriptionDateLabel;
          if (isPaused) {
            if (subscription.autoResumeAt) {
              subscriptionDateLabel = ['Reactive on', business.formatDate(subscription.autoResumeAt)].join(' ');
            } else {
              subscriptionDateLabel = 'Need manual reactivation';
            }
          } else {
            subscriptionDateLabel = [
              isRenewing ? 'Next bill on' : 'Expiring on',
              business.formatDate(isRenewing ? subscription.nextBillingDate : subscription.expiresAt),
            ].join(' ');
          }

          return (
            <div key={membership.id} className="moe-mb-8px-200 last:moe-mb-0">
              <div className="moe-text-primary moe-flex moe-flex-col moe-gap-y-xxs">
                <Heading size="6" className="moe-line-clamp-2 moe-mr-s">
                  {membership.name}
                </Heading>
                <div className="moe-flex moe-items-center moe-gap-x-xxs">
                  <Text variant="small" className="moe-whitespace-nowrap">
                    {`${business.formatAmount(membership.price)}/${membership.billingCycleName()}`}
                  </Text>
                  <Text
                    variant="small"
                    className={cn('moe-text-tertiary moe-whitespace-nowrap', {
                      'moe-text-tertiary': isRenewing && !isPaused,
                      'moe-text-warning': !isRenewing || isPaused,
                    })}
                  >
                    {`(${subscriptionDateLabel})`}
                  </Text>
                </div>
              </div>

              <Text variant="small" className="moe-text-tertiary moe-mt-xs">
                {membership.description}
              </Text>
            </div>
          );
        })}
      </div>
    );
  };

  const NormalIcon = isMajorIcon ? MajorMembership : MinorMembership;
  const DisabledIcon = isMajorIcon ? MajorMembershipGrey : MinorMembershipGrey;

  return (
    <Tooltip
      backgroundTheme="light"
      classNames={{
        container: 'moe-max-w-[500px]',
      }}
      content={renderContent()}
      isDisabled={isDisabledTooltip}
    >
      {allPaused ? (
        <DisabledIcon
          className={cn('moe-min-w-[20px]', className)}
          onClick={onStopPropagation}
          data-testid={ApptTestIds.ApptMemberShipIdentifyDisbledIcon}
        />
      ) : (
        <NormalIcon
          className={cn('moe-min-w-[20px]', className)}
          onClick={onStopPropagation}
          data-testid={ApptTestIds.ApptMemberShipIdentifyAvaliableIcon}
        />
      )}
    </Tooltip>
  );
});
