/*
 * @Author: guoxiaoyang <<EMAIL>>
 * @Date: 2024-05-06 20:06:28
 * @LastEditTime: 2024-05-06 22:40:21
 * @LastEditors: guoxiaoyang
 */

import { action } from 'amos';

import { type SuggestedAddress } from '@moego/api-web/moego/models/map/v1/map_models';
import { Form, Input, type UseFormReturn, cn, useForm } from '@moego/ui';
import React, { forwardRef, type MutableRefObject, useEffect, useImperativeHandle, useRef } from 'react';
import { type CommonAddressModel } from '../../store/customer/customerAddress.boxes';
import { filterNilValues } from '../../utils/filterNilValues';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { AddressInputV3 } from './AddressInputV3';
import { getAddress } from './getAddress';
import { type SearchAddressResult } from './types';

export interface AddressFormProps<T extends CommonAddressModel = CommonAddressModel> {
  address?: T | null;
  defaultAddress?: Partial<CommonAddressModel>;
  className?: string;
  twoColumnsLayout?: boolean;
}

export const dummyCreateAdd = action(async () => {});

export const addressFormKeys = [
  'address1',
  'address2',
  'city',
  'state',
  'zipcode',
  'country',
  'lat',
  'lng',
  'coordinate',
] as const;

export function isAddressFormField(key: string): key is (typeof addressFormKeys)[number] {
  return addressFormKeys.includes(key as any);
}

export type AddressFormFields = Pick<CommonAddressModel, (typeof addressFormKeys)[number]>;
export type AddressFormFieldKeys = keyof AddressFormFields;

export function useAddressState(form?: UseFormReturn<AddressFormFields>, initialAddress?: SearchAddressResult) {
  const result = useRef<SearchAddressResult>(initialAddress ? initialAddress : { lng: '', lat: '' });
  const handleSelectAddress = useLatestCallback(async (address: SuggestedAddress) => {
    const addressResult = await getAddress(address);
    result.current = {
      ...addressResult,
      lat: addressResult.coordinate?.latitude ? String(addressResult.coordinate?.latitude) : '',
      lng: addressResult.coordinate?.longitude ? String(addressResult.coordinate?.longitude) : '',
    };
    if (form) {
      form.clearErrors(Object.keys(result.current) as AddressFormFieldKeys[]);
      form.reset({ ...form.getValues(), ...result.current }, { keepDirty: true, keepErrors: true });
    }
    return result.current;
  });
  const handleClearAddress = useLatestCallback(() => {
    result.current = { lng: '', lat: '' };
  });
  const handleResetAddress = useLatestCallback(() => {
    result.current = initialAddress ? initialAddress : { lng: '', lat: '' };
  });

  useEffect(() => {
    if (initialAddress) {
      result.current = initialAddress;
    }
  }, [initialAddress]);

  return [result, handleSelectAddress, handleClearAddress, handleResetAddress] as [
    MutableRefObject<SearchAddressResult>,
    (address: SuggestedAddress) => Promise<SearchAddressResult>,
    () => void,
    () => void,
  ];
}

export const useAddressForm = function <T extends CommonAddressModel>({
  address,
  defaultAddress,
  className,
  twoColumnsLayout = true,
}: AddressFormProps<T>) {
  const form = useForm<AddressFormFields>({
    defaultValues: filterNilValues(defaultAddress),
    mode: 'onBlur',
  });
  const [, handleSelectAddress] = useAddressState(form);

  useEffect(() => {
    if (address) {
      form.reset(address);
    }
  }, [address]);

  const dom = (
    <Form form={form} className={className} footer={null}>
      <div className="moe-grid moe-grid-cols-2 moe-gap-[24px]">
        <Form.Item label="Address line 1" name="address1" rules={{ required: 'Address line 1 is required' }}>
          <AddressInputV3
            placeholder="Street address or P.O. Box"
            onSelectAddress={handleSelectAddress}
            isRequired
            classNames={{
              base: 'moe-col-span-full',
            }}
          />
        </Form.Item>
        <Form.Item label="Address line 2" name="address2">
          <Input placeholder="Apt, suite, unit, building, floor, etc" className="moe-col-span-full" />
        </Form.Item>
        <Form.Item label="City" name="city" rules={{ required: 'City is required' }}>
          <Input
            placeholder="City"
            isRequired
            className={cn({
              'moe-col-span-full': !twoColumnsLayout,
            })}
          />
        </Form.Item>
        <Form.Item label="State" name="state">
          <Input
            placeholder="State"
            className={cn({
              'moe-col-span-full': !twoColumnsLayout,
            })}
          />
        </Form.Item>
        <Form.Item label="Zipcode" name="zipcode">
          <Input
            placeholder="Zipcode"
            className={cn({
              'moe-col-span-full': !twoColumnsLayout,
            })}
          />
        </Form.Item>
        <Form.Item label="Country" name="country">
          <Input
            placeholder="Country"
            id="address-country"
            className={cn({
              'moe-col-span-full': !twoColumnsLayout,
            })}
          />
        </Form.Item>
      </div>
    </Form>
  );
  return { form, dom };
};

export type AddressFormRef = { form: ReturnType<typeof useAddressForm>['form'] };

export const AddressForm = forwardRef<AddressFormRef, AddressFormProps>((props, ref) => {
  const { form, dom } = useAddressForm(props);
  /**
   * formState is wrapped with a Proxy to improve render performance and skip extra logic if specific state is not subscribed to. Therefore make sure you invoke or read it before a render in order to enable the state update.
   *
   * 需要在 render 之前读取 formState.isDirty，以便启用状态更新
   * 而 ref 使用注定是在 render 之后才能被调用，所以需要在 render 之前读取 formState.isDirty
   * 后续才能在函数里通过  ref.current?.form.formState.isDirty 获取到最新的值
   */
  form.formState.isDirty;
  useImperativeHandle(ref, () => ({ form }), [form]);
  return dom;
});
