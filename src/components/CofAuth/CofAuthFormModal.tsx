import { CloseOutlined } from '@ant-design/icons';
import { useNextZIndex } from '@moego/ui';
import { useSelector } from 'amos';
import html2canvas from 'html2canvas';
import React, { memo, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useUnmount } from 'react-use';
import IconCofAuthFormDownloadSvg from '../../assets/icon/cof-auth-form-download.svg';
import IconCofAuthFormPrintSvg from '../../assets/icon/cof-auth-form-print.svg';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { ImgIcon } from '../Icon/Icon';
import { Fill } from '../Style/Style';
import { CofAuthFormModalGlobalStyle, CofAuthFormModalView } from './CofAuthFormModal.style';

export interface CofAuthFormData {
  submitted: number;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  cardLast4: string;
  cardBrand: string;
}

export interface CofAuthFormModalProps {
  className?: string;
  data: CofAuthFormData | undefined;
  onClose: () => void;
}

export const CofAuthFormModal = memo<CofAuthFormModalProps>(({ className, data, onClose }) => {
  const visible = !!data;
  const zIndex = useNextZIndex();

  const [business] = useSelector(selectCurrentBusiness());

  const container = useRef<HTMLDivElement>();
  if (!container.current) {
    container.current = document.createElement('div');
    container.current.className = 'cof-auth-form-modal';
    document.body.appendChild(container.current);
  }
  container.current.classList.toggle('visible', visible);
  useUnmount(() => {
    if (container.current) {
      document.body.removeChild(container.current);
    }
  });
  const contentRef = useRef<HTMLDivElement>(null);
  const title = `Credit card authorization form - ${data?.firstName} - ${data?.lastName}`.replace(
    /[<>:"/\\|?*]+/g,
    '_',
  );

  const handleDownload = () => {
    if (!contentRef.current) {
      return;
    }
    html2canvas(contentRef.current, { scale: 2, useCORS: true, allowTaint: true }).then((canvas) => {
      const imageData = canvas.toDataURL('image/png');
      const anchor = document.createElement('a');
      anchor.href = imageData;
      anchor.download = title + '.png';
      anchor.target = '_blank';
      anchor.click();
    });
  };

  const handlePrint = () => {
    const originalTitle = document.title;
    document.title = title;
    print();
    document.title = originalTitle;
  };

  if (!visible) {
    return null;
  }
  return (
    <>
      {<CofAuthFormModalGlobalStyle />}
      {createPortal(
        <CofAuthFormModalView
          className={className}
          style={{
            zIndex,
          }}
        >
          <div className="modal-content">
            <div className="container">
              <div className="content" ref={contentRef}>
                <div className="header">{business.businessName}</div>
                <div className="title">Credit card authorization form</div>
                <div className="body">
                  <p className="cancel-claim">
                    We would like you to save your credit card on file for future services. You may cancel this
                    authorization at any time by contacting us.
                  </p>
                  <p>
                    <span className="form-label">Date:</span>
                    <span>{business.localDate(data?.submitted ?? 0).format(business.dateFormat + ' HH:mm')}</span>
                  </p>
                  <p>
                    <span className="form-label">Name:</span>
                    <span>{[data?.firstName, data?.lastName].filter((s) => !!s).join(' ')}</span>
                  </p>
                  <p>
                    <span className="form-label">Contact:</span>
                    <span>{data?.phoneNumber}</span>
                  </p>
                  <p>
                    <span className="form-label">Email:</span>
                    <span>{data?.email}</span>
                  </p>
                  <p>
                    <span className="form-label">Content:</span>
                    <span>
                      <span className="asterisk">**** **** ****</span> {data?.cardLast4} ({data?.cardBrand})
                    </span>
                  </p>
                  <p className="confirm">
                    By confirming, I consent to have the card information stored on file to be saved for future charges.
                  </p>
                </div>
              </div>
            </div>
            <div className="actions">
              <ImgIcon src={IconCofAuthFormDownloadSvg} width={32} onClick={handleDownload} />
              <Fill width={12} />
              <ImgIcon src={IconCofAuthFormPrintSvg} width={32} onClick={handlePrint} />
            </div>
          </div>
          <CloseOutlined className="close" onClick={onClose} />
        </CofAuthFormModalView>,
        container.current,
      )}
    </>
  );
});
