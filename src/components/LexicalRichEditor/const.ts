import { type DragDropPasteFile, type DragDropPasteOptions } from '@moego/fn-components';
import { handleImageUpload } from '../RichTextEditor/RichEditorPlugins/upload.utils';

export const baseRichEditorDragDropPasteOptions: DragDropPasteOptions = {
  handleFiles: async (files) => {
    const results = await Promise.all(
      files.map(async (file) => {
        const res = await handleImageUpload({
          file,
        });
        if (res) {
          return { result: res.url, file } as DragDropPasteFile;
        }
        return null;
      }),
    );
    return results.filter(Boolean) as unknown as DragDropPasteFile[];
  },
};
