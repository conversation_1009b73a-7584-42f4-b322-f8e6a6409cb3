import { forwardRef, useImperativeHandle, useRef } from 'react';
import { type MultiChoiceFeedbackProps } from '../types';
import React from 'react';
import { Select, type SelectRefConfig } from '@moego/ui';

export const FeedbackMultiChoice = forwardRef<HTMLElement, MultiChoiceFeedbackProps>(
  (props: MultiChoiceFeedbackProps, forwardedRef) => {
    const { type, options = [], choices = [], onChoicesChange, ...restProps } = props;

    const selectRef = useRef<SelectRefConfig | null>(null);
    useImperativeHandle(forwardedRef, () => selectRef.current?.nativeElement as HTMLElement);

    return (
      <Select.Multiple
        {...restProps}
        mode="value"
        items={options.map((value) => ({ value }))}
        value={choices}
        onChange={(value) => {
          onChoicesChange(value);
        }}
        ref={selectRef}
      />
    );
  },
);
