import { forwardRef } from 'react';
import { type TextInputFeedbackProps } from '../types';
import React from 'react';
import { Input } from '@moego/ui';

const TEXT_INPUT_LENGTH_LIMIT = 1000;

export const FeedbackTextInput = forwardRef<HTMLTextAreaElement, TextInputFeedbackProps>((props, forwardedRef) => {
  const { type, inputText, onInputTextChange, ...restProps } = props;

  return (
    <Input.TextArea
      {...restProps}
      value={inputText}
      onChange={onInputTextChange}
      showCount
      maxLength={TEXT_INPUT_LENGTH_LIMIT}
      ref={forwardedRef}
    />
  );
});
