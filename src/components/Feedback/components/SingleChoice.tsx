import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { type SingleChoiceFeedbackProps } from '../types';
import React from 'react';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { Input, Select, type SelectRefConfig } from '@moego/ui';

const CUSTMIZE_OPTION = 'Customize';

export const FeedbackSingleChoice = forwardRef<HTMLElement, SingleChoiceFeedbackProps>(
  (props: SingleChoiceFeedbackProps, forwardedRef) => {
    const {
      type,
      options = [],
      choices = [],
      onChoicesChange,
      customOptions = [],
      isInvalid,
      errorMessage,
      ...restProps
    } = props;

    const items = [...options, CUSTMIZE_OPTION].map((value) => ({ value }));

    let value: string | null;
    if (customOptions.length > 0 && (customOptions[0] === '' || customOptions[0] === choices[0])) {
      value = CUSTMIZE_OPTION;
    } else {
      value = choices[0] || null;
    }

    // 记录 Input 输入框是否刚刚从隐藏变为显示，具体作用见后续的注释
    const [firstShowInput, setFirstShowInput] = useState(value === CUSTMIZE_OPTION);

    const onChange = useLatestCallback((value: string | null) => {
      if (value === CUSTMIZE_OPTION) {
        onChoicesChange([], ['']);
      } else {
        onChoicesChange(value ? [value] : [], []);
      }

      setFirstShowInput(value === CUSTMIZE_OPTION);
    });

    const inputText = customOptions[0] || '';

    const onInputChange = useLatestCallback((value: string) => {
      setFirstShowInput(false);
      onChoicesChange(value ? [value] : [], value ? [value] : ['']);
    });

    const selectRef = useRef<SelectRefConfig | null>(null);
    useImperativeHandle(forwardedRef, () => selectRef.current?.nativeElement as HTMLElement);

    return (
      <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
        <Select
          {...restProps}
          // Select 选择了 CUSTMIZE_OPTION 后错误信息应该显示在 Input 上，这里做区分判断
          isInvalid={value !== CUSTMIZE_OPTION && isInvalid}
          errorMessage={value !== CUSTMIZE_OPTION ? errorMessage : null}
          items={items}
          value={value}
          onChange={onChange}
          isClearable
          ref={selectRef}
        />
        {value === CUSTMIZE_OPTION && (
          <Input.Text
            // Input 每次从隐藏到出现 (Select 从其他选项切换到 CUSTMIZE_OPTION) 时，choices 会为空从而触发了错误信息
            // 但实际交互场景下，这个时候是不该出现错误信息的，所以这里要区分首次出现时，使其一定合法
            // 不过有个可改进的地方是：用户切换到 CUSTMIZE_OPTION 后什么都不输入直接提交的话，不会出现错误信息
            // 需要一个 flag 来标识表单是否刚点了提交以及结合 dirty 来判断 (后续有必要可优化)
            isInvalid={!firstShowInput && isInvalid}
            errorMessage={!firstShowInput ? errorMessage : null}
            value={inputText}
            onChange={onInputChange}
            placeholder="Enter customized feedback..."
          />
        )}
      </div>
    );
  },
);
