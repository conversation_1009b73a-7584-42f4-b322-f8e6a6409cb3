import { forwardRef } from 'react';
import { type ShortTextInputFeedbackProps } from '../types';
import React from 'react';
import { Input } from '@moego/ui';

const SHORT_TEXT_INPUT_LENGTH_LIMIT = 100;

export const FeedbackShortTextInput = forwardRef<HTMLInputElement, ShortTextInputFeedbackProps>(
  (props, forwardedRef) => {
    const { type, inputText, onInputTextChange, ...restProps } = props;

    return (
      <Input.Text
        {...restProps}
        value={inputText}
        onChange={onInputTextChange}
        showCount
        maxLength={SHORT_TEXT_INPUT_LENGTH_LIMIT}
        ref={forwardedRef}
      />
    );
  },
);
