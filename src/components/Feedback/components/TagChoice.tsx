import React, { forwardRef, type ReactNode } from 'react';
import { type TagChoiceFeedbackProps } from '../types';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { cn, Form, IconButton, usePress } from '@moego/ui';
import { MinorCloseOutlined, MinorPlusOutlined } from '@moego/icons-react';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { AddTagModal } from './AddTagModal';

const TagChoiceItem = ({
  value,
  isActive,
  isClosable = false,
  prefix,
  onPress,
  onClose,
}: {
  value: string;
  isActive?: boolean;
  isClosable?: boolean;
  prefix?: ReactNode;
  onPress: (value: string) => void;
  onClose?: (value: string) => void;
}) => {
  const { pressProps } = usePress({ onPress: () => onPress(value) });

  return (
    <div
      {...pressProps}
      className={cn(
        'moe-flex moe-items-center moe-gap-x-xxs moe-h-[24px] moe-px-spacing-xs',
        'moe-cursor-pointer moe-text-primary moe-text-caption moe-bg-neutral-default',
        'moe-border moe-border-button moe-rounded-full',
        'moe-transition-colors',
        'hover:moe-bg-neutral-sunken-0',
        'focus:moe-bg-neutral-sunken-0',
        {
          'moe-bg-brand-subtle moe-border-brand hover:moe-bg-brand-subtle focus:moe-bg-brand-subtle': isActive,
        },
      )}
    >
      {prefix}
      {value}
      {isClosable && (
        <IconButton
          className="moe-w-[16px] moe-h-[16px]"
          icon={<MinorCloseOutlined className="!moe-text-[16px]" />}
          variant="primary"
          color="transparent"
          size="xs"
          onPress={() => onClose?.(value)}
        />
      )}
    </div>
  );
};

export const FeedbackTagChoice = forwardRef<HTMLDivElement, TagChoiceFeedbackProps>((props, forwardedRef) => {
  const {
    type,
    options = [],
    choices = [],
    onChoicesChange,
    customOptions = [],
    label,
    isRequired,
    description,
    errorMessage,
    isInvalid,
    onBlur,
    ...restProps
  } = props;

  const { mountFloatableAutoClose } = useFloatableHost<string>();

  const choicesSet = new Set(choices);

  const handleChange = (choices: string[], customOptions?: string[]) => {
    onChoicesChange(choices, customOptions);
    // 手动调用一下 blur 触发一下表单验证
    onBlur?.();
  };

  const handleToggle = useLatestCallback((value: string) => {
    if (choicesSet.has(value)) {
      handleChange(choices.filter((v) => v !== value));
    } else {
      handleChange([...choices, value]);
    }
  });

  const handleAdd = useLatestCallback(async () => {
    const value = await mountFloatableAutoClose(<AddTagModal existingTags={new Set([...options, ...customOptions])} />);

    if (!value) return;

    handleChange([...choices, value], [...customOptions, value]);
  });

  const handleRemove = useLatestCallback((value: string) => {
    const newCustomOptions = customOptions.filter((v) => v !== value);

    if (choicesSet.has(value)) {
      handleChange(
        choices.filter((v) => v !== value),
        newCustomOptions,
      );
    } else {
      handleChange(choices, newCustomOptions);
    }
  });

  return (
    <div {...restProps} ref={forwardedRef}>
      <Form.Label label={label} isRequired={isRequired} />
      <div className="moe-flex moe-flex-wrap moe-gap-s">
        {options.map((value) => (
          <TagChoiceItem key={value} value={value} isActive={choicesSet.has(value)} onPress={handleToggle} />
        ))}
        {customOptions.map((value) => (
          <TagChoiceItem
            key={value}
            value={value}
            isActive={choicesSet.has(value)}
            isClosable
            onPress={handleToggle}
            onClose={handleRemove}
          />
        ))}
        <TagChoiceItem
          value="Add new"
          prefix={<MinorPlusOutlined className="!moe-text-[16px]" />}
          onPress={handleAdd}
        />
      </div>
      <Form.HelpText className="moe-mt-xxs" description={description} errorMessage={errorMessage} />
    </div>
  );
});
