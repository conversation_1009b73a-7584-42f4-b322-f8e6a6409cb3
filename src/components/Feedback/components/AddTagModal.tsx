import React, { useState } from 'react';
import { FocusScope, Input, Modal, type ModalProps } from '@moego/ui';

const TAG_NAME_LENGTH_LIMIT = 30;

export const AddTagModal = ({
  existingTags,
  onConfirm,
  ...restProps
}: Omit<ModalProps, 'onConfirm'> & { existingTags: Set<string>; onConfirm?: (value?: string) => void }) => {
  const [value, setValue] = useState<string>();
  const isDuplicate = !!value && existingTags.has(value);

  return (
    <Modal
      onConfirm={() => onConfirm?.(value)}
      {...restProps}
      title="Add new tag"
      size="s"
      confirmButtonProps={{
        isDisabled: !value || isDuplicate,
      }}
    >
      <FocusScope autoFocus restoreFocus>
        <Input.Text
          label="Tag name"
          isRequired
          maxLength={TAG_NAME_LENGTH_LIMIT}
          showCount
          onChange={(newValue) => {
            setValue(newValue || undefined);
          }}
          isInvalid={isDuplicate}
          errorMessage={isDuplicate ? 'Duplicate tag name' : undefined}
        />
      </FocusScope>
    </Modal>
  );
};
