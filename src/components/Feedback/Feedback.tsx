import { forwardRef, type Ref } from 'react';
import { type FeedbackProps, type FeedbackValue } from './types';
import React from 'react';
import { QuestionCategoryType, QuestionType } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { FeedbackTagChoice } from './components/TagChoice';
import { FeedbackSingleChoice } from './components/SingleChoice';
import { FeedbackMultiChoice } from './components/MultiChoice';
import { FeedbackTextInput } from './components/TextInput';
import { FeedbackShortTextInput } from './components/ShortTextInput';

const defaultPartialValue = Object.freeze<FeedbackValue>({
  category: QuestionCategoryType.UNSPECIFIED,
  type: QuestionType.UNSPECIFIED,
  key: '',
  title: '',
  required: false,
  show: true,
  options: [],
  choices: [],
  customOptions: [],
  placeholder: '',
  // 兼容旧 grooming 接口，因为 text 作为回退值可以直接指定一个默认值，但 inputText 指定了默认值会永远无法回退到 text
  text: '',
});

export const Feedback = forwardRef<HTMLElement, FeedbackProps>((props, forwardedRef) => {
  const { value, onChange, description, errorMessage, isInvalid, onBlur, ...restProps } = props;

  const mergeValue = (newValue: Partial<FeedbackValue>) => {
    return {
      ...defaultPartialValue,
      ...value,
      ...newValue,
    } satisfies FeedbackValue;
  };

  const mergedValue = mergeValue({});
  const { title, required = false, show = true } = mergedValue;

  const onChoicesChange = useLatestCallback((choices: string[], customOptions?: string[]) => {
    onChange?.(mergeValue(customOptions ? { choices, customOptions } : { choices }));
  });

  const onInputTextChange = useLatestCallback((inputText: string) => {
    onChange?.(mergeValue({ inputText, text: inputText }));
  });

  const renderControl = () => {
    const { type, options, choices, customOptions } = mergedValue;
    const inputText = mergedValue.inputText ?? mergedValue.text ?? '';
    // 后端默认下发空字符，此时应该使用默认值，所以处理为 undefined
    const placeholder: string | undefined = mergedValue.placeholder || undefined;

    const commonProps = {
      ...restProps,
      label: title,
      isRequired: required,
      description,
      errorMessage,
      isInvalid,
      onBlur,
    };

    switch (type) {
      case QuestionType.SINGLE_CHOICE:
        return (
          <FeedbackSingleChoice
            {...commonProps}
            type={type}
            options={options}
            choices={choices}
            onChoicesChange={onChoicesChange}
            customOptions={customOptions}
            placeholder={placeholder}
            ref={forwardedRef}
          />
        );
      case QuestionType.MULTI_CHOICE:
        return (
          <FeedbackMultiChoice
            {...commonProps}
            type={type}
            options={options}
            choices={choices}
            onChoicesChange={onChoicesChange}
            placeholder={placeholder}
            ref={forwardedRef}
          />
        );
      case QuestionType.TAG_CHOICE:
        return (
          <FeedbackTagChoice
            {...commonProps}
            type={type}
            options={options}
            choices={choices}
            onChoicesChange={onChoicesChange}
            customOptions={customOptions}
            ref={forwardedRef as Ref<HTMLDivElement>}
          />
        );
      case QuestionType.SHORT_TEXT_INPUT:
        return (
          <FeedbackShortTextInput
            {...commonProps}
            type={type}
            inputText={inputText}
            onInputTextChange={onInputTextChange}
            placeholder={placeholder}
            ref={forwardedRef as Ref<HTMLInputElement>}
          />
        );
      case QuestionType.TEXT_INPUT:
        return (
          <FeedbackTextInput
            {...commonProps}
            type={type}
            inputText={inputText}
            onInputTextChange={onInputTextChange}
            placeholder={placeholder}
            ref={forwardedRef as Ref<HTMLTextAreaElement>}
          />
        );
      default:
        return null;
    }
  };

  if (!show) {
    return null;
  }

  return renderControl();
});
