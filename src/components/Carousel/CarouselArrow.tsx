import { MinorChevronRightOutlined } from '@moego/icons-react';
import { cn } from '@moego/ui';
import React, { memo } from 'react';

export interface CarouselArrowProps {
  className?: string;
  reverse?: boolean;
  onClick?: () => void;
}

export const CarouselArrow = memo<CarouselArrowProps>(function CarouselArrow(props) {
  const { className, reverse, onClick } = props;

  return (
    <div
      className={cn(
        'moe-w-[56px] moe-flex moe-flex-col moe-justify-center moe-items-start',
        reverse ? 'moe-rotate-180' : undefined,
        className,
      )}
      style={{ background: 'linear-gradient(270deg, #FFFFFF 12.12%, rgba(255, 255, 255, 0) 100%)' }}
      onClick={onClick}
    >
      <div
        className={cn(
          'moe-w-[32px] moe-h-[32px] moe-flex moe-items-center moe-justify-center moe-rounded-full moe-border moe-border-solid moe-border-[#CDCDCD] moe-cursor-pointer moe-bg-[#fff]',
        )}
      >
        <MinorChevronRightOutlined />
      </div>
    </div>
  );
});
