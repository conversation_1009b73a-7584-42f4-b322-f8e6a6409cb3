import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { FeedingMedicationScheduleDateType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_feeding_medication_enum';
import dayjs, { type Dayjs } from 'dayjs';
import { createEnum } from '../../store/utils/createEnum';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { enumOptions } from '../common/select';
import {
  ServiceItemType,
  DateType as ServiceSettingDateType,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type BusinessRecord } from '../../store/business/business.boxes';

export interface DisabledDaysParams<T = Dayjs | null | string> {
  minDate?: T;
  maxDate?: T;
  specificDates?: string[];
}

export const getDisabledDays = (d: Dayjs, params: DisabledDaysParams) => {
  const { minDate, maxDate, specificDates } = params;
  const isOutSpecificDates = specificDates?.length ? !specificDates.includes(d.format(DATE_FORMAT_EXCHANGE)) : false;
  // 在 dayjs 中，isBefore 方法的第二个参数 date 是等价于 day 的，但是我们可以统一用 day。
  // https://day.js.org/docs/en/manipulate/start-of#list-of-all-available-units
  return d.isBefore(minDate, 'day') || d.isAfter(maxDate, 'day') || isOutSpecificDates;
};

export const resolveSpecificDates = (props: {
  specificDates?: Dayjs[];
  dateType?: PetDetailDateType;
  firstStartDate?: string;
}) => {
  const { specificDates, dateType, firstStartDate } = props;
  if (dateType === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
    if (specificDates?.length) {
      return specificDates;
    }
    return firstStartDate ? [dayjs(firstStartDate)] : [];
  } else {
    return [];
  }
};

export const DateTypeWordingMap: Record<ServiceSettingDateType, string> = {
  [ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKOUT_DAY]: 'Every day except the check-out day',
  [ServiceSettingDateType.SPECIFIC_DATE]: 'Certain date(s)',
  [ServiceSettingDateType.DATE_POINT]: 'Certain date(s)',
  [ServiceSettingDateType.EVERY_DAY_INCLUDE_CHECKOUT_DAY]: 'Every day',
  [ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKIN_DAY]: 'Every day except the check-in day',
  [ServiceSettingDateType.LAST_DAY]: 'Last day',
  [ServiceSettingDateType.FIRST_DAY]: 'First day',
  [ServiceSettingDateType.UNSPECIFIED]: '',
};

export const SelectDateTypeEnum = createEnum(
  {
    EVERYDAY_INCLUDE_CHECKOUT_DAY: [
      PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
      {
        label: DateTypeWordingMap[PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY],
      },
    ],
    EVERYDAY_EXCEPT_CHECKIN_DAY: [
      PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY,
      {
        label: DateTypeWordingMap[PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY],
      },
    ],
    EVERYDAY: [
      PetDetailDateType.PET_DETAIL_DATE_EVERYDAY,
      {
        label: DateTypeWordingMap[PetDetailDateType.PET_DETAIL_DATE_EVERYDAY],
      },
    ],
    LAST_DAY: [
      PetDetailDateType.PET_DETAIL_DATE_LAST_DAY,
      {
        label: DateTypeWordingMap[PetDetailDateType.PET_DETAIL_DATE_LAST_DAY],
      },
    ],
    SPECIFIC_DATE: [
      PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE,
      {
        label: DateTypeWordingMap[PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE],
      },
    ],
  } as const,
  { enableValueSort: false },
);

export const SelectDateTypeOptions = SelectDateTypeEnum.values.map((v) => {
  const { label } = SelectDateTypeEnum.mapLabels[v];
  return {
    key: v,
    label: label,
  };
});

export const SelectRequireStaffDateTypeEnum = createEnum(
  {
    FIRST_DAY: [
      PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY,
      {
        label: DateTypeWordingMap[PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY],
      },
    ],
    LAST_DAY: [
      PetDetailDateType.PET_DETAIL_DATE_LAST_DAY,
      {
        label: DateTypeWordingMap[PetDetailDateType.PET_DETAIL_DATE_LAST_DAY],
      },
    ],
    DATE_POINT: [
      PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
      {
        // 目前 date point 暂不支持多选，所以先不用复数形式
        label: 'Certain date',
      },
    ],
  },
  { enableValueSort: false },
);

export const SelectRequireStaffDateTypeOptions = SelectRequireStaffDateTypeEnum.values.map((v) => {
  const { label } = SelectRequireStaffDateTypeEnum.mapLabels[v];
  return {
    key: v,
    label: label,
  };
});

export const SelectFeedingMedicationDateTypeEnum = createEnum({
  EverydayExceptCheckoutDay: [
    FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE,
    'Every day except the checkout day',
  ],
  EverydayIncludingCheckoutDay: [
    FeedingMedicationScheduleDateType.EVERYDAY_INCLUDE_CHECKOUT_DATE,
    'Every day including checkout day',
  ],
  SpecificDate: [FeedingMedicationScheduleDateType.SPECIFIC_DATE, 'Certain date(s)'],
});

const NoRequiredStaffDateTypeEnum = createEnum({
  // 包含了 checkout day 的才是真正的 every day
  Everyday: [
    ServiceSettingDateType.EVERY_DAY_INCLUDE_CHECKOUT_DAY,
    DateTypeWordingMap[ServiceSettingDateType.EVERY_DAY_INCLUDE_CHECKOUT_DAY],
  ],
  EverydayExceptCheckInDay: [
    ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKIN_DAY,
    DateTypeWordingMap[ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKIN_DAY],
  ],
  // 早期实现的时候 dateType 的 every day 其实是不包含 checkout day 的
  EverydayExceptCheckOutDay: [
    ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKOUT_DAY,
    DateTypeWordingMap[ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKOUT_DAY],
  ],
  LastDay: [ServiceSettingDateType.LAST_DAY, DateTypeWordingMap[ServiceSettingDateType.LAST_DAY]],
});

const RequiredStaffDateTypeEnum = createEnum(
  {
    FirstDay: [ServiceSettingDateType.FIRST_DAY, DateTypeWordingMap[ServiceSettingDateType.FIRST_DAY]],
    LastDay: [ServiceSettingDateType.LAST_DAY, DateTypeWordingMap[ServiceSettingDateType.LAST_DAY]],
  },
  { enableValueSort: false },
);

export const RequiredStaffDateTypeOptions = enumOptions(RequiredStaffDateTypeEnum, true);

export const NoRequiredStaffDateTypeOptions = enumOptions(NoRequiredStaffDateTypeEnum, true);

interface GetServiceSettingDateTypeOptionsParams {
  isRequiredStaff: boolean;
}

/**
 * service settings 里获取 date type 的 options
 */
export const getServiceSettingDateTypeOptions = ({ isRequiredStaff }: GetServiceSettingDateTypeOptionsParams) => {
  // 后续可能会根据 service type 来判断返回的 options，所以这里 params 留个口子，支持 object params
  if (isRequiredStaff) {
    return RequiredStaffDateTypeOptions;
  }
  return NoRequiredStaffDateTypeOptions;
};

export const getPetDetailDateTypeFromDateType = (dateType: ServiceSettingDateType): PetDetailDateType => {
  switch (dateType) {
    case ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKOUT_DAY:
      return PetDetailDateType.PET_DETAIL_DATE_EVERYDAY;
    case ServiceSettingDateType.SPECIFIC_DATE:
      return PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE;
    case ServiceSettingDateType.DATE_POINT:
      return PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
    case ServiceSettingDateType.EVERY_DAY_INCLUDE_CHECKOUT_DAY:
      return PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY;
    case ServiceSettingDateType.EVERY_DAY_EXCEPT_CHECKIN_DAY:
      return PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY;
    case ServiceSettingDateType.LAST_DAY:
      return PetDetailDateType.PET_DETAIL_DATE_LAST_DAY;
    case ServiceSettingDateType.FIRST_DAY:
      return PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY;
    default:
      return PetDetailDateType.UNSPECIFIED;
  }
};

const ShowDateTypeLabelDateType = [
  PetDetailDateType.PET_DETAIL_DATE_EVERYDAY,
  PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY,
  PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY,
  PetDetailDateType.PET_DETAIL_DATE_LAST_DAY,
  PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY,
];

const ShowDateTimeDateType = [
  PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY,
  PetDetailDateType.PET_DETAIL_DATE_LAST_DAY,
  PetDetailDateType.PET_DETAIL_DATE_DATE_POINT,
];

// 当前 LAST_DAY 在 require staff 和 not require staff 都支持
const BothRequireAndNotRequireStaffDateType = [PetDetailDateType.PET_DETAIL_DATE_LAST_DAY];

export const checkDateTypeTimeRequireStatus = (dateType?: PetDetailDateType, requireDedicatedStaff?: boolean) => {
  let isTimeRequired;
  if (!dateType || !ShowDateTimeDateType.includes(dateType)) {
    isTimeRequired = false;
  } else if (BothRequireAndNotRequireStaffDateType.includes(dateType)) {
    isTimeRequired = requireDedicatedStaff;
  } else {
    isTimeRequired = ShowDateTimeDateType.includes(dateType);
  }
  const isDatePoint = dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
  return {
    isTimeRequired,
    isDynamicDateAndStaticTime: isTimeRequired && !isDatePoint,
    isDatePoint,
  };
};

export const getDateTypeDisplayInfo = (params: {
  dateType?: PetDetailDateType;
  mainServiceItemType?: ServiceItemType;
  isRequireDedicatedStaff?: boolean;
}) => {
  const { dateType, mainServiceItemType, isRequireDedicatedStaff } = params;
  const isMainServiceBoarding = mainServiceItemType === ServiceItemType.BOARDING;
  const isSpecificDate = isMainServiceBoarding && dateType === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE;
  const isDatePoint = isMainServiceBoarding && dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;

  // show label
  const showDateTypeLabel =
    isMainServiceBoarding && dateType && (ShowDateTypeLabelDateType.includes(dateType) || isSpecificDate);
  const showTimeLabel = dateType && isRequireDedicatedStaff && ShowDateTimeDateType.includes(dateType);

  return {
    showDateTypeLabel,
    showTimeLabel,
    isSpecificDate,
    isDatePoint,
  };
};

export const getDateTypeDateLabel = (dateType?: PetDetailDateType) => (dateType ? DateTypeWordingMap[dateType] : '');

export const getFormattedDateTypeDateLabel = (
  business: BusinessRecord,
  dateType?: PetDetailDateType,
  isSpecificDate?: boolean,
  specificDates?: string[],
) => {
  return isSpecificDate
    ? specificDates?.map((v) => business.formatDate(v)).join(', ') || ''
    : getDateTypeDateLabel(dateType);
};
