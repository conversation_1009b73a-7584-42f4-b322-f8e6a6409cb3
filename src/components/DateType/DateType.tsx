import { type PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { ID_ANONYMOUS } from '@moego/finance-utils';
import { useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import React, { forwardRef, memo } from 'react';
import { apptAddOnMapBox } from '../../container/Appt/store/appt.boxes';
import { serviceMapBox } from '../../store/service/service.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { getDisabledDays, resolveSpecificDates } from './DateType.utils';
import { SelectDateType, type SelectDateTypeProps } from './SelectDateType';
import { matchApptServiceScene } from '../../container/Appt/store/appt.options';
import { ApptServiceScene } from '../../container/Appt/store/appt.types';

export type DateTypeValue = {
  dateType?: PetDetailDateType; // for service
  specificDates?: string[];
  quantityPerDay?: number;
};

export interface DateTypeProps extends Pick<SelectDateTypeProps, 'errorMessage' | 'label' | 'isRequired'> {
  minDate?: Dayjs | string | null;
  maxDate?: Dayjs | string | null;
  value?: DateTypeValue;
  specificDates?: string[];
  onChange?: (v: DateTypeValue) => void;
  showQuantityPerDay?: boolean;
  serviceType?: ServiceType;
  serviceId?: number;
  ownId: string;
  hideDateTypePicker?: boolean;
  serviceItemType?: ServiceItemType;
  mainServiceItemType?: ServiceItemType;
}

export const DateType = memo(
  forwardRef<HTMLDivElement, DateTypeProps>(function DateType(props, ref) {
    const {
      minDate,
      maxDate,
      value,
      specificDates,
      onChange,
      serviceType,
      serviceId,
      ownId,
      showQuantityPerDay: showQuantityPerDayProps = true,
      serviceItemType,
      mainServiceItemType,
      ...rest
    } = props;
    // 比较奇怪的是，为啥 addOn 记录的 serviceItemType 会被视为 appt 的 serviceItemType，可能有 bug
    const [{ serviceItemType: apptServiceItemType }] = useSelector(apptAddOnMapBox.mustGetItem(ownId));
    const isAddon = serviceType === ServiceType.ADDON;
    const [serviceMap] = useSelector(serviceMapBox);
    const service = serviceMap.mustGetItem(serviceId ?? ID_ANONYMOUS);

    /**
     * 两种场景：
     * 1. 不涉及到 main care type 变动的情况：这时候 apptServiceItemType 是当前 appt 的 serviceItemType，没有问题
     * 2. 如果组件涉及 main care type 变动：这时候需要用最新的 mainServiceItemType 来判断是否符合 quantity per day 场景
     */
    const isGrooming = (serviceItemType || apptServiceItemType) === ServiceItemType.GROOMING;
    const isMatchedQuantityPerDayScene = matchApptServiceScene(ApptServiceScene.AddOnSupportQuantityPerDay, {
      mainServiceItemType,
    });
    // 针对没有 require staff 的 addon ，可以选择 addon 次数
    const showQuantityPerDay =
      showQuantityPerDayProps &&
      isAddon &&
      !service.requireDedicatedStaff &&
      (!isGrooming || isMatchedQuantityPerDayScene);

    return (
      <SelectDateType
        {...rest}
        ref={ref}
        showQuantityPerDay={showQuantityPerDay}
        disabledDays={(d) => getDisabledDays(d, { minDate, maxDate, specificDates })}
        value={{
          dateType: value?.dateType,
          specificDates: value?.specificDates?.map((v) => dayjs(v)) || [],
          quantityPerDay: value?.quantityPerDay,
        }}
        onChange={(v) => {
          const firstStartDate = minDate || specificDates?.[0];
          const newSpecificDates = resolveSpecificDates({
            ...v,
            firstStartDate: firstStartDate ? dayjs(firstStartDate).format(DATE_FORMAT_EXCHANGE) : undefined,
          });
          onChange?.({
            dateType: v?.dateType,
            quantityPerDay: v.quantityPerDay,
            specificDates: newSpecificDates.map((v) => v.format(DATE_FORMAT_EXCHANGE)),
          });
        }}
      />
    );
  }),
);
