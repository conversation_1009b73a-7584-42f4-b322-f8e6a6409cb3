import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { Select } from '@moego/ui';
import { type Dayjs } from 'dayjs';
import { isNil } from 'lodash';
import React, { forwardRef, memo, useEffect, useState } from 'react';
import { AddonQuantity } from '../AddonQuantiity/AddonQuantity';
import { CalendarInput } from '../CalendarInput/CalendarInput';
import { DefaultQuantityPerDay } from '../ServiceApplicablePicker/utils/getDefaultService';
import { WithFormError, type WithFormErrorRef } from '../WithFormError/WithFormError';
import { SelectDateTypeOptions } from './DateType.utils';

export interface SelectDateTypeValue {
  dateType?: PetDetailDateType;
  specificDates?: Dayjs[];
  quantityPerDay?: number;
}

export interface SelectDateTypeProps {
  value: SelectDateTypeValue;
  label?: string;
  errorMessage?: string;
  isRequired?: boolean;
  onChange: (v: SelectDateTypeValue) => void;
  disabledDays?: (d: Dayjs) => boolean;
  showQuantityPerDay?: boolean;
  hideDateTypePicker?: boolean;
}

export const SelectDateType = memo(
  forwardRef<WithFormErrorRef, SelectDateTypeProps>((props, ref) => {
    const {
      value,
      label = 'Date(s)',
      showQuantityPerDay = true,
      hideDateTypePicker,
      isRequired,
      errorMessage,
      disabledDays,
      onChange,
    } = props;

    // 组件首次加载时，如果是非法值，则默认选中EVERYDAY
    const [type, setType] = useState<PetDetailDateType | undefined>(() => {
      return SelectDateTypeOptions.some((v) => v.key === value.dateType)
        ? value.dateType
        : PetDetailDateType.PET_DETAIL_DATE_EVERYDAY;
    });

    useEffect(() => {
      !isNil(type) && onChange({ ...value, dateType: type });
    }, [type]);

    return (
      <WithFormError ref={ref} errorMessage={errorMessage}>
        {(!hideDateTypePicker || showQuantityPerDay) && (
          <div className="moe-flex moe-flex-col moe-gap-s moe-relative">
            <div className="moe-grid moe-grid-cols-2 moe-gap-s">
              {!hideDateTypePicker && (
                <Select
                  label={label}
                  isRequired={isRequired}
                  value={String(type)}
                  onChange={(v) => {
                    setType(Number(v));
                  }}
                  className={!showQuantityPerDay ? 'moe-col-span-2' : ''}
                  classNames={{
                    // 文案太长，指定宽度
                    overlay: '!moe-w-[290px]',
                  }}
                >
                  {SelectDateTypeOptions.map((option) => (
                    <Select.Item key={option.key} title={option.label} />
                  ))}
                </Select>
              )}
              {!!showQuantityPerDay && (
                <AddonQuantity
                  isRequired
                  onChange={(val) => {
                    onChange({ ...value, quantityPerDay: val || DefaultQuantityPerDay });
                  }}
                  quantityPerDay={value.quantityPerDay}
                />
              )}
            </div>
            {type === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE && (
              <div className="moe-w-[70%] moe-col-span-2">
                <CalendarInput
                  onlyFuture={false}
                  disabledDate={disabledDays}
                  value={value.specificDates}
                  onChange={(e) => {
                    onChange({ ...value, specificDates: e });
                  }}
                  defaultValue={value.specificDates?.[0]}
                />
              </div>
            )}
          </div>
        )}
      </WithFormError>
    );
  }),
);
