import { FeedingMedicationScheduleDateType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_feeding_medication_enum';
import { Form, Radio, RadioGroup } from '@moego/ui';
import { type Dayjs } from 'dayjs';
import { isNil } from 'lodash';
import React, { forwardRef, memo, useEffect, useState } from 'react';
import { CalendarInput } from '../CalendarInput/CalendarInput';
import { Condition } from '../Condition';
import { WithFormError, type WithFormErrorRef } from '../WithFormError/WithFormError';
import { enumOptions } from '../common/select';
import { SelectFeedingMedicationDateTypeEnum } from './DateType.utils';

export interface SelectFeedingMedicationDateTypeValue {
  dateType?: FeedingMedicationScheduleDateType;
  specificDates?: Dayjs[];
}

export interface SelectFeedingMedicationDateTypeProps {
  value: SelectFeedingMedicationDateTypeValue;
  label?: string;
  errorMessage?: string;
  isRequired?: boolean;
  onChange: (v: SelectFeedingMedicationDateTypeValue) => void;
  disabledDays?: (d: Dayjs) => boolean;
}

export const SelectFeedingMedicationDateType = memo(
  forwardRef<WithFormErrorRef, SelectFeedingMedicationDateTypeProps>((props, ref) => {
    const { value, label = 'Date(s)', isRequired, errorMessage, disabledDays, onChange } = props;

    // 组件首次加载时，如果是非法值，则默认选中everyday_including_checkout_date
    const [type, setType] = useState<FeedingMedicationScheduleDateType | undefined>(() => {
      return SelectFeedingMedicationDateTypeEnum.values.some((v) => v === value.dateType)
        ? value.dateType
        : FeedingMedicationScheduleDateType.EVERYDAY_INCLUDE_CHECKOUT_DATE;
    });

    useEffect(() => {
      !isNil(type) && onChange({ ...value, dateType: type });
    }, [type]);

    const dateTypeOptions = enumOptions(SelectFeedingMedicationDateTypeEnum, true);

    return (
      <WithFormError ref={ref} errorMessage={errorMessage}>
        <div className="moe-flex moe-flex-col moe-gap-s moe-relative">
          <Condition if={label}>
            <Form.Label isRequired={isRequired} className="moe-mb-none">
              {label}
            </Form.Label>
          </Condition>
          <RadioGroup
            value={String(value.dateType)}
            onChange={(v) => {
              setType(Number(v));
            }}
          >
            {dateTypeOptions.map(({ value, label }) => (
              <Radio value={String(value)} key={value}>
                {label}
              </Radio>
            ))}
          </RadioGroup>
          <Condition if={value.dateType === FeedingMedicationScheduleDateType.SPECIFIC_DATE}>
            <div className="moe-w-[70%]">
              <CalendarInput
                onlyFuture={false}
                disabledDate={disabledDays}
                value={value.specificDates}
                onChange={(e) => {
                  onChange({ ...value, specificDates: e });
                }}
                defaultValue={value.specificDates?.[0]}
              />
            </div>
          </Condition>
        </div>
      </WithFormError>
    );
  }),
);
