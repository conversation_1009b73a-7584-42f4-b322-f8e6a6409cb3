import { MajorChevronDownOutlined } from '@moego/icons-react';
import { Dropdown, Input } from '@moego/ui';
import React, { type ReactElement, forwardRef, memo } from 'react';
import styled from 'styled-components';

export interface DropdownInputProps {
  value?: string;
  placeholder?: string;
  children: ReactElement;
  label?: string;
  isRequired?: boolean;
  renderInputWrapper?: (children: React.ReactElement) => ReactElement<HTMLInputElement, string>;
  onOpenChange?: (val: boolean) => void;
  isOpen?: boolean;
  errorType?: string;
  errorMessage?: string;
}

type ErrorType =
  | 'pattern'
  | 'disabled'
  | 'max'
  | 'maxLength'
  | 'min'
  | 'minLength'
  | 'required'
  | 'value'
  | 'onChange'
  | 'onBlur'
  | 'validate'
  | 'setValueAs'
  | 'shouldUnregister'
  | 'deps'
  | 'valueAsNumber'
  | 'valueAsDate';

const CustomInput = styled(Input)`
  input {
    text-align: left;
  }
`;

/** @deprecated */
export const DropdownInput = memo(
  forwardRef<HTMLInputElement, DropdownInputProps>((props, ref) => {
    const { value, placeholder, children, label, isRequired, onOpenChange, isOpen, errorMessage, errorType } = props;

    return (
      <Dropdown
        classNames={{
          content: 'moe-min-w-[490px]',
          overlay: 'moe-min-w-[540px] !moe-top-[12px] !moe-left-[-12px]',
        }}
        shouldCloseOnBlur
        onOpenChange={onOpenChange}
        isOpen={isOpen}
      >
        <Dropdown.Trigger>
          <CustomInput
            ref={ref}
            label={label}
            isRequired={isRequired}
            readOnly
            value={value || ''}
            placeholder={placeholder}
            size="m"
            suffix={<MajorChevronDownOutlined color="#9b9b9b" />}
            errorType={errorType as ErrorType}
            errorMessage={errorMessage}
          />
        </Dropdown.Trigger>
        {children}
      </Dropdown>
    );
  }),
);
