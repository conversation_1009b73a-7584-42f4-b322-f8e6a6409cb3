import { Markup, type OptionValue, cn } from '@moego/ui';
import React, { Fragment } from 'react';
import { memo } from '../../utils/react';
import { Condition } from '../Condition';

/** @deprecated */
export type optionsType = {
  title?: React.ReactNode;
  value: OptionValue;
  disabled?: boolean;
  selected?: boolean;
  [key: string]: any;
};

/** @deprecated */
export type listType<T> = {
  label: string;
  options: T[];
};

/** @deprecated */
export type optionsListType<T> = listType<T>[];

export interface DropdownSelectorProps<T> {
  onClick?: React.MouseEventHandler<HTMLTableRowElement>;
  onSelect?: (value?: T) => void;
  customSelectItemRender?: (options: T, index: number, isLastOne?: boolean) => React.ReactNode;
  optionsList: optionsListType<T>;
}

/** @deprecated use `Select` of moego ui */
export const DropdownSelector = memo(function DropdownSelector<T>(props: DropdownSelectorProps<T>) {
  const { onSelect, onClick, customSelectItemRender, optionsList } = props;

  const handleItemSelect = (e: React.MouseEvent<HTMLTableRowElement, MouseEvent>, item: T, disabled?: boolean) => {
    if (!disabled) {
      onSelect?.(item);
      onClick?.(e);
    }
  };

  return (
    <table className="moe-w-full">
      <tbody>
        {optionsList.map((option, index) => {
          const { label, options } = option;
          return (
            <Fragment key={index}>
              <Condition if={label}>
                <tr className="category">
                  <td
                    className={cn([
                      'name moe-p-spacing-xs moe-text-tertiary moe-leading-[24px]',
                      {
                        'moe-border-solid moe-border-t moe-border-gray-100 moe-pt-[12px]': index !== 0,
                      },
                    ])}
                    colSpan={3}
                  >
                    {label}
                  </td>
                </tr>
              </Condition>

              {customSelectItemRender &&
                options.map((item, i) => customSelectItemRender(item, i, options?.length - 1 === i))}

              <Condition if={!customSelectItemRender}>
                {options.map((item, i) => {
                  const { title, disabled, selected } = item as T & optionsType;
                  return (
                    <tr key={i} onClick={(e) => handleItemSelect(e, item, disabled)}>
                      <td
                        colSpan={3}
                        className={cn([
                          'moe-px-spacing-xs',
                          { 'moe-pl-[8px]': index === 0 },
                          { 'moe-pb-spacing-s': i === options?.length - 1 },
                        ])}
                      >
                        <Markup
                          as="div"
                          variant="small"
                          className={cn([
                            'moe-leading-[24px] moe-cursor-pointer moe-font-normal moe-text-primary hover:moe-bg-brand-subtle',
                            {
                              'moe-bg-brand-subtle moe-font-bold': selected,
                              'moe-opacity-[0.3] moe-bg-[rgb(42, 45, 52)] moe-cursor-not-allowed': disabled,
                            },
                          ])}
                        >
                          {title}
                        </Markup>
                      </td>
                    </tr>
                  );
                })}
              </Condition>
            </Fragment>
          );
        })}
      </tbody>
    </table>
  );
});
