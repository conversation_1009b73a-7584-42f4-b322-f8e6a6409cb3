import { Tabs } from '@moego/ui';
import React, { type ReactElement, type Ref, forwardRef } from 'react';
import { memo } from '../../utils/react';
import { DropdownInput } from './DropdownInput';
import { DropdownSelector, type optionsListType } from './DropdownSelector';

export interface DropdownMenuProps<T> {
  onTabChange?: (index: string | number) => void;
  dropdownItemList: {
    tabLabel: string;
    optionsList: optionsListType<T>;
    customSelectItemRender?: (option: T, index: number, isLastOne?: boolean) => React.ReactNode;
  }[];
  label?: string;
  isRequired?: boolean;
  onClick?: React.MouseEventHandler<HTMLTableRowElement>;
  onSelect?: (value?: T) => void;
  customSelectRender?: (option: T, index: number) => React.ReactNode;
  value?: string;
  onOpenChange?: (val: boolean) => void;
  isOpen?: boolean;
  errorType?: string;
  errorMessage?: string;
}

/**
 * @deprecated use `Select` of moego ui
 */
export const DropdownMenu = memo(
  forwardRef(<T,>(props: DropdownMenuProps<T>, ref: Ref<HTMLInputElement>) => {
    const {
      dropdownItemList,
      onTabChange,
      label,
      isRequired,
      onSelect,
      onClick,
      customSelectRender,
      value = '',
      onOpenChange,
      isOpen,
      errorType,
      errorMessage,
    } = props;

    if (!dropdownItemList?.length) return null;

    return (
      <DropdownInput
        value={value}
        label={label}
        isRequired={isRequired}
        ref={ref}
        onOpenChange={onOpenChange}
        isOpen={isOpen}
        errorType={errorType}
        errorMessage={errorMessage}
      >
        <div className="moe-p-spacing-xs">
          <Tabs
            onChange={onTabChange}
            orientation="horizontal"
            classNames={{
              panel: 'moe-p-0 moe-h-[280px] moe-overflow-y-scroll moe-mx-[-8px] moe-pb-[8px]',
              base: 'moe-px-spacing-xs',
            }}
          >
            {dropdownItemList.map((item, index) => {
              const { tabLabel, optionsList, customSelectItemRender } = item;
              return (
                <Tabs.Item label={tabLabel} key={index}>
                  <DropdownSelector<T>
                    optionsList={optionsList}
                    onSelect={onSelect}
                    onClick={onClick}
                    customSelectItemRender={customSelectItemRender || customSelectRender}
                  />
                </Tabs.Item>
              );
            })}
          </Tabs>
        </div>
      </DropdownInput>
    );
  }) as <T>(p: DropdownMenuProps<T>, ref: Ref<HTMLInputElement>) => ReactElement,
);
