import { Dropdown } from 'antd';
import { type DropDownProps } from 'antd/es/dropdown';
import classNames from 'classnames';
import { debounce } from 'lodash';
import React, { type CSSProperties, useCallback, useMemo } from 'react';
import { type ColorResult, SketchPicker } from 'react-color';
import IconIconTrianglePng from '../../assets/icon/icon-triangle.png';
import { useBool } from '../../utils/hooks/useBool';
import { Condition } from '../Condition';
import { MoeLoading } from '../Loading/Loading';

export const DEFAULT_COLOR_CODE = '#000000';

export const correctColorCode = (colorCode: string | number | undefined | null) => {
  // '0' is the default value in database
  if (typeof colorCode !== 'string' || colorCode.trim() === '' || colorCode === '0') {
    colorCode = DEFAULT_COLOR_CODE;
  }
  return colorCode;
};

/** 默认可选的颜色 */
export const DEFAULT_PRESET_COLORS = [
  '#000000',
  '#ff9800',
  '#3949ab',
  '#b8e886',
  '#00838f',
  '#ec407a',
  '#ffee58',
  '#36cfc9',
  '#8b572a',
  '#7bed00',
  '#008fdd',
  '#fe8862',
  '#6927ff',
  '#ff39e8',
  '#93ddff',
  '#bf81fe',
  '#fee6eb',
  '#c40018',
];

export interface ColorCodePicker {
  className?: string;
  value?: string;
  onChange?: (color: string) => void;
  size?: number;
  presetColors?: string[];
  loading?: boolean;
  disabled?: boolean;
}

export function ColorCodePicker(props: ColorCodePicker) {
  const { className, value, onChange, presetColors, size = 24, loading, disabled = false } = props;
  const visible = useBool();
  const presetColorArr = useMemo(() => {
    if (!Array.isArray(presetColors) || presetColors.length === 0) {
      return DEFAULT_PRESET_COLORS;
    }
    return presetColors;
  }, [presetColors]);

  const onChangeColorPicker = useCallback(
    debounce(
      (colorResult: ColorResult) => {
        onChange?.(colorResult.hex);
        visible.close();
      },
      300,
      {
        leading: false,
        trailing: true,
      },
    ),
    [],
  );

  const handleChangeVisible = (visibleInput: boolean) => {
    if (disabled) return;
    visible.as(visibleInput);
  };

  const roundStyle = useMemo(() => {
    const background: string = value || presetColorArr?.[0] || DEFAULT_COLOR_CODE;
    return { width: size, height: size, background: correctColorCode(background) } as CSSProperties;
  }, [size, value, presetColorArr]);
  const trigger = useMemo(() => ['click'] as DropDownProps['trigger'], []);

  return (
    <Dropdown
      trigger={trigger}
      visible={visible.value}
      onVisibleChange={handleChangeVisible}
      overlay={<SketchPicker presetColors={presetColorArr} color={value} onChange={onChangeColorPicker} />}
    >
      <div
        className={classNames(
          'moe-relative moe-flex moe-items-center moe-justify-center moe-rounded-full',
          {
            'moe-cursor-pointer': !disabled,
          },
          className,
        )}
        style={roundStyle}
      >
        <img src={IconIconTrianglePng} className="moe-w-[15px]" />
        <Condition if={loading}>
          <MoeLoading />
        </Condition>
      </div>
    </Dropdown>
  );
}
