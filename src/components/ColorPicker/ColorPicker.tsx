import { cn } from '@moego/ui';
import classNames from 'classnames';
import React, { type CSSProperties, useState } from 'react';
import { type ColorResult, SketchPicker } from 'react-color';
import IconIconTrianglePng from '../../assets/icon/icon-triangle.png';
import { ColorPickerWrapper } from './ColorPicker.style';

interface ColorPickerProps {
  className?: string;
  hoverShow?: boolean;
  clickShow?: boolean;
  presetColor?: string[];
  height?: string;
  width?: string;
  onChange?: (color: string) => void;
  value?: string;
  popPosition?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  onClosePop?: () => void;
  onClick?: () => void;
  disabled?: boolean;
  /**
   * dealing with color picker popup on a fixed popup
   */
  fixedPosition?: boolean;
}

interface IOffset {
  x: number;
  y: number;
}

export const ColorPicker = React.forwardRef<HTMLDivElement, ColorPickerProps>((props, ref) => {
  const {
    className,
    hoverShow = false,
    clickShow = true,
    presetColor,
    value,
    height = '30px',
    width = '30px',
    onChange,
    popPosition = 'bottom-right',
    onClosePop,
    onClick,
    fixedPosition,
    disabled,
  } = props;
  const [visible, setVisible] = useState(false);
  const [popOffset, setPopOffset] = useState({
    x: 0,
    y: 0,
  } as IOffset);

  let presetColorArr = presetColor;

  const handleClose = () => {
    if (onClosePop) {
      onClosePop();
    }
    setVisible(false);
  };

  const handleClickColorPicker = (e: React.MouseEvent) => {
    onClick && onClick();
    if (!clickShow) {
      return;
    }
    setVisible(!visible);
    let popPositionX = 0;
    let popPositionY = 0;
    switch (popPosition) {
      case 'bottom-left':
        popPositionY = 0;
        popPositionX = -223;
        break;
      case 'bottom-right':
        popPositionY = 0;
        popPositionX = 0;
        break;
      case 'top-left':
        popPositionY = -317;
        popPositionX = -223;
        break;
      case 'top-right':
        popPositionY = -317;
        popPositionX = 0;
        break;
      default:
        break;
    }
    setPopOffset({
      x: e.clientX + popPositionX,
      y: e.clientY + popPositionY,
    });
  };

  const handleHoverColorPicker = (status: boolean) => {
    if (!hoverShow) {
      return;
    }
    setVisible(status);
  };

  const handleColorPickerOnChange = (colorResult: ColorResult) => {
    onChange?.(colorResult.hex);
  };

  const classes = classNames('circle-wrapper', className);

  if (!Array.isArray(presetColorArr) || presetColorArr.length === 0) {
    presetColorArr = [
      '#000000',
      '#ff9800',
      '#3949ab',
      '#b8e886',
      '#00838f',
      '#ec407a',
      '#ffee58',
      '#36cfc9',
      '#8b572a',
      '#7bed00',
      '#008fdd',
      '#fe8862',
      '#6927ff',
      '#ff39e8',
      '#93ddff',
      '#bf81fe',
      '#fee6eb',
      '#c40018',
    ];
  }

  const cover = {
    position: 'fixed',
    top: '0px',
    right: '0px',
    bottom: '0px',
    left: '0px',
  } as CSSProperties;

  const popClasses = classNames('picker-wrapper', {
    // [`picker-wrapper-${popPosition}`]: popPosition
  });

  return (
    <ColorPickerWrapper ref={ref}>
      <div
        className={cn(classes, disabled ? 'moe-pointer-events-none' : '')}
        onClick={handleClickColorPicker}
        onMouseEnter={() => handleHoverColorPicker(true)}
        onMouseLeave={() => handleHoverColorPicker(false)}
        style={{
          height,
          width,
          background: value || presetColorArr[0] || '#000',
        }}
      >
        <img src={IconIconTrianglePng} alt="#" className="icon-triangle" />
      </div>
      {visible ? (
        <>
          <div style={cover} onClick={handleClose} />
          <div
            className={popClasses}
            style={
              fixedPosition
                ? {}
                : {
                    top: popOffset.y,
                    left: popOffset.x,
                  }
            }
          >
            <SketchPicker
              presetColors={presetColorArr}
              color={value}
              onChange={(colorResult) => handleColorPickerOnChange(colorResult)}
            />
          </div>
        </>
      ) : null}
    </ColorPickerWrapper>
  );
});
