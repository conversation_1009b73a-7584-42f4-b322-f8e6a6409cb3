import { type CustomerPackageView } from '@moego/api-web/moego/api/appointment/v1/appointment_view';
import { MajorPackageOutlined, MinorPackageOutlined } from '@moego/icons-react';
import { Tooltip, type TooltipProps } from '@moego/ui';
import React, { memo } from 'react';
import { PackageTestIds } from '../../config/testIds/package';
import { CustomerPackages, checkPackageValid } from './CustomerPackages';

export interface PackagesPopoverProps extends Omit<TooltipProps, 'content' | 'children'> {
  packages: CustomerPackageView[];
  iconSize?: 'minor' | 'major';
}

export const CustomerPackagesWithTooltip = memo<PackagesPopoverProps>((props) => {
  const { packages, iconSize = 'minor', ...rest } = props;
  const hasSomeValidPackages = packages?.length && packages.some(checkPackageValid);
  if (!hasSomeValidPackages) {
    return null;
  }
  const isMinorIcon = iconSize === 'minor';
  const Icon = isMinorIcon ? MinorPackageOutlined : MajorPackageOutlined;

  return (
    <Tooltip
      backgroundTheme="light"
      classNames={{
        container: 'moe-min-w-[320px] moe-max-w-[400px] moe-max-h-[320px]',
      }}
      content={<CustomerPackages packages={packages} className="moe-max-h-[240px]" />}
      {...rest}
    >
      <div className="moe-items-center moe-flex moe-cursor-pointer">
        <Icon className="!moe-text-[20px]" data-testid={PackageTestIds.CustomerPackageIcon} />
      </div>
    </Tooltip>
  );
});
