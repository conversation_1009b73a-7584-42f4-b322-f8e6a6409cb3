import {
  type CustomerPackageView,
  type CustomerPackageViewPackageDetail,
} from '@moego/api-web/moego/api/appointment/v1/appointment_view';
import { Heading, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { longPackageListFormatter } from '../../store/packages/utils';
import { isAfter, isToday } from '../../utils/DateTimeUtil';
import { dateMessageToString } from '../../utils/utils';

interface CustomerPackagesProps {
  packages: CustomerPackageView[];
  className?: string;
}

export function checkPackageValid(packageInfo: CustomerPackageView) {
  const { packageDetails, expirationDate } = packageInfo;
  if (!packageDetails.length) return false;
  // if not set expiration date, it is valid
  if (!expirationDate) return true;

  return isToday(dayjs(dateMessageToString(expirationDate))) || isAfter(dayjs(dateMessageToString(expirationDate)));
}

export const CustomerPackages: React.FC<CustomerPackagesProps> = ({ packages, className }) => {
  return (
    <div className="moe-flex moe-flex-col">
      <Heading size="5">Packages</Heading>
      <div
        className={cn(
          'moe-mt-8px-200 moe-overflow-y-auto moe-flex moe-flex-col moe-gap-y-8px-200 moe-max-h-[400px]',
          className,
        )}
      >
        {packages.filter(checkPackageValid).map((pkg) => (
          <PackageItem key={pkg.packageId} packageInfo={pkg} />
        ))}
      </div>
    </div>
  );
};

function PackageItem({ packageInfo }: { packageInfo: CustomerPackageView }) {
  const { packageName, packageDetails, expirationDate, startDate } = packageInfo;
  const [business] = useSelector(selectCurrentBusiness());

  const getExpirationDate = () => {
    if (!expirationDate) return 'No expiration';
    const date = [startDate, expirationDate].map((date) => business.formatDate(dateMessageToString(date)));
    return date.join(' - ');
  };

  const renderPkgDetail = (service: CustomerPackageViewPackageDetail) => {
    const serviceNameList = longPackageListFormatter(service.services?.map((item) => item.name));
    return (
      <div className="moe-flex moe-justify-between moe-items-center">
        <div className="moe-mr-[8px]">{serviceNameList}</div>
        <div className="moe-flex-shrink-0">
          <span
            className={cn({
              'moe-text-success': service.remainingQuantity > 0,
              'moe-text-[#F3413B]': service.remainingQuantity === 0,
            })}
          >
            {service.remainingQuantity}
          </span>
          {' available'}
        </div>
      </div>
    );
  };

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xxs">
      <Heading size="6" className="moe-truncate">
        {packageName}
      </Heading>
      <span className="moe-text-xs moe-text-tertiary">{getExpirationDate()}</span>
      {packageDetails.map((detail) => (
        <React.Fragment key={detail.serviceId}>{renderPkgDetail(detail)}</React.Fragment>
      ))}
    </div>
  );
}
