import { Checkbox as AndCheckbox } from 'antd';
import { type CheckboxProps } from 'antd/es/checkbox';
import React from 'react';
import styled from 'styled-components';

const CheckboxStyled = styled(AndCheckbox)`
  .ant-checkbox-inner,
  .ant-checkbox-checked::after {
    border-radius: 4px;
  }
  > span:nth-child(2) {
    user-select: none;
  }

  &.purple {
    .ant-checkbox-checked::after {
      display: none;
    }
    .ant-checkbox-checked {
      .ant-checkbox-inner {
        background-color: #9c68ff !important;
        border-color: #9c68ff !important;
      }
    }
    .ant-checkbox-inner {
      background-color: #fff;
      border-color: #9c68ff;
    }
  }

  &.grey {
    .ant-checkbox-checked::after {
      display: none;
    }
    .ant-checkbox-checked {
      .ant-checkbox-inner {
        background-color: #828282 !important;
        border-color: #828282 !important;
      }
    }
    .ant-checkbox-inner {
      background-color: #fff;
      border-color: #828282;
    }
  }
`;

export const Checkbox = (props: CheckboxProps) => <CheckboxStyled {...props} />;

Checkbox.Group = AndCheckbox.Group;
Checkbox.__ANT_CHECKBOX = true;
