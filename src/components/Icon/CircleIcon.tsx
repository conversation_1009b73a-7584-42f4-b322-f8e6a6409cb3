import { Spin, Tooltip, type TooltipImplProps, cn } from '@moego/ui';
import React from 'react';

export interface CircleIconProps extends TooltipImplProps {
  iconClassName?: string;
  loading?: boolean;
}

export const CircleIcon = function CircleIcon(props: React.PropsWithChildren<CircleIconProps>) {
  const { children, iconClassName, loading, ...toolTipProps } = props;

  return (
    <Tooltip side="top" delay={0} {...toolTipProps}>
      <div
        className={cn(
          'moe-w-[32px] moe-h-[32px] moe-flex moe-items-center moe-justify-center moe-rounded-full hover:moe-bg-[#E6E6E6]',
          iconClassName,
        )}
      >
        {loading ? <Spin isLoading size="s" /> : children}
      </div>
    </Tooltip>
  );
};
