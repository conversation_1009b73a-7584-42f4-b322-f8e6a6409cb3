import { Text } from '@moego/ui';
import classNames from 'classnames';
import React from 'react';
import IconModernText from '../../assets/svg/icon-modern-text.svg';
import { ApptTestIds } from '../../config/testIds/apptDrawer';
import { memoForwardRef } from '../../utils/react';
import { Condition } from '../Condition';
import { SvgIcon } from './Icon';

export interface IconModernTextNumProps extends React.HTMLAttributes<HTMLDivElement> {
  count?: number;
  showCount?: boolean;
}

export const IconModernTextNum = memoForwardRef<HTMLDivElement, IconModernTextNumProps>(
  (props: IconModernTextNumProps, ref) => {
    const { count, showCount, ...domProps } = props;

    return (
      <div
        ref={ref}
        {...domProps}
        className={classNames('moe-flex moe-items-center moe-gap-x-[4px] moe-cursor-pointer', domProps.className)}
      >
        <SvgIcon src={IconModernText} size={20} color="moe-text-primary" data-testid={ApptTestIds.ApptPetNoteIcon} />
        <Condition if={showCount && typeof count === 'number'}>
          <Text variant="caption" className="moe-text-primary moe-whitespace-nowrap moe-font-medium">
            {`(${count})`}
          </Text>
        </Condition>
      </div>
    );
  },
);
