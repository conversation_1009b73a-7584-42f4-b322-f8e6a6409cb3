import React from 'react';
import ImageLogoPng from '../../assets/image/logo.png';
import { ImgIcon, type ImgIconProps } from './Icon';

interface LogoProps extends ImgIconProps {
  className?: string;
}

export const Logo = (props: LogoProps) => {
  return <ImgIcon {...props} src={ImageLogoPng} />;
};

export const LeftNavLogo = () => {
  return (
    <div className="moe-w-8px-600 moe-h-8px-600 moe-rounded-8px-150 moe-bg-white moe-flex moe-items-center moe-justify-center">
      <Logo width={24} />
    </div>
  );
};
