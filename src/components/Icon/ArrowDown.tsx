import React from 'react';
import { Motion, spring } from 'react-motion';
import ArrowDown from '../../assets/svg/icon-arrow.svg';
import { SvgIcon, type SvgIconProps } from './Icon';

export interface ArrowDownIconProps extends Partial<SvgIconProps> {
  className?: string;
  /** 是否箭头向上 */
  reverse?: boolean;
}

export function ArrowDownIcon(props: ArrowDownIconProps) {
  const { reverse, ...iconProps } = props;
  return (
    <Motion defaultStyle={{ rotate: 0 }} style={{ rotate: spring(reverse ? -180 : 0, { stiffness: 300 }) }}>
      {(style) => <SvgIcon src={ArrowDown} {...iconProps} style={{ transform: `rotate(${style.rotate}deg)` }} />}
    </Motion>
  );
}
