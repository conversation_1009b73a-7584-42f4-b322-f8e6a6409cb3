import { Checkbox } from 'antd';
import { type CheckboxProps } from 'antd/lib/checkbox';
import styled from 'styled-components';

export const StyledCheckboxGroup = styled(Checkbox.Group)`
  display: flex;
  flex-direction: column;
  .checkbox-wrapper {
    &:not(:last-of-type) {
      margin-bottom: 12px;
    }
    .ant-checkbox-wrapper {
      margin: 0;
      color: #333;
      font-weight: 500;

      .ant-checkbox::after,
      .ant-checkbox-checked::after {
        display: none;
      }
    }
  }

  .ant-checkbox + span {
    padding-right: 0;
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    border: 1px solid var(--moe-color-border-brand);
  }
  .ant-checkbox-inner {
    border-radius: 4px;
    border: 1px solid #cdcdcd;
  }
`;

export const StyledCheckboxWrap = styled.div`
  .ant-checkbox-wrapper {
    margin: 0;
    color: #333;
    font-weight: 500;

    .ant-checkbox::after,
    .ant-checkbox-checked::after {
      display: none;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      border: 1px solid var(--moe-color-border-brand);
    }

    .ant-checkbox-inner {
      border-radius: 4px;
      border: 1px solid #cdcdcd;

      &::after {
        transition: none;
      }
    }
  }
`;

export const StyledCheckbox: React.ForwardRefExoticComponent<CheckboxProps & React.RefAttributes<HTMLInputElement>> =
  styled(Checkbox)`
  .ant-checkbox-checked .ant-checkbox-inner {
    border: 1px solid var(--moe-color-border-brand);
  }
  .ant-checkbox-inner {
    border-radius: 4px;
    border: 1px solid #cdcdcd;
  }
  .ant-checkbox-checked::after {
    border-radius: 4px;
  }
`;
