import { type OptionsType } from 'rc-select/lib/interface';
import { type ID } from '../../types/common';

/**
 * 获取多选框的label
 * - 只选中一个展示选中的label
 * - 选中多个展示选中的个数
 * - 全选展示一个自定义文案
 * @param param0
 * @returns
 */
export const getMultiSelectLabel = ({
  options,
  values,
  selectAllLabel,
}: {
  options: OptionsType;
  values?: ID[];
  selectAllLabel?: string;
}) => {
  if (!options.length) {
    return (values?.length ?? 0) > 1 ? values?.length : values?.[0];
  }

  const selectedOptionList = options.filter((item) => values?.includes(item.value));
  if (selectedOptionList.length > 1) {
    if (selectedOptionList.length === options.length) {
      return selectAllLabel || selectedOptionList.length;
    }
    return selectedOptionList.length;
  }
  return selectedOptionList[0]?.label;
};
