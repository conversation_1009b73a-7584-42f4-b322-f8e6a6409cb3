import { type CheckboxProps } from 'antd/lib/checkbox';
import React, { memo, type ReactNode } from 'react';
import { useBool } from '../../utils/hooks/useBool';
import { Condition } from '../Condition';
import { StyledCheckbox } from './Checkbox.style';

export interface ToggleInputCheckboxProps extends CheckboxProps {
  formItemName?: string;
  checkboxLabel: ReactNode;
  renderInput?: () => ReactNode;
}

export const ToggleInputCheckbox = memo<ToggleInputCheckboxProps>(
  ({ checkboxLabel, formItemName, children, checked, renderInput, ...restCheckboxProps }) => {
    const checkedState = useBool(checked);

    const isChecked = typeof checked === 'undefined' ? checkedState.value : checked;

    return (
      <>
        <StyledCheckbox
          className="!moe-block"
          checked={isChecked}
          onChange={(e) => {
            checkedState.as(e.target.checked);
          }}
          {...restCheckboxProps}
        >
          {checkboxLabel}
        </StyledCheckbox>
        <Condition if={isChecked}>{renderInput}</Condition>
      </>
    );
  },
);
