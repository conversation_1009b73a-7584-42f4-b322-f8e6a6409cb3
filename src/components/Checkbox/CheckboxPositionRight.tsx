import { Checkbox, type CheckboxProps, cn, mergeClassNames } from '@moego/ui';
import React from 'react';
import { memoForwardRef } from '../../utils/react';

export interface CheckboxPositionRightProps extends CheckboxProps {
  editable: boolean;
  disabled: boolean;
}

export const CheckboxPositionRight = memoForwardRef<HTMLLabelElement, CheckboxPositionRightProps>((props, ref) => {
  const { editable, disabled, children, classNames, className, ...restProps } = props;

  return (
    <Checkbox
      ref={ref}
      isDisabled={disabled}
      className={cn('moe-flex-1', className)}
      classNames={mergeClassNames(
        {
          base: 'moe-max-w-full',
          content: 'moe-ml-none',
          wrapper: 'moe-items-center moe-justify-between moe-flex-row-reverse',
          iconWrapper: cn({
            'moe-invisible': !editable,
          }),
        },
        classNames,
      )}
      {...restProps}
    >
      {children}
    </Checkbox>
  );
});
