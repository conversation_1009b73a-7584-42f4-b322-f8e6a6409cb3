import React, { memo } from 'react';

export interface NewBadgeProps {
  className?: string;
}

export const NewBadge = memo(({ className = '' }: NewBadgeProps) => {
  return (
    <span
      className={`moe-h-[20px] moe-px-[6px] moe-bg-brand-bold moe-rounded-[20px] moe-text-[12px] moe-font-[700] moe-leading-[16px] moe-text-white moe-z-[1] moe-flex moe-justify-center moe-items-center ${className}`}
    >
      New
    </span>
  );
});
