import { type CustomerPackageView } from '@moego/api-web/moego/api/appointment/v1/appointment_view';
import { Button, Tag, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { memo, useMemo } from 'react';
import SvgMinorEditOutlinedSvg from '../../assets/svg/minor-edit-outlined.svg';

// TODO<vision,p1> 这个难顶
import { CofStatus } from '../../container/Payment/components/CofStatus';
import { type ClientDetailQuery } from '../../router/paths';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { selectCurrentPermissions, selectViewClientListPermission } from '../../store/business/role.selectors';
import { type CustomerRecord } from '../../store/customer/customer.boxes';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../utils/reportType';
import { transformFrequencyToString } from '../../utils/transformFrequencyToString';
import { Avatar } from '../Avatar/Avatar';
import { CustomerPackagesWithTooltip } from '../CustomerPackages/CustomerPackagesWithTooltip';
import { SvgIcon } from '../Icon/Icon';
import { Loading } from '../Loading/Loading';
import { MembershipIdentify } from '../MembershipIdentify/MembershipIdentify';
import { ClientInsights } from './ClientInsights';
import { ClientNotes } from './ClientNotes';
import { ClientTags } from './ClientTags';

export interface ClientInfoSummaryCardProps {
  className?: string;
  client: CustomerRecord;
  loading?: boolean;
  go2ClientDetail?: (state?: ClientDetailQuery | undefined) => void;
  go2ClientNotes?: () => void;
  onEditClient?: () => void;
  before?: React.ReactNode;
  after?: React.ReactNode;
  style?: React.CSSProperties;
  isDeleted?: boolean;
  fallbackClientName?: string;
  ticketId?: number;
  displayClientTags?: boolean;
  clientPackages?: CustomerPackageView[];
}

export const ClientInfoSummaryCard = memo<ClientInfoSummaryCardProps>(function ClintInfoCard(props) {
  const {
    isDeleted,
    className,
    client,
    loading,
    go2ClientDetail,
    go2ClientNotes,
    onEditClient,
    after,
    before,
    style,
    fallbackClientName,
    displayClientTags = true,
    clientPackages = [],
  } = props;
  const [business, permissions, canViewClientList] = useSelector(
    selectCurrentBusiness,
    selectCurrentPermissions(),
    selectViewClientListPermission,
  );
  const canEdit = !!(onEditClient && canViewClientList);
  const { clientColor, preferredFrequencyDay, preferredFrequencyType } = client;
  const clientNameStyle = useMemo(() => {
    const hasClientColor = typeof clientColor === 'string' && clientColor.length > 0;
    return { color: hasClientColor ? clientColor : '#333' };
  }, [clientColor]);
  const frequencyText = transformFrequencyToString(preferredFrequencyDay, preferredFrequencyType);
  const canViewClientProfile = permissions.has('viewIndividualClientProfile');

  const onStopPropagation = useLatestCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  });
  const onClickClient = useLatestCallback(() => {
    if (!canInteractClient) {
      return;
    }
    go2ClientDetail?.();
  });
  const canInteractClient = (!!go2ClientDetail || !!isDeleted) && canViewClientProfile;
  const activeClient = !isDeleted;

  return (
    <div
      className={classNames(
        'moe-group moe-rounded-m moe-bg-[#fff] moe-transition-all',
        canEdit && 'hover:moe-bg-[#F2F3F6] moe-cursor-pointer',
        className,
      )}
      onClick={canEdit ? onEditClient : undefined}
      style={{ boxShadow: '0px 4px 12px 0px rgba(0, 0, 0, 0.06)', ...style }}
    >
      <div className="moe-p-[16px] moe-flex moe-flex-col moe-gap-y-s">
        {before}
        <div className="moe-flex moe-justify-between moe-gap-x-m">
          <div className="moe-flex moe-gap-x-[16px] moe-flex-1 moe-min-w-0">
            <Avatar
              className={classNames('!moe-flex-shrink-0', !canInteractClient && '!moe-cursor-default')}
              src={client.avatarPath}
              size="48px"
              info={client}
              onClick={(e) => {
                e.stopPropagation();
                onClickClient();
              }}
            />
            <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col">
              <div className="moe-flex moe-items-center moe-justify-between moe-gap-x-[8px]">
                <div className="moe-flex moe-items-center moe-gap-x-[8px]">
                  <div
                    className={classNames(
                      'moe-text-[18px] moe-font-bold moe-leading-[24px] moe-truncate moe-max-w-[160px]',
                      isDeleted
                        ? '!moe-text-[#ccc] !moe-cursor-default'
                        : canInteractClient
                          ? 'hover:!moe-text-brand moe-cursor-pointer'
                          : 'hover: moe-cursor-not-allowed',
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      onClickClient();
                    }}
                    style={clientNameStyle}
                  >
                    {isDeleted ? fallbackClientName : client.fullName()}
                  </div>
                  {isDeleted ? (
                    <div className="moe-px-[6px] moe-py-[3px] moe-bg-[#F7F8FA] moe-rounded-full moe-text-[12px] moe-leading-[12px] moe-font-medium moe-text-[#666] moe-whitespace-nowrap moe-cursor-default">
                      Deleted
                    </div>
                  ) : null}
                  {!isDeleted && (
                    <>
                      <MembershipIdentify customerId={String(client.customerId)} />
                      <ClientNotes
                        clientId={client.customerId}
                        maxNotes={2}
                        popOverChildren={(notes) =>
                          go2ClientNotes ? (
                            <div className="moe-flex moe-items-center moe-gap-x-[16px] moe-justify-start">
                              {notes.length > 2 && (
                                <Button
                                  variant="tertiary-legacy"
                                  data-action={ReportActionName.CalendarClientNoteViewAll}
                                  onPress={go2ClientNotes}
                                  size="s"
                                  align="start"
                                >
                                  {`View all (${notes.length})`}
                                </Button>
                              )}
                              <Button
                                variant="tertiary-legacy"
                                data-action={ReportActionName.CalendarClientNoteEdit}
                                onPress={go2ClientNotes}
                                size="s"
                                align="start"
                                className="moe-min-w-[45px]"
                              >
                                Edit
                              </Button>
                            </div>
                          ) : null
                        }
                      />
                      {!loading ? (
                        <>
                          <div className="moe-flex" onClick={onStopPropagation}>
                            <CofStatus customerId={client.customerId} source="quick-add-drawer" isMajorIcon />
                          </div>
                          <CustomerPackagesWithTooltip packages={clientPackages} iconSize="major" />
                        </>
                      ) : null}

                      {client.isProspectCustomer ? (
                        <div
                          className="moe-flex-shrink-0 moe-bg-[#fff7e8] moe-px-[6px] moe-py-[3px] moe-text-[#faad14] moe-text-[12px] moe-font-bold moe-leading-[14px] moe-rounded-[32px] moe-cursor-default"
                          onClick={onStopPropagation}
                        >
                          Prospect
                        </div>
                      ) : (
                        client.isNewCustomer && (
                          <Tag
                            label="New"
                            color="success"
                            isBordered={false}
                            onClick={onStopPropagation}
                            className="moe-min-w-[44px]"
                          />
                        )
                      )}

                      <Tooltip side="top" content={`Grooming frequency: ${frequencyText}`}>
                        <Tag
                          label={frequencyText}
                          color="neutral"
                          onClick={onStopPropagation}
                          className="moe-max-w-[90px] moe-flex-shrink-0"
                        />
                      </Tooltip>
                    </>
                  )}
                </div>
                {after && !isDeleted ? <div>{after}</div> : null}
              </div>
              {!loading && (
                <div
                  className="moe-flex moe-items-center moe-gap-x-[8px] moe-cursor-default"
                  onClick={onStopPropagation}
                >
                  <div
                    className={classNames(
                      'moe-text-[14px] moe-font-medium moe-leading-[18px] moe-cursor-default rr-mask',
                      isDeleted ? 'moe-text-[#ccc]' : 'moe-text-[#999]',
                    )}
                  >
                    {business.formatPhoneNumber(client.phoneNumber)}
                  </div>
                </div>
              )}
            </div>
            {loading ? (
              <div className="moe-w-[20px]">
                <Loading loading indicatorStyle={{ fontSize: 14 }} />
              </div>
            ) : (
              canEdit && (
                <div className="moe-w-[20px]">
                  <Tooltip content="Change client">
                    <SvgIcon
                      src={SvgMinorEditOutlinedSvg}
                      size={20}
                      className="!moe-ml-0 !moe-text-[#202020] group-hover:!moe-block !moe-hidden"
                    />
                  </Tooltip>
                </div>
              )
            )}
          </div>
        </div>
        {!loading && activeClient && displayClientTags ? (
          <ClientTags clientId={client.customerId} max={4} onClick={onStopPropagation}>
            {(tag, tagNode) => {
              const hasAnyTag = tag.length > 0;
              return (
                <ClientInsights clientId={client.customerId} onClick={onStopPropagation}>
                  {(hasAnyInsight, insightNode) => {
                    return (
                      <>
                        {hasAnyTag || hasAnyInsight ? (
                          <div className="moe-flex moe-flex-col moe-gap-xs">
                            <div className="empty:moe-hidden">{tagNode}</div>
                            <div className="empty:moe-hidden">{insightNode}</div>
                          </div>
                        ) : null}
                      </>
                    );
                  }}
                </ClientInsights>
              );
            }}
          </ClientTags>
        ) : null}
      </div>
    </div>
  );
});
