import { MoreOutlined } from '@ant-design/icons';
import { Tag } from '@moego/ui';
import { useSelector } from 'amos';
import { Popover } from 'antd';
import React, { useMemo } from 'react';
import { customerMapBox } from '../../store/customer/customer.boxes';
import { type CustomerTagRecord, customerTagMapBox } from '../../store/customer/customerTag.boxes';

export interface ClientTagsProps {
  clientId: number;
  max?: number;
  onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  children?: (list: CustomerTagRecord[], defaultNode: React.ReactElement | null) => React.ReactElement;
}

export function ClientTags(props: ClientTagsProps) {
  const { clientId, max: maxCount, onClick, children } = props;
  const [client, tagMap] = useSelector(customerMapBox.mustGetItem(clientId), customerTagMapBox);
  const tagList = client.customerTagIdList.map((tagId) => tagMap.mustGetItem(tagId));
  const max = maxCount ?? tagList.length;
  const { list, remainList } = useMemo(() => {
    const list = Array.isArray(tagList) ? tagList : [];
    return {
      list: list.slice(0, max),
      remainList: list.slice(max),
    };
  }, [tagList, max]);

  if (!list.length && !remainList.length) {
    return children ? children(tagList, null) : null;
  }

  const node = (
    <div className="moe-flex moe-items-center moe-flex-wrap moe-gap-[4px]" onClick={onClick}>
      {list.map((tag) => (
        <Tag key={tag.id} variant="filled" color="neutral" label={tag.name} />
      ))}
      {remainList.length ? (
        <Popover
          placement="topRight"
          content={
            <div className="moe-flex moe-items-center moe-flex-wrap moe-gap-[4px]">
              {remainList.map((tag) => (
                <Tag key={tag.id} variant="filled" color="neutral" label={tag.name} />
              ))}
            </div>
          }
          align={{ offset: [12, 0] }}
        >
          <MoreOutlined className="moe-block moe-cursor-pointer" />
        </Popover>
      ) : null}
    </div>
  );

  return children ? children(tagList, node) : node;
}
