import { type CustomerPackageView } from '@moego/api-web/moego/api/appointment/v1/appointment_view';
import { useDispatch } from 'amos';
import React, { useEffect } from 'react';
import { getCustomerActiveSubscriptionList } from '../../store/membership/membership.actions';
import { isNormal } from '../../store/utils/identifier';
import { optionalFunction } from '../../utils/utils';
import { ClientInfoRender } from './ClientInfoRender';
import { ClientInfoSummaryCard, type ClientInfoSummaryCardProps } from './ClientInfoSummaryCard';

export interface ClientInfoCardProps extends Pick<ClientInfoSummaryCardProps, 'go2ClientNotes' | 'after'> {
  className?: string;
  clientId: number;
  loadingClient?: boolean;
  /**
   * onChange 方法是否存在，代表了是否可以修改 client
   */
  onChange?: (v: number) => void;
  customerPackages?: CustomerPackageView[];
}

export function ClientInfoCard(props: ClientInfoCardProps) {
  const { clientId, onChange, loadingClient, after, go2ClientNotes, customerPackages } = props;
  const dispatch = useDispatch();

  useEffect(() => {
    if (isNormal(clientId)) {
      dispatch(getCustomerActiveSubscriptionList(clientId + ''));
    }
  }, [clientId]);

  return (
    <ClientInfoRender clientId={clientId} preventAutoReload>
      {({ client, loading: innerLoading, go2ClientDetail }) => {
        const loading = innerLoading || loadingClient;
        return (
          <ClientInfoSummaryCard
            className="moe-cursor-pointer"
            client={client}
            loading={loading}
            after={after}
            go2ClientDetail={go2ClientDetail}
            go2ClientNotes={go2ClientNotes}
            onEditClient={optionalFunction(() => onChange?.(client.customerId), Boolean(onChange))}
            clientPackages={customerPackages}
          />
        );
      }}
    </ClientInfoRender>
  );
}
