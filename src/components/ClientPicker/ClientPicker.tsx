import { useDispatch } from 'amos';
import React, { useEffect } from 'react';
import { ApptTestIds } from '../../config/testIds/apptDrawer';
import { getCustomerList } from '../../store/customer/customer.actions';
import { isNormal } from '../../store/utils/identifier';
import { useBool } from '../../utils/hooks/useBool';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { ClientInfoCard, type ClientInfoCardProps } from './ClientInfoCard';
import { ClientSearchInput } from './ClientSearchInput';

export interface ClientPickerProps extends ClientInfoCardProps {}

export function ClientPicker(props: ClientPickerProps) {
  const { className, clientId, loadingClient, onChange, customerPackages } = props;
  const dispatch = useDispatch();
  const isOpen = useBool(false);

  const getTop10 = useSerialCallback(async () => {
    await dispatch(getCustomerList({ queries: { keyword: '' }, filters: undefined }));
  });

  useEffect(() => {
    getTop10();
  }, []);

  if (!isNormal(clientId) || isOpen.value) {
    // case1: 未选择客户
    // case2: 点击了修改按钮
    const value = isNormal(clientId) ? clientId : undefined;
    return (
      <div data-testid={ApptTestIds.ApptSelectClientBtn}>
        <ClientSearchInput
          isOpen={isOpen.value}
          loadingTop10={getTop10.isBusy()}
          value={value}
          onChange={(newVal) => {
            if (newVal === clientId) return;
            onChange?.(newVal);
          }}
          onOpenChange={isOpen.as}
        />
      </div>
    );
  }

  return (
    <ClientInfoCard
      className={className}
      clientId={clientId}
      loadingClient={loadingClient}
      onChange={onChange ? isOpen.open : undefined}
      customerPackages={customerPackages}
    />
  );
}
