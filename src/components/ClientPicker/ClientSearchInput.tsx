import { MinorSearchOutlined } from '@moego/icons-react';
import {
  type Option,
  type OptionValue,
  LegacySelect as Select,
  type LegacySelectProps as SelectProps,
  Text,
  Tooltip,
} from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect, useMemo } from 'react';
import { useSetState } from 'react-use';
import { selectViewClientListPermission } from '../../store/business/role.selectors';
import { type CustomerBookingSearchListDto, clientBookSearch } from '../../store/customer/clientFilters.action';
import { type CustomerRecord, customerMapBox, printFullName } from '../../store/customer/customer.boxes';
import { selectBusinessCustomers } from '../../store/customer/customer.selectors';
import { customerPetListBox, petMapBox } from '../../store/pet/pet.boxes';
import { type RecordProps } from '../../store/utils/RecordMap';
import { isNormal } from '../../store/utils/identifier';
import { jsonRecordMap } from '../../store/utils/utils';
import { useCancelableCallback } from '../../utils/hooks/useCancelableCallback';
import { useDebounceValue } from '../../utils/hooks/useDebounceValue';
import { ClientAddButton } from './ClientAddButton';
import { ClientItem, type ClientListItem } from './ClientItem';

interface CustomOption<T extends OptionValue> extends Option<T> {
  detail: ClientListItem;
}

export interface ClientSearchInputProps extends SelectProps<CustomOption<number>, false> {
  loadingTop10?: boolean;
}

export function ClientSearchInput(props: ClientSearchInputProps) {
  const { loadingTop10, value, placeholder = 'Select a client to start', isOpen, onChange, ...rest } = props;
  const dispatch = useDispatch();
  const [customers, customerMap, customerPetList, petMap, canViewClientList] = useSelector(
    selectBusinessCustomers(),
    customerMapBox,
    customerPetListBox,
    petMapBox,
    selectViewClientListPermission,
  );

  const [{ keywords, clientList }, setState] = useSetState<{
    keywords: string;
    clientList: CustomerBookingSearchListDto['customerList'];
  }>({
    keywords: '',
    clientList: [],
  });
  const delayedKeywords = useDebounceValue(keywords);
  const isEmptyKeywords = delayedKeywords.trim().length === 0;

  const first10CustomerIdList = customers.getList();
  const top10CustomerList = useMemo(() => {
    return first10CustomerIdList.map((id) => {
      const customer = customerMap.mustGetItem(id);
      const customerPetIdList = customerPetList.getList(id);
      const petList = jsonRecordMap(petMap, customerPetIdList);
      return {
        ...(customer.toJS() as RecordProps<CustomerRecord>),
        petNameList: petList,
      };
    });
  }, [first10CustomerIdList, customerMap, customerPetList, petMap]);

  const getCustomerOption = (id: number) => {
    const newCustomer = customerMap.mustGetItem(id);
    return {
      value: id,
      label: newCustomer.fullName(),
      detail: { ...newCustomer.toJSON(), petNameList: [] },
    };
  };

  const getSearch = useCancelableCallback(async (signal, keywords: string) => {
    const clientList = await dispatch(clientBookSearch(keywords, signal));
    const list = clientList.slice(0, 200);
    setState({ clientList: list });
  });
  const loading = getSearch.isBusy() || !!loadingTop10;

  const options = useMemo(() => {
    const list = (isEmptyKeywords ? top10CustomerList : clientList).map((v) => ({
      value: v.customerId,
      label: printFullName(v.firstName, v.lastName),
      detail: v,
    }));
    if (isNormal(value) && !list.some((v) => v.value === value)) {
      return list.concat(getCustomerOption(value));
    }
    return list;
  }, [isEmptyKeywords, top10CustomerList, clientList]);

  useEffect(() => {
    if (isEmptyKeywords) {
      return setState({ clientList: [] });
    }
    getSearch(delayedKeywords);
  }, [isEmptyKeywords, delayedKeywords]);

  return (
    <Tooltip
      content={`Please request "Access client list at businesses" permission from the business owner`}
      isDisabled={canViewClientList}
    >
      <Select
        {...rest}
        className={canViewClientList ? 'moe-cursor-default' : 'moe-cursor-not-allowed'}
        isDisabled={!canViewClientList}
        classNames={{
          dropdownIndicator: 'moe-hidden',
          loadingIndicator: 'moe-self-auto',
          control:
            'moe-rounded-m moe-shadow-[0px_0px_0px_1px_#E6E6E6_inset,0px_0px_8px_0px_rgba(0,0,0,0.04),0px_4px_22px_-1px_rgba(0,0,0,0.08)]',
          menu: 'moe-max-h-[60vh]' /** 近似值 */,
        }}
        isOpen={isOpen}
        autoFocus={isOpen} // 当isOpen来控制下拉框时，保证可以click outside 关闭。这是vision一个聪明的 hack
        isLoading={loading}
        placeholder={placeholder}
        options={options}
        value={value}
        isSearchable
        onSearchValueChange={(value) => setState({ keywords: value })}
        noOptionsMessage={() => (loading ? '' : 'No client found')}
        prefix={
          <div className="moe-flex moe-items-center moe-justify-center moe-w-[40px] moe-h-[40px] moe-my-[6px] moe-mr-xs moe-rounded-full moe-cursor-auto moe-bg-neutral-sunken-0">
            <MinorSearchOutlined className="moe-text-secondary" />
          </div>
        }
        filterOption={() => true}
        formatOptionLabel={() => {
          return (
            <Text variant="regular-short" className="moe-text-disabled">
              {placeholder}
            </Text>
          );
        }}
        renderMenu={(menu) => (
          <>
            <div className="moe-flex moe-pt-xs">
              <ClientAddButton
                useNewButton
                onAddClientSuccess={(id) => {
                  const option = getCustomerOption(id);
                  onChange?.(id, option, { action: 'select-option', option });
                }}
              />
            </div>
            {menu}
          </>
        )}
        renderItem={({ data: { detail } }) => <ClientItem client={detail} />}
        onChange={onChange}
      />
    </Tooltip>
  );
}
