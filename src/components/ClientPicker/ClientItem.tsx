import { useSelector } from 'amos';
import React from 'react';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { printFullName } from '../../store/customer/customer.boxes';
import { PetLifeStatus } from '../../store/pet/pet.boxes';
import { Avatar } from '../Avatar/Avatar';
import { Condition } from '../Condition';

export type ClientListItem = {
  avatarPath: string;
  customerId: number;
  firstName: string;
  lastName: string;
  clientColor: string;
  phoneNumber: string;
  inactive: number;
  petNameList: { petId: number; petName: string; lifeStatus: number }[];
};

export interface ClientItemProps {
  client: ClientListItem;
}

export function ClientItem(props: ClientItemProps) {
  const { client } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const petList = Array.isArray(client.petNameList) ? client.petNameList : [];

  return (
    <div className="moe-w-full moe-flex moe-justify-between">
      <div className="moe-flex moe-items-center moe-gap-x-[16px]">
        <Avatar src={client.avatarPath} size="40px" info={client} />
        <div className="moe-flex-1 moe-min-w-0">
          <div className="moe-flex moe-items-center moe-gap-x-[4px]">
            <div className="moe-flex-1 moe-min-w-0 moe-text-[16px] moe-text-[#333] moe-font-bold moe-leading-[24px] moe-max-w-[160px] moe-truncate">
              {printFullName(client.firstName, client.lastName)}
            </div>
            <Condition if={client.inactive > 0}>
              <div className="moe-inline-block moe-rounded-[31px] moe-px-[7px] moe-py-[4px] moe-bg-[#FAE6E8] moe-text-[#D0021B] moe-text-[12px] moe-scale-[0.9] moe-font-bold moe-leading-[14px]">
                Inactive
              </div>
            </Condition>
          </div>
          <div className="moe-text-[14px] moe-text-[#666] moe-font-medium moe-leading-[18px]">
            {petList
              .filter((pet) => pet.lifeStatus === PetLifeStatus.Alive)
              .map((pet) => pet.petName)
              .join(' / ')}
          </div>
        </div>
      </div>
      <div>
        <div className="moe-text-[14px] moe-text-[#666] moe-font-medium moe-leading-[18px] moe-truncate moe-max-w-[120px] rr-mask">
          {business.formatPhoneNumber(client.phoneNumber)}
        </div>
      </div>
    </div>
  );
}
