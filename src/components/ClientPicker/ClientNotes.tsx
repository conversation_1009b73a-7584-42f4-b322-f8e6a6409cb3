import { Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import {
  type CustomerNoteRecord,
  customerNoteListBox,
  customerNoteMapBox,
} from '../../store/customer/customerNote.boxes';
import { jsonRecordMap } from '../../store/utils/utils';
import { IconModernTextNum } from '../Icon/IconModernTextNum';
import { NotesContent } from '../PetInfo/NoteContent';

export interface ClientNotesProps {
  clientId: number;
  maxNotes?: number;
  popOverChildren?: (notes: CustomerNoteRecord[]) => React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  onClickPopover?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
}

export function ClientNotes(props: ClientNotesProps) {
  const { clientId, popOverChildren, maxNotes, onClick, onClickPopover } = props;
  const [noteList, noteMap] = useSelector(customerNoteListBox.getList(clientId), customerNoteMapBox);
  const clientNotes = jsonRecordMap(noteMap, noteList);
  const notesLength = clientNotes.length;

  return notesLength ? (
    <div onClick={(e) => e.stopPropagation()}>
      <Tooltip
        side="bottom"
        backgroundTheme="light"
        content={
          <NotesContent max={maxNotes} notes={clientNotes} onClick={onClickPopover} type="client">
            {popOverChildren?.(clientNotes)}
          </NotesContent>
        }
      >
        <IconModernTextNum count={notesLength} onClick={onClick} />
      </Tooltip>
    </div>
  ) : null;
}
