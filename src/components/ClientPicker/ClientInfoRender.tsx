import { useDispatch, useSelector } from 'amos';
import { type List } from 'immutable';
import type React from 'react';
import { useAsync } from 'react-use';
import { type ClientDetailQuery, PATH_CUSTOMER_DETAIL } from '../../router/paths';
import { type BusinessRecord } from '../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { getAllClientInfo } from '../../store/customer/customer.actions';
import { type CustomerRecord, customerMapBox } from '../../store/customer/customer.boxes';
import { type CustomerAddressRecord } from '../../store/customer/customerAddress.boxes';
import { selectPrimaryAddress } from '../../store/customer/customerAddress.selectors';
import { customerPetListBox } from '../../store/pet/pet.boxes';
import { isNormal } from '../../store/utils/identifier';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';

export interface ClientInfoRenderProps {
  clientId: number;
  /**
   * 默认行为进来就会加载client数据
   * 配置true后，强制不发起请求, 阻止自动加载client相关数据，需要开发自行确保数据来源
   */
  preventAutoReload?: boolean;
  children?: (_: {
    loading: boolean;
    client: CustomerRecord;
    primaryAddress: CustomerAddressRecord;
    business: BusinessRecord;
    petIdList: List<number>;
    go2ClientDetail: (state?: ClientDetailQuery) => void;
  }) => React.ReactElement | null;
}

export function ClientInfoRender(props: ClientInfoRenderProps) {
  const { clientId, children, preventAutoReload = false } = props;
  const dispatch = useDispatch();
  const [client, primaryAddress, business, petIdList] = useSelector(
    customerMapBox.mustGetItem(clientId),
    selectPrimaryAddress(clientId),
    selectCurrentBusiness,
    customerPetListBox.getList(clientId),
  );
  const preClientId = client.customerId;
  const inValidatePreClientId = !isNormal(preClientId) || !isNormal(primaryAddress.customerId);
  const go2ClientDetail = useLatestCallback((state?: ClientDetailQuery) => {
    if (!isNormal(clientId)) {
      return;
    }
    // open in new tab
    window.open(PATH_CUSTOMER_DETAIL.queried({ ...state }, { customerId: clientId }), '_blank', 'noopener noreferrer');
  });

  const { loading } = useAsync(async () => {
    if (preventAutoReload) {
      return;
    }
    if (isNormal(clientId) && inValidatePreClientId) {
      await dispatch(getAllClientInfo(clientId));
    }
  }, [clientId, inValidatePreClientId, preventAutoReload]);

  if (!isNormal(clientId)) {
    return null;
  }

  return (
    children?.({
      loading,
      client,
      business,
      primaryAddress,
      petIdList,
      go2ClientDetail,
    }) ?? null
  );
}
