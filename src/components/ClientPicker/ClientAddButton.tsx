import { MajorPlusOutlined } from '@moego/icons-react';
import { Button, toast } from '@moego/ui';
import React, { useMemo } from 'react';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useAddClientWithPetsFormModal } from '../AddClientWithPets/AddClientWithPetsModal';
import { AddTextButton } from '../AddTextButton/AddTextButton';
import { WithPermission } from '../GuardRoute/WithPermission';

interface AddClientModalOperation {
  onOpenModal: () => void;
}

export interface ClientAddButtonProps {
  useNewButton?: boolean;
  addButtonClassName?: string;
  textClassName?: string;
  children?: (_: AddClientModalOperation) => React.ReactElement | null;
  onAddClientSuccess?: (clientId: number) => void;
}

export function ClientAddButton(props: ClientAddButtonProps) {
  const { children, addButtonClassName, textClassName, useNewButton, onAddClientSuccess } = props;

  const _open = useAddClientWithPetsFormModal();
  const onOpenModal = useLatestCallback(() => {
    _open({
      isShowAddAndSchedule: false,
      onAdded: (clientId) => {
        toast({ type: 'success', title: 'New client added.' });
        onAddClientSuccess?.(clientId);
      },
    });
  });

  const operations = useMemo(() => ({ onOpenModal }), []);
  return (
    <>
      {children ? (
        children(operations)
      ) : (
        <WithPermission permissions={['createNewClient']}>
          {useNewButton ? (
            <Button
              variant="tertiary-legacy"
              icon={<MajorPlusOutlined />}
              onPress={onOpenModal}
              className={addButtonClassName}
              classNames={{
                text: textClassName,
              }}
            >
              New client
            </Button>
          ) : (
            <AddTextButton className={addButtonClassName} onClick={onOpenModal} textClassName={textClassName}>
              New client
            </AddTextButton>
          )}
        </WithPermission>
      )}
    </>
  );
}
