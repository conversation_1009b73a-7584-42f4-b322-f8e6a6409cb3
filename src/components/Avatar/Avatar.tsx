import { Tooltip } from '@moego/ui';
import classNames from 'classnames';
import React, { type CSSProperties, type FC, useMemo } from 'react';
import IconAvatarCatDefaultSvg from '../../assets/icon/avatar-cat-default.svg';
import IconAvatarDogDefaultSvg from '../../assets/icon/avatar-dog-default.svg';
import IconAvatarOtherDefaultSvg from '../../assets/icon/avatar-other-default.svg';
import IconAvatarUserActiveSvg from '../../assets/icon/avatar-user-active.svg';
import IconAvatarUserDefaultSvg from '../../assets/icon/avatar-user-default.svg';
import SvgIconMoegoLogoSvg from '../../assets/svg/icon-moego-logo.svg';
import SvgVanDefaultAvatarSvg from '../../assets/svg/van-default-avatar.svg';
import { c_white, colors } from '../../style/_variables';
import { firstLetter } from '../../utils/misc';
import { SvgIcon } from '../Icon/Icon';

type SizeType = 'normal' | 'small' | 'large' | 'mini' | 'micro' | (string & {});
type BorderType = 'normal' | 'circle';
export type AvatarType = 'user' | 'cat' | 'dog' | 'other' | 'user-active' | 'van' | 'system';

export interface AvatarProps {
  size?: SizeType;
  src?: string;
  className?: string;
  imgClassName?: string;
  border?: BorderType;
  type?: AvatarType;
  style?: CSSProperties;
  imgStyle?: CSSProperties;
  onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  info?: { firstName?: string; lastName?: string; clientColor?: string };
  passAway?: boolean;
  useSvgIcon?: boolean;
  tooltip?: React.ReactNode;
}

export const icons: Record<AvatarType, string> = {
  user: IconAvatarUserDefaultSvg,
  'user-active': IconAvatarUserActiveSvg,
  cat: IconAvatarCatDefaultSvg,
  dog: IconAvatarDogDefaultSvg,
  van: SvgVanDefaultAvatarSvg,
  other: IconAvatarOtherDefaultSvg,
  system: SvgIconMoegoLogoSvg,
};

export const sizeMap: Record<string, number> = {
  large: 64,
  normal: 50,
  small: 40,
  mini: 32,
  micro: 24,
};

export const Avatar: FC<AvatarProps> = ({
  size = 'normal',
  src = '',
  className,
  imgClassName,
  border = 'circle',
  type: avatarType = 'user',
  style,
  imgStyle,
  onClick,
  info,
  useSvgIcon = false,
  passAway = false,
  tooltip,
}) => {
  const classes = classNames(
    'avatar-wrapper',
    {
      [`avatar-wrapper-${size}`]: size,
      [`avatar-wrapper-border-${border}`]: border,
      'pass-away': passAway,
    },
    className,
  );

  const wrapperStyle = useMemo(
    () => ({
      width: sizeMap[size] || size,
      height: sizeMap[size] || size,
      ...style,
    }),
    [style, size],
  );

  if (!src && info?.firstName && info.lastName) {
    let code = firstLetter(info.firstName).toUpperCase();
    if (code.length === 1) {
      code += firstLetter(info.lastName).toUpperCase();
      if (code.length > 2) {
        code = code.charAt(0);
      }
    } else {
      code += ' ';
    }
    const width = sizeMap[size] || parseInt(size, 10);
    return (
      <div
        className={classes}
        style={{
          ...wrapperStyle,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          fontWeight: 'bold',
          fontSize: width / 2 + 'px',
          lineHeight: width / 2 + 'px',
          color: c_white,
          backgroundColor:
            info?.clientColor ||
            colors[((info.firstName.charCodeAt(0) || 0) + (info.lastName.charCodeAt(0) || 0)) % colors.length],
        }}
        onClick={onClick}
      >
        {code}
      </div>
    );
  }

  return (
    <Tooltip content={tooltip}>
      <div className={classes} style={wrapperStyle} onClick={onClick}>
        {src ? (
          <img src={src} alt={avatarType} className={imgClassName} style={imgStyle} />
        ) : useSvgIcon ? (
          <SvgIcon src={icons[avatarType!] || ''} size={sizeMap[size]} />
        ) : (
          <img src={icons[avatarType!] || ''} alt={avatarType} className={imgClassName} style={imgStyle} />
        )}
      </div>
    </Tooltip>
  );
};
