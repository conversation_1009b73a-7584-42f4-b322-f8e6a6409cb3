.avatar-wrapper {
  overflow: hidden;
  cursor: pointer;
  color: #e6e6e6;
  word-break: keep-all;

  & > img {
    object-fit: cover;
    height: 100%;
    width: 100%;
    border-radius: 50%;
  }

  &.pass-away {
    position: relative;
    overflow: visible;

    &::after {
      content: 'Deactivated';
      font-size: 9px;
      color: #101928;
      padding: 0 6px;
      border-radius: 11px;
      position: absolute;
      bottom: -3px;
      left: 50%;
      margin-left: -30px;
      background-color: #dedede;
      display: flex;
      align-items: center;
      height: 16px;
      white-space: nowrap;
    }

    & > img {
      opacity: 0.3;
    }
  }
}

.avatar-wrapper-large {
  height: 64px;
  width: 64px;
}

.avatar-wrapper-normal {
  height: 50px;
  width: 50px;
}

.avatar-wrapper-small {
  height: 40px;
  width: 40px;
}

.avatar-wrapper-mini {
  height: 32px;
  width: 32px;
}

.avatar-wrapper-border-normal {
  border-radius: 4px;
}

.avatar-wrapper-border-circle {
  border-radius: 50%;
}
