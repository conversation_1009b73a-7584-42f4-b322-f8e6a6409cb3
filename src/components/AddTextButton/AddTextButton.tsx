import classNames from 'classnames';
import React from 'react';
import IconPlus from '../../assets/svg/icon-plus.svg';
import { SvgIcon } from '../Icon/Icon';

export interface AddTextButtonProps {
  className?: string;
  textClassName?: string;
  style?: React.CSSProperties;
  plusStyle?: React.CSSProperties;
  onClick?: () => void;
  disabled?: boolean;
}

export function AddTextButton(props: React.PropsWithChildren<AddTextButtonProps>) {
  const {
    disabled,
    style,
    className,
    children,
    onClick,
    textClassName = '!moe-font-medium !moe-text-[14px] !moe-text-brand',
    plusStyle = { fontSize: 16, color: 'var(--moe-color-icon-brand)' },
    ...rest
  } = props;

  return (
    <div
      className={classNames(
        'moe-flex moe-gap-x-[6px] moe-items-center moe-select-none',
        className,
        disabled ? 'moe-cursor-not-allowed' : 'moe-cursor-pointer hover:!moe-opacity-80',
      )}
      style={style}
      onClick={disabled ? undefined : onClick}
      {...rest}
    >
      <SvgIcon src={IconPlus} style={plusStyle} />
      <div className={textClassName} title={typeof children === 'string' ? children : undefined}>
        {children}
      </div>
    </div>
  );
}
