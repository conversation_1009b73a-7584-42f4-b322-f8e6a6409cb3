import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { InvoiceInfo } from '../../container/Calendar/Grooming/TakePaymentModal/InvoiceInfo/InvoiceInfo';
import { PaymentInfoBody, PaymentInfoView } from '../../container/Calendar/Grooming/TakePaymentModal/PaymentInfo.style';
import { PaymentResult } from '../../container/Calendar/Grooming/TakePaymentModal/PaymentResult';
import { ModalHeader } from '../../container/Calendar/Grooming/TakePaymentModal/components/ModalHeader';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { getGroomingTicketInvoice } from '../../store/grooming/grooming.actions';
import { InvoiceStatus } from '../../store/grooming/grooming.boxes';
import { selectGroomingTicketInvoice } from '../../store/grooming/grooming.selectors';
import { getPaymentSettingInfo } from '../../store/payment/actions/public/payment.actions';
import { ID_LOADING, isNormal } from '../../store/utils/identifier';
import { Condition } from '../Condition';
import { Modal } from '../Modal/Modal';

export interface RetailSaleInvoiceModalProps {
  className?: string;
  invoiceId: number;
  onClose: () => void;
}

export const SaleHistoryInvoiceModal = memo<RetailSaleInvoiceModalProps>(({ invoiceId, onClose }) => {
  const [business, invoice] = useSelector(selectCurrentBusiness, selectGroomingTicketInvoice(invoiceId ?? ID_LOADING));
  const dispatch = useDispatch();
  const isCompleted = invoice.status === InvoiceStatus.Completed || invoice.remainAmount === 0;

  useEffect(() => {
    dispatch(getPaymentSettingInfo());
  }, []);

  useEffect(() => {
    isNormal(invoiceId) && dispatch(getGroomingTicketInvoice(invoiceId));
  }, [invoiceId]);

  return (
    <Modal
      visible={isNormal(invoiceId)}
      showClose={!isCompleted}
      title={!isCompleted ? 'Invoice' : undefined}
      onClose={onClose}
      loading={!isNormal(invoice.id)}
      width={isCompleted ? '1130px' : '612px'}
      bodyStyle={{
        padding: 0,
      }}
    >
      <div className="moe-flex">
        <InvoiceInfo
          className="!moe-flex-1"
          invoiceId={invoiceId}
          showHeader={isCompleted}
          editable={false}
          showCreatedOrderPackage
        />
        <Condition if={isCompleted}>
          <PaymentInfoView>
            <ModalHeader title="Fully paid" onClose={onClose} />
            <PaymentInfoBody>
              <PaymentResult
                className="!moe-overflow-y-auto"
                invoice={invoice}
                business={business}
                disableEditInvoice
              />
            </PaymentInfoBody>
          </PaymentInfoView>
        </Condition>
      </div>
    </Modal>
  );
});
