import { Popover } from 'antd';
import React, { memo } from 'react';
import SvgDiscountSmallFilledSvg from '../../assets/svg/discount-small-filled.svg';
import { useInvoiceDiscountList } from '../../container/Calendar/Grooming/TakePaymentModal/TakePayment.utils';
import { AmountChangeType } from '../../store/grooming/grooming.boxes';
import { PopoverBlackStyle } from '../AntdStyle/PopoverBlackStyle';
import { Condition } from '../Condition';
import { SvgIcon } from '../Icon/Icon';

export interface AppliedDiscountCodePopoverProps {
  invoiceId: number;
}

export const AppliedDiscountCodePopover = memo<AppliedDiscountCodePopoverProps>(({ invoiceId }) => {
  const discountList = useInvoiceDiscountList(invoiceId);

  const filterCreditDiscountList = discountList.filter(({ discountType }) => discountType !== AmountChangeType.Credit);
  const discountCodeStr = filterCreditDiscountList
    .map(({ discountCode, formattedDiscountAmount }) => discountCode || formattedDiscountAmount)
    .join('\n');

  return (
    <Condition if={discountCodeStr}>
      <Popover
        placement="topRight"
        content={<div className="moe-whitespace-pre-wrap">{discountCodeStr}</div>}
        overlayClassName="explore-popover-black tooltip-popover-black"
      >
        <SvgIcon src={SvgDiscountSmallFilledSvg} size={20} color="#f96b18" />
      </Popover>
      <PopoverBlackStyle />
    </Condition>
  );
});
