import { LoadingOutlined } from '@ant-design/icons';
import { cn } from '@moego/ui';
import { Spin } from 'antd';
import React, { type CSSProperties, type FC } from 'react';
import styled from 'styled-components';

export interface LoadingProps {
  loading: boolean;
  style?: CSSProperties;
  iconSize?: string;
  className?: string;
  height?: number;
  tip?: string;
  indicatorStyle?: React.CSSProperties;
  classNames?: {
    wrapper?: string;
  };
}

export const Loading: FC<LoadingProps> = (props) => {
  const {
    children,
    style,
    loading,
    height,
    className,
    tip = '',
    iconSize = '20px',
    indicatorStyle,
    classNames,
  } = props;
  return (
    <Spin
      className={cn('loading', className)}
      wrapperClassName={classNames?.wrapper}
      indicator={<LoadingOutlined style={indicatorStyle} color="var(--moe-color-icon-brand)" />}
      style={{
        fontSize: iconSize,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        maxHeight: '100%',
        height,
        color: 'var(--moe-color-icon-brand)',
        ...style,
      }}
      spinning={loading}
      tip={tip}
    >
      {children}
    </Spin>
  );
};

export const MoeLoading = styled.div.attrs(() => ({ children: <LoadingOutlined /> }))`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
  color: var(--moe-color-icon-brand);
  font-size: 24px;
`;

export const LoadingModal = styled.div.attrs({
  children: <LoadingOutlined />,
})`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(240, 240, 240, 0.3);
  z-index: 1;
  color: var(--moe-color-icon-brand);
  font-size: 24px;
`;
