import { Condition, Tooltip, type TooltipProps, cn } from '@moego/ui';
import React from 'react';
import IconIconSendPortalSvg from '../../assets/icon/icon-send-portal.svg';
import { useHasBrandedApp } from '../../store/branded/branded.hooks';
import { ImgIcon } from '../Icon/Icon';

export interface BrandedAppTagProps extends Omit<TooltipProps, 'children'> {
  iconClassName?: string;
}

export const BrandedAppTag: React.FC<BrandedAppTagProps> = ({ iconClassName }) => {
  const { has, loading } = useHasBrandedApp();
  return (
    <Condition if={!loading && has}>
      <Tooltip content="Pet Parent App user" side="top">
        <ImgIcon className={cn('', iconClassName)} src={IconIconSendPortalSvg} />
      </Tooltip>
    </Condition>
  );
};
