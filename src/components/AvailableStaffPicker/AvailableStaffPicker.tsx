import { LegacySelect as Select, type LegacySelectProps as SelectProps } from '@moego/ui';
import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React, { memo, useMemo } from 'react';
import { staffMapBox } from '../../store/staff/staff.boxes';
import { useShowOnCalendarStaff } from '../ServiceStaffPicker/hooks/useShowOnCalendarStaff';
import { AvailableStaffOption } from './AvailableStaffOption';

export interface AvailableStaffPickerProps<T = number>
  extends Omit<SelectProps<{ label: string; value: number }, false>, 'value' | 'onChange'> {
  value?: T;
  onChange?: (v?: T) => void;
  start?: Dayjs;
  duration?: number;
  originStaffId?: number;
}

export const AvailableStaffPicker = memo((props: AvailableStaffPickerProps) => {
  const { value, start, duration, originStaffId, onChange, ...rest } = props;
  const [staffMap] = useSelector(staffMapBox);

  const staffIds = useShowOnCalendarStaff();

  const options = useMemo(() => {
    const ids = value ? (staffIds.includes(value) ? staffIds : [value, ...staffIds]) : staffIds;
    return ids.map((staffId) => {
      const staff = staffMap.mustGetItem(staffId);
      return {
        label: staff.fullName(),
        value: staffId,
      };
    });
  }, [staffMap, staffIds, value]);

  return (
    <Select
      {...rest}
      isMultiple={false}
      value={value}
      options={options}
      renderItem={({ data: { value } }) => (
        <AvailableStaffOption
          showConflict={originStaffId !== value}
          staffId={value}
          start={start}
          duration={duration}
        />
      )}
      onChange={onChange}
    />
  );
});
