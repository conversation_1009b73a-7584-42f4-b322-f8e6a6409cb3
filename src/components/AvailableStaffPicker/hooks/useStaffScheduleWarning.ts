import { useDispatch } from 'amos';
import { type Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import {
  type StaffScheduleApptWarningParams,
  checkStaffScheduleWarning,
} from '../../../store/calendarLatest/actions/public/calendar.actions';
import { isNormal } from '../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useCancelableCallback } from '../../../utils/hooks/useCancelableCallback';

export const STAFF_AVAILABLE = 0;

/**
 * 检查staff 是否working available, 冲突原因等
 * */
export function useStaffScheduleWarning(staffId: number, start?: Dayjs, duration?: number) {
  const dispatch = useDispatch();
  const [warningType, setWarningType] = useState<number>(STAFF_AVAILABLE);

  const checkScheduleWarning = useCancelableCallback(async (signal) => {
    if (!isNormal(staffId) || !start) {
      return;
    }
    const params: StaffScheduleApptWarningParams = {
      staffId,
      startTime: start.getMinutes(),
      appointmentTime: start.format(DATE_FORMAT_EXCHANGE),
      duration,
    };
    setWarningType(STAFF_AVAILABLE);
    const { isNotConflict, type } = await dispatch(checkStaffScheduleWarning(params, signal));
    setWarningType(isNotConflict ? STAFF_AVAILABLE : type);
  });

  useEffect(() => {
    if (isNormal(staffId) && start) {
      checkScheduleWarning();
    } else {
      setWarningType(STAFF_AVAILABLE);
    }
  }, [staffId, start, duration]);

  return {
    loading: checkScheduleWarning.isBusy(),
    warningType,
  };
}
