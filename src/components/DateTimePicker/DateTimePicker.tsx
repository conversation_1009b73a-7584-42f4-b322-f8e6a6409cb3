import { DatePicker, TimePicker, useControlledState } from '@moego/ui';
import { FormItemHelpText } from '@moego/ui/dist/esm/components/Form/FormItemHelpText';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { useStore } from 'amos';
import { type Dayjs } from 'dayjs';
import { isNil } from 'lodash';
import React, { type HTMLAttributes, memo, type ReactNode, useMemo } from 'react';
import { selectCurrentCompany } from '../../store/company/company.selectors';

interface DateTimePickerProps extends Omit<HTMLAttributes<HTMLDivElement>, 'value' | 'onChange' | 'defaultValue'> {
  value?: Dayjs;
  defaultValue?: Dayjs;
  onChange?: (value: Dayjs | null) => void;
  label?: ReactNode;
  isRequired?: boolean;
  tooltip?: ReactNode;
  errorMessage?: ReactNode;
  description?: ReactNode;
  allowOnlyTime?: boolean;
}

export const DateTimePicker = memo<DateTimePickerProps>(function DateTimePicker(props) {
  const {
    value: rawValue,
    onChange,
    defaultValue,
    label,
    isRequired,
    tooltip,
    errorMessage,
    description,
    allowOnlyTime = false,
    ...rest
  } = props;
  const store = useStore();
  const currentCompany = store.select(selectCurrentCompany);

  const [value, setValue] = useControlledState<Dayjs | null>(rawValue, defaultValue || null, onChange);

  const isDateSelected = useMemo(() => !isNil(value), [value]);

  const onChangeValue = (newValue: Dayjs | null) => {
    if (isNil(newValue)) {
      setValue(null);
    } else {
      setValue(newValue);
    }
  };

  return (
    <div {...rest} className="moe-flex moe-flex-col">
      <FormItemLabel label={label} isRequired={isRequired} tooltip={tooltip} />
      <div className="moe-flex moe-justify-between">
        <DatePicker
          className="moe-w-[240px]"
          format={currentCompany.formatDate.bind(currentCompany)}
          value={value}
          isClearable={false}
          onChange={onChangeValue}
        />
        <TimePicker
          isDisabled={!isDateSelected && !allowOnlyTime}
          className="moe-w-[240px]"
          format={currentCompany.formatTime.bind(currentCompany)}
          value={value}
          isClearable={false}
          onChange={onChangeValue}
        />
      </div>
      <FormItemHelpText errorMessage={errorMessage} description={description} />
    </div>
  );
});
