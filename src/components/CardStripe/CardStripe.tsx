import { type Stripe, loadStripe } from '@stripe/stripe-js';
import { noop } from 'monofile-utilities/lib/consts';
import { KEY_STRIPE } from '../../config/host/const';
import { http } from '../../middleware/api';

/**
 * 获取 business 内的 stripe 客户端
 * @param account
 */
export function getBusinessStripe(account?: string) {
  return new Promise<Stripe | null>(async (resolve, reject) => {
    try {
      let stripeAccount: string | undefined = undefined;
      if (account) {
        stripeAccount = account;
      } else {
        const accountInfo: any = await http.open('GET/payment/stripe/getAccount').catch(noop);
        stripeAccount = accountInfo?.data?.id;
      }
      const stripe = await loadStripe(KEY_STRIPE, {
        stripeAccount: stripeAccount,
      });
      resolve(stripe);
    } catch (e) {
      reject(e);
    }
  });
}

let _stripeAccountPromise: Promise<Stripe | null>;

/**
 * 获取全局 stripe 客户端, 与上者的区别是这里的信息没有指定 stripe account, 请注意区分
 */
export function getSystemStripe() {
  if (!_stripeAccountPromise) {
    _stripeAccountPromise = loadStripe(KEY_STRIPE);
  }
  return _stripeAccountPromise;
}
