import { DownOutlined, RightOutlined } from '@ant-design/icons';
import React, { type FC, useState } from 'react';

export interface AccordionPanelProps {
  title?: string | React.ReactNode;
  style?: React.CSSProperties;
}

export const AccordionPanel: FC<AccordionPanelProps> = (props) => {
  const { title, children, style } = props;
  const [isOpen, setIsOpen] = useState(true);
  return (
    <div className="accordion-panel-wrapper" style={style}>
      <div className="accordion-panel-title">
        <div className="title">{title}</div>
        <div className="arrow" onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? <DownOutlined /> : <RightOutlined />}
        </div>
      </div>
      {isOpen ? <div className="accordion-panel-body">{children}</div> : ''}
    </div>
  );
};
