import { cn } from '@moego/ui';
import React, { memo, type PropsWithChildren } from 'react';
import { Condition } from '../Condition';

export interface DrawerLayerProps {
  className?: string;
  visible?: boolean;
}

export const DrawerLayer = memo((props: PropsWithChildren<DrawerLayerProps>) => {
  const { children, visible, className } = props;
  return (
    <Condition if={visible}>
      <div
        className={cn(
          'moe-absolute moe-left-0 moe-right-0 moe-top-0 moe-bottom-0 moe-flex moe-flex-col moe-bg-white moe-z-50 moe-rounded-8px-300 moe-overflow-hidden',
          className,
        )}
      >
        {children}
      </div>
    </Condition>
  );
});
