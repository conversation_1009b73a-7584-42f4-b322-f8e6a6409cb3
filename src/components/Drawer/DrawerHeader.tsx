import { MajorLeftArrowOutlined } from '@moego/icons-react';
import { Heading, IconButton, cn } from '@moego/ui';
import React, { memo } from 'react';
import { ApptTestIds } from '../../config/testIds/apptDrawer';

interface DrawerHeaderProps {
  title: string;
  border?: boolean;
  onClick?: () => void;
  className?: string;
}
export const DrawerHeader = memo((props: DrawerHeaderProps) => {
  const { title, border = true, onClick, className } = props;
  return (
    <div
      className={cn('moe-flex moe-items-center moe-gap-x-[8px] moe-px-[32px] moe-py-[22px]', className, {
        'moe-border-divider': border,
      })}
    >
      <IconButton icon={<MajorLeftArrowOutlined />} onPress={onClick} data-testid={ApptTestIds.ApptReturnBtn} />
      <Heading size="3">{title}</Heading>
    </div>
  );
});
