import { Button } from 'antd';
import { type DrawerProps as AntdDrawerProps } from 'antd/es/drawer';
import { isUndefined } from 'lodash';
import React, { type PropsWithChildren } from 'react';
import SvgIconCloseSvg from '../../assets/svg/icon-close.svg';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { SvgIcon } from '../Icon/Icon';
import { DrawerView } from './StyledDrawer.style';

interface DrawerProps extends AntdDrawerProps {
  onApply?: () => void | Promise<any>;
  onClose: () => void;
  disable?: boolean;
  saveBtnText?: string;
  cancelBtnText?: string;
}

export type StyledDrawerProps = PropsWithChildren<DrawerProps>;

export const StyledDrawer = (props: StyledDrawerProps) => {
  const { onApply, saveBtnText, cancelBtnText, ...otherProps } = props;
  const handleApply = useSerialCallback(async () => {
    await onApply?.();
  });

  const footer = isUndefined(props.footer) ? (
    <div className="!moe-flex !moe-justify-center">
      <span
        className="!moe-border-[1px] !moe-border-solid !moe-rounded-full !moe-border-[#DEE1E5] !moe-h-[32px] !moe-flex !moe-justify-center !moe-items-center !moe-px-[26px] !moe-text-[#000] !moe-cursor-pointer !moe-font-bold"
        onClick={props.onClose}
      >
        {cancelBtnText ?? 'Cancel'}
      </span>
      <Button
        onClick={handleApply}
        loading={handleApply.isBusy()}
        className={`!moe-rounded-full !moe-border-brand !moe-bg-brand-bold !moe-text-[#fff] !moe-ml-[12px] !moe-px-[26px] !moe-font-bold ${
          props.disable ? '!moe-opacity-70' : ''
        }`}
        disabled={props.disable}
      >
        {saveBtnText ?? 'Apply'}
      </Button>
    </div>
  ) : (
    props.footer
  );

  return <DrawerView {...otherProps} footer={footer} closeIcon={<SvgIcon src={SvgIconCloseSvg} />} />;
};
