import { type GetDailyReportForCustomerResult } from '@moego/api-web/moego/api/appointment/v1/daily_report_api';
import { QuestionCategoryType, QuestionType } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { type QuestionDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { generateThemePattern } from '../../ThemeColorInput/utils';
import { type DailyReportEmailProps } from '../../ReportEmail/DailyReportEmail/DailyReportEmail';
import { type FeedbackItem } from '../../ReportEmail/components/ReportFeedback';

export const HOW_DID_IT_GO = 'how_did_it_go';
export const MOOD = 'mood';
export const ADDITIONAL_NOTE = 'additional_note';

export function calcEmailProps(data?: GetDailyReportForCustomerResult): DailyReportEmailProps {
  if (!data) {
    return {};
  }

  const {
    business,
    pet,
    report: { content },
  } = data;
  const { businessName, avatarPath: businessAvatarPath } = business;
  const { petName } = pet;
  const { feedbacks, photos, themeColor, videos } = content;

  const showcase = {
    images: photos,
    videoLink: videos[0],
  };

  const questionDefToFeedbackItem = (item: QuestionDef): FeedbackItem => {
    const question = item.title;
    let answer = '';
    switch (item.type) {
      case QuestionType.SINGLE_CHOICE:
      case QuestionType.MULTI_CHOICE:
      case QuestionType.TAG_CHOICE:
        answer = item.choices.join('\n') || '';
        break;
      case QuestionType.TEXT_INPUT:
      case QuestionType.SHORT_TEXT_INPUT:
        answer = item.inputText || '';
    }
    return {
      question,
      answer,
    };
  };

  const howDidItGoItem = feedbacks.find((item) => item.key === HOW_DID_IT_GO);
  const additionalNoteItem = feedbacks.find((item) => item.key === ADDITIONAL_NOTE);
  const moodListItem = feedbacks.find((item) => item.key === MOOD);

  const overallFeedbacks1 = [howDidItGoItem, additionalNoteItem]
    .filter((item): item is QuestionDef => !!item)
    .map((item) => questionDefToFeedbackItem(item));

  // mood 单独处理，分隔方式为 #mood1, #mood2 ，并进行兼容处理
  const overallFeedbacks2 = [moodListItem]
    .filter((item): item is QuestionDef => !!item)
    .map((item) => ({
      question: item.title,
      answer: [...new Set([...item.choices, ...item.customOptions])].map((choice) => `#${choice}`).join(' ') || '',
    }));

  const overallFeedbacks = [...overallFeedbacks1, ...overallFeedbacks2].filter((item) => !!item.answer);

  const customizedFeedbacks = feedbacks
    .filter((item) => item.category === QuestionCategoryType.CUSTOMIZE_FEEDBACK)
    .map((item) => questionDefToFeedbackItem(item))
    .filter((item) => !!item.answer);

  let lightThemeColor: string | undefined = undefined;
  if (themeColor) {
    const { light } = generateThemePattern(themeColor);
    lightThemeColor = light;
  }

  return {
    businessAvatarPath,
    petName,
    showcase,
    overallFeedbacks,
    customizedFeedbacks,
    businessName,
    themeColor,
    lightThemeColor,
  };
}
