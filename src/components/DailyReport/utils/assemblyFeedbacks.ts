import { type QuestionDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { type FeedbackItemType } from '../../../store/groomingReport/groomingReportEdit.actions';
import {
  ADDITIONAL_NOTE,
  type DailyReportStructure,
  type FormParams,
  HOW_DID_IT_GO,
  HOW_DID_IT_GO_CUSTMIZE_OPTION,
  MOOD,
} from '../hook/DailyReportForm.hook';

export const assemblyFeedbacks = (
  structure: DailyReportStructure,
  formValues: FormParams,
  moodData?: FeedbackItemType,
) => {
  return structure.report.content.feedbacks.map((current: QuestionDef) => {
    current = { ...current };
    switch (current.key) {
      case HOW_DID_IT_GO:
        current.choices =
          formValues.howDidItGo === HOW_DID_IT_GO_CUSTMIZE_OPTION
            ? [formValues.howDidItGoInput]
            : [formValues.howDidItGo];
        current.customOptions = [formValues.howDidItGoInput];
        break;
      case MOOD:
        current.choices = moodData?.choices || [];
        current.customOptions = moodData?.customOptions || [];
        break;
      case ADDITIONAL_NOTE:
        current.inputText = formValues.additionalNote;
        break;
    }
    return current;
  });
};
