import { SendMethod } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { MajorChevronDownOutlined } from '@moego/icons-react';
import { useSerialCallback } from '@moego/tools';
import { Dropdown, Modal, Scroll, Spin, Tabs, cn, type Key } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React, { forwardRef, memo, type ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { getAppointment } from '../../container/Appt/store/appt.api';
import { selectMainServiceInAppt } from '../../container/Appt/store/appt.selectors';
import {
  generateMessage,
  getDailyReportResultList,
  upsertDailyReportConfig,
  upsertDailyReportStatus,
} from '../../store/dailyReport/dailyReport.action';
import { selectDailyReportIsResend } from '../../store/dailyReport/dailyReport.selectors';
import { useQuery } from '../../store/utils/useQuery';
import { useAsyncEffect } from '../../utils/hooks/useAsyncEffect';
import { useBool } from '../../utils/hooks/useBool';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { unSavedDoubleConfirmV2 } from '../../utils/hooks/useUnsavedConfirmV2';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';
import { stringToDateMessage } from '../../utils/utils';
import { toastApi } from '../Toast/Toast';
import { DailyReportForm, type DailyReportFormRef } from './components/DailyReportForm';
import { DailyReportHistory } from './components/DailyReportHistory';
import { DailyReportPreview, type DailyReportPreviewRef } from './components/DailyReportPreview';
import { SendMessageModal } from './components/SendMessageModal';
import { useDailyReportForm } from './hook/DailyReportForm.hook';
import { useGetHistoryData } from './hook/DailyReportHistory.hook';
import { assemblyFeedbacks } from './utils/assemblyFeedbacks';
import { mergeRefs } from '../../utils/react';

export interface DailyReportParams {
  show: boolean;
  title?: string;
  appointmentId: string;
  petId: string;
  customerId: string;
  startDateTime?: Dayjs;
  endDateTime?: Dayjs;
  isBoarding?: boolean;
  onClose: () => void;
  afterUpdate?: () => void;
}

const enum TabType {
  Form = 'SendReport',
  History = 'History',
}

const LeftPanel = forwardRef<HTMLDivElement, { className?: string; children: ReactNode }>(
  ({ className, children }, forwardedRef) => {
    const [leftWidth, setLeftWidth] = useState(360);
    const viewportRef = useRef<HTMLDivElement>(null);

    // Scroll 组件下有个 table 布局，阻断了 width 透传，兼容处理一下
    useEffect(() => {
      const viewport = viewportRef.current;
      if (!viewport) return;

      const observer = new ResizeObserver((entries) => {
        const entry = entries[0];
        if (entry) {
          setLeftWidth(entry.borderBoxSize[0].inlineSize);
        }
      });

      observer.observe(viewport);

      return () => {
        observer.unobserve(viewport);
        observer.disconnect();
      };
    }, [viewportRef]);

    return (
      <Scroll
        classNames={{
          base: 'moe-flex-1 moe-min-w-[360px] moe-pr-m',
          viewport: className,
        }}
        viewportRef={mergeRefs(viewportRef, forwardedRef)}
      >
        <div className="moe-max-w-full moe-pl-m" style={{ width: leftWidth }}>
          {children}
        </div>
      </Scroll>
    );
  },
);

/**
 * Daily Report Modal
 */
export const DailyReport = memo<DailyReportParams>((props) => {
  const {
    show,
    title = 'Daily report',
    onClose,
    appointmentId,
    petId,
    startDateTime,
    endDateTime,
    isBoarding,
    customerId,
    afterUpdate,
  } = props;

  const dispatch = useDispatch();

  const previewRef = useRef<DailyReportPreviewRef>(null);
  const formRef = useRef<DailyReportFormRef>(null);
  const leftPanelRef = useRef<HTMLDivElement>(null);

  const isLoading = useBool();
  const sendModalVisible = useBool();

  const [mainService, isResend] = useSelector(
    selectMainServiceInAppt(appointmentId),
    selectDailyReportIsResend(+appointmentId, petId),
  );

  const isDaycare = mainService.serviceItemType === ServiceItemType.DAYCARE;
  const [activeTab, setActiveTab] = useState(TabType.Form);
  const [historyIndex, setHistoryIndex] = useState(0);

  const historyList = useGetHistoryData(appointmentId, petId);
  const { moodQuestion, howDidItGoOptions, form, placeholder, structure, getData } = useDailyReportForm(
    appointmentId,
    petId,
  );
  const hasHistory = historyList.length > 0;

  useQuery(getDailyReportResultList(+appointmentId, stringToDateMessage(form.getValues().date)));

  const isDirty = useBool();

  const renderView = (activeTab: TabType) => {
    return (
      <div className="moe-flex moe-w-full moe-h-full moe-overflow-y-hidden">
        <LeftPanel className={!isDaycare && hasHistory ? 'moe-pt-[24px]' : ''} ref={leftPanelRef}>
          {activeTab === TabType.History ? (
            <DailyReportHistory
              onPostMessage={(data) => {
                previewRef.current?.postMessage(data);
              }}
              appointmentId={appointmentId}
              petId={petId}
              historyList={historyList}
              activeIndex={historyIndex}
              onActiveIndexChange={setHistoryIndex}
            />
          ) : (
            <DailyReportForm
              onPostMessage={(data) => {
                previewRef.current?.postMessage(data);
              }}
              appointmentId={appointmentId}
              petId={petId}
              placeholder={placeholder}
              moodQuestion={moodQuestion}
              howDidItGoOptions={howDidItGoOptions}
              form={form}
              getData={getData}
              structure={structure}
              isDirty={isDirty}
              startDateTime={startDateTime}
              endDateTime={endDateTime}
              isBoarding={isBoarding}
              ref={formRef}
            />
          )}
        </LeftPanel>
        <Scroll className="moe-flex moe-justify-center moe-shrink-0 moe-w-[480px] moe-bg-neutral-sunken-1 moe-p-[32px_20px]">
          <DailyReportPreview ref={previewRef} />,
        </Scroll>
      </div>
    );
  };

  const handleClose = useLatestCallback(async () => {
    if (isDirty.value) {
      const needClose = await unSavedDoubleConfirmV2(
        () => {
          onClose();
        },
        () => {},
        { confirmText: 'Leave', cancelText: 'Back to edit' },
      );

      if (!needClose) {
        return;
      }
    } else {
      onClose();
    }
  });

  const [smsContent, setSmsContent] = useState('');
  const [sendMethod, setSendMethod] = useState(SendMethod.SMS);

  const sendReport = useSerialCallback(async () => {
    await saveDraft();
    const res = await dispatch(
      generateMessage({ appointmentId, petId, serviceDate: stringToDateMessage(form.getValues().date) }),
    );
    setSmsContent(res.message);
    sendModalVisible.open();
    reportData(ReportActionName.SendDailyReport, {
      mainCareType: mainService.serviceItemType,
    });
  });

  const [reportId, setReportId] = useState('');
  const saveDraft = async () => {
    const data = formRef.current?.moodRef.getData();
    const formValues = form.getValues();
    const serviceDate = stringToDateMessage(formValues.date);
    const id = await dispatch(
      upsertDailyReportConfig({
        appointmentId: structure.appointmentId,
        petId: structure.petId,
        serviceDate,
        customerId,
        report: {
          content: {
            photos: formValues.photos.map((photo) => photo.url!),
            videos: formValues.videos.map((video) => video.url!),
            themeColor: formValues.themeColor,
            lightThemeColor: formValues.lightThemeColor,
            feedbacks: [
              ...assemblyFeedbacks(structure, formValues, data),
              ...formValues.customizedFeedbacks.map((f) => f.value),
            ],
          },
        },
      }),
    );
    dispatch(upsertDailyReportStatus({ appointmentId, petId, serviceDate }));
    setReportId(id);
  };
  const onTertiary = () => {
    saveDraft();
    isDirty.close();
    toastApi.success('Saved successfully', 2000);
    afterUpdate?.();
  };

  useAsyncEffect(async () => {
    if (show && appointmentId) {
      isLoading.open();
      await dispatch(getAppointment({ appointmentId }));
      isLoading.close();
    }
  }, [appointmentId, show]);

  const handleRefreshReportResultList = () =>
    dispatch(getDailyReportResultList(+appointmentId, stringToDateMessage(form.getValues().date)));

  const isSending = sendReport.isBusy();
  const confirmButton = useMemo(
    () => (
      <Dropdown>
        <Dropdown.TriggerButton isLoading={isSending} suffix={<MajorChevronDownOutlined />}>
          {isResend ? 'Send again' : 'Send report'}
        </Dropdown.TriggerButton>
        <Dropdown.Menu
          onAction={async (key) => {
            setSendMethod(key === 'sms' ? SendMethod.SMS : SendMethod.EMAIL);
            sendReport();
          }}
        >
          <Dropdown.Item key="sms">via SMS</Dropdown.Item>
          <Dropdown.Item key="email">via Email</Dropdown.Item>
        </Dropdown.Menu>
      </Dropdown>
    ),
    [isResend, sendReport, isSending],
  );

  return (
    <>
      <Modal
        isOpen={show}
        title={title}
        showTertiaryButton={activeTab === TabType.Form}
        tertiaryText="Save as draft"
        size="l"
        onClose={handleClose}
        onTertiary={onTertiary}
        autoCloseOnConfirm={false}
        className="moe-w-[1040px]"
        classNames={{
          body: cn('moe-p-0', isBoarding && hasHistory && '!moe-border-t-0'),
        }}
        renderActions={(actions) => {
          const cancelButton = React.Children.toArray(actions.props.children)[0];
          return (
            <>
              {cancelButton}
              {activeTab === TabType.Form && confirmButton}
            </>
          );
        }}
        bodyScrollRef={leftPanelRef}
      >
        <Spin
          classNames={{
            base: 'moe-flex moe-flex-col moe-h-full moe-overflow-y-hidden',
            container: 'moe-flex moe-flex-col moe-h-full moe-overflow-y-hidden',
          }}
          isLoading={isLoading.value || getData.isBusy()}
        >
          {isBoarding && hasHistory ? (
            <>
              <Tabs
                classNames={{
                  panel: 'moe-hidden',
                  tabList: 'moe-px-spacing-m',
                }}
                selectedKey={activeTab}
                onChange={(v: Key) => {
                  setActiveTab(v as TabType);
                }}
                orientation="horizontal"
              >
                <Tabs.Item label="Send report" key={TabType.Form} />
                <Tabs.Item label="History" key={TabType.History} />
              </Tabs>
              {renderView(activeTab)}
            </>
          ) : (
            renderView(TabType.Form)
          )}
        </Spin>
      </Modal>
      {sendModalVisible.value ? (
        <SendMessageModal
          isOpen={sendModalVisible.value}
          onClose={() => sendModalVisible.close()}
          reportId={reportId}
          petId={Number(petId)}
          customerId={Number(customerId)}
          message={smsContent}
          sendMethod={sendMethod}
          handleSend={async () => {
            isDirty.close();
            try {
              await handleRefreshReportResultList();
            } finally {
              afterUpdate?.();
              handleClose();
            }
          }}
          handleSendFail={handleRefreshReportResultList}
          isResend={isResend}
        />
      ) : null}
    </>
  );
});
