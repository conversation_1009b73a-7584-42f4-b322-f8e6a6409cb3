import { SendMethod } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { isNormal } from '@moego/finance-utils';
import { useSerialCallback, validator } from '@moego/tools';
import { AlertDialog, Input } from '@moego/ui';
import { useControllableValue } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect, useState } from 'react';
import { getCustomer } from '../../../store/customer/customer.actions';
import { customerMapBox } from '../../../store/customer/customer.boxes';
import { sendMessage } from '../../../store/dailyReport/dailyReport.action';
import { type BoolState } from '../../../utils/hooks/useBool';
import { toastApi } from '../../Toast/Toast';

interface SendMessageModalParams {
  reportId: string;
  petId: number;
  customerId: number;
  message: string;
  sendMethod: SendMethod;
  isResend?: boolean;
  handleSend?: () => void;
  handleSendFail?: () => void;
  isOpen?: boolean;
  defaultOpen?: boolean;
  onClose?: () => void;
}

export interface SendMessageModalRef {
  showSendMessage: BoolState;
}

export const SendMessageModal = ({
  reportId,
  customerId,
  message,
  sendMethod,
  isResend = false,
  handleSend,
  handleSendFail,
  isOpen,
  defaultOpen = false,
  onClose,
}: SendMessageModalParams) => {
  const dispatch = useDispatch();
  const [customer] = useSelector(customerMapBox.mustGetItem(customerId));
  const [recipient, setRecipient] = useState(customer.email);
  const isInvaild = !!recipient && !validator.isEmail(recipient);

  const [mergedOpen, setOpen] = useControllableValue({
    value: isOpen,
    defaultValue: defaultOpen,
    onChange: (open) => {
      if (!open) {
        onClose?.();
      }
    },
  });

  useEffect(() => {
    if (!mergedOpen || !isNormal(customerId)) return;

    dispatch(getCustomer(customerId));
  }, [mergedOpen]);

  useEffect(() => {
    setRecipient(customer.email);
  }, [customer.email]);

  const send = useSerialCallback(async () => {
    try {
      return await dispatch(
        sendMessage({ sendMethod, id: reportId, recipientEmails: sendMethod === SendMethod.EMAIL ? [recipient] : [] }),
      );
    } catch (e) {
      console.error(e);
      return false;
    }
  });

  const handleSendMessage = async () => {
    const sendRes = await send();

    if (sendRes) {
      toastApi.success('Report send successfully', 2000);
      setOpen(false);
      handleSend?.();
    } else {
      toastApi.error('Report send failed', 2000);
      handleSendFail?.();
    }
  };

  const renderBody = () => {
    if (sendMethod === SendMethod.SMS) {
      const httpsIndex = message.indexOf('https');
      return (
        <div>
          <div className="moe-text-[16px] moe-font-normal moe-text-primary moe-leading-[24px] moe-bg-neutral-sunken-0 moe-rounded-[8px] moe-p-[12px]">
            <div>{message.substring(0, httpsIndex)}</div>
            <div>{message.substring(httpsIndex)}</div>
          </div>
          <div className="moe-text-secondary moe-text-[12px] moe-mt-[10px]">1 SMS from auto message will be used.</div>
        </div>
      );
    }

    return (
      <Input.Text
        label="Recipient"
        isRequired
        value={recipient}
        onChange={setRecipient}
        isInvalid={isInvaild}
        errorMessage={isInvaild ? 'Incorrect format' : undefined}
      />
    );
  };

  return (
    <AlertDialog
      isOpen={mergedOpen}
      title={`Send daily report ${isResend ? '(updated)' : ''}`}
      variant="confirm"
      confirmText="Send"
      cancelText="Back to edit"
      confirmButtonProps={{
        isDisabled: isInvaild,
        isLoading: send.isBusy(),
      }}
      size="m"
      autoCloseOnConfirm={false}
      onClose={() => setOpen(false)}
      onConfirm={handleSendMessage}
      onCancel={() => setOpen(false)}
      classNames={{
        body: 'moe-pb-0',
      }}
    >
      {renderBody()}
    </AlertDialog>
  );
};
