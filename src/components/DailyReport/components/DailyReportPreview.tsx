import { type GetDailyReportForCustomerResult } from '@moego/api-web/moego/api/appointment/v1/daily_report_api';
import { SegmentControl, Spin } from '@moego/ui';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { URL_CLIENT } from '../../../config/host/const';
import { type PortalFrameMsg } from '../../../container/settings/Settings/GroomingReportSetting/TemplatePreview/PreviewPortalEmbed';
import { useRefObject } from '../../../utils/hooks/hooks';
import { useBool } from '../../../utils/hooks/useBool';
import { memoForwardRef } from '../../../utils/react';
import { calcEmailProps } from '../utils/calcEmailProps';
import { DailyReportEmail } from '../../ReportEmail/DailyReportEmail/DailyReportEmail';

interface DailyReportPreviewProps {}

export interface DailyReportPreviewRef {
  postMessage: (data: GetDailyReportForCustomerResult) => void;
}

const PREVIEW_URL = URL_CLIENT + '/daily/report/preview';

const enum PreviewType {
  SMS = 'SMS',
  EMAIL = 'Email',
}

export const DailyReportPreview = memoForwardRef<DailyReportPreviewRef, DailyReportPreviewProps>(
  (_props, forwardedRef) => {
    const iframeRef = useRefObject<HTMLIFrameElement>();

    const [type, setType] = useState<PreviewType>(PreviewType.SMS);
    const [data, setData] = useState<GetDailyReportForCustomerResult>();

    const emailProps = calcEmailProps(data);

    const isLoading = useBool(true);

    useEffect(() => {
      const onMessage = (e: MessageEvent) => {
        try {
          const msg = e.data as PortalFrameMsg;

          if (msg.event === 'onload') {
            iframeRef.current?.contentWindow?.postMessage(
              {
                from: 'client-web',
                event: 'setData' as PortalFrameMsg['event'],
                data,
              },
              '*',
            );
          }
        } catch (error) {
          console.error(error);
        } finally {
          isLoading.close();
        }
      };
      window.addEventListener('message', onMessage);

      return () => {
        window.removeEventListener('message', onMessage);
      };
    }, [data]);

    useImperativeHandle(forwardedRef, () => {
      return {
        postMessage: (data) => {
          setData(data);
          iframeRef.current?.contentWindow?.postMessage(
            {
              from: 'client-web',
              event: 'setData' as PortalFrameMsg['event'],
              data,
            },
            '*',
          );
        },
      };
    });

    return (
      <div className="moe-relative moe-flex moe-flex-col moe-items-center moe-gap-m">
        <div className="moe-flex moe-justify-center">
          <SegmentControl
            size="s"
            itemClassName="moe-justify-center moe-min-w-[90px]"
            value={type}
            onChange={(type) => setType(type as PreviewType)}
          >
            <SegmentControl.Item value={PreviewType.SMS} label={PreviewType.SMS} />
            <SegmentControl.Item value={PreviewType.EMAIL} label={PreviewType.EMAIL} />
          </SegmentControl>
        </div>
        {type === PreviewType.SMS ? (
          <div className="moe-w-[calc(388px*0.78)] moe-h-[calc(900px*0.78)] moe-overflow-hidden">
            <div className="moe-w-[388px] moe-h-[900px] moe-bg-white moe-scale-[.78] moe-origin-top-left moe-overflow-hidden moe-rounded-[20px] moe-border-solid moe-border-[4px] moe-border-[#888C96] ">
              <Spin
                classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
                isLoading={isLoading.value}
                size="m"
              >
                <iframe
                  className="moe-w-[382px] moe-h-[900px] moe-border-none moe-overscroll-none"
                  ref={iframeRef}
                  src={PREVIEW_URL}
                />
              </Spin>
            </div>
          </div>
        ) : (
          <div className="moe-w-[420px] moe-transform moe-scale-[.7] moe-origin-top-left">
            <DailyReportEmail {...emailProps} />
          </div>
        )}
      </div>
    );
  },
);
