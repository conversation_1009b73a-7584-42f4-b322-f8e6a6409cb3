import { type GetDailyReportForCustomerResult } from '@moego/api-web/moego/api/appointment/v1/daily_report_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  DatePicker,
  Form,
  Heading,
  Input,
  Select,
  type UploadItem,
  type UseFormReturn,
  toast,
  useFieldArray,
  useWatch,
} from '@moego/ui';
import { ColorPickerPalette } from '@moego/ui/dist/esm/components/ColorPicker/ColorPickerPalette';
import { TextArea } from '@moego/ui/dist/esm/components/Input/TextArea';
import { useSelector } from 'amos';
import { Divider } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { selectApptStartAndEndTime, selectMainServiceInAppt } from '../../../container/Appt/store/appt.selectors';
import { type QuestionRef } from '../../../container/settings/GroomingReport/GroomingReportEdit/GroomingReportEdit.options';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type FeedbackItemType } from '../../../store/groomingReport/groomingReportEdit.actions';
import { Mood } from '../../Mood/Mood';
import { MoeGoUIUpload } from '../../Upload/MoeGoUIUpload';

import { type Dayjs } from 'dayjs';
import { ByteUnit } from '../../../utils/common';
import { downloadFromUrl } from '../../../utils/download';
import { type BoolState } from '../../../utils/hooks/useBool';
import { useDebounceCallback } from '../../../utils/hooks/useDebounceCallback';
import { upload2WebpHandler } from '../../Upload/AvatarUpload';
import { MoeGoTranscodeUpload } from '../../Upload/MoeGoTranscodeUpload';
import { usePetAndBusiness } from '../hook/DailyReport.hook';
import {
  type DailyReportStructure,
  type FormParams,
  HOW_DID_IT_GO_CUSTMIZE_OPTION,
} from '../hook/DailyReportForm.hook';
import { assemblyFeedbacks } from '../utils/assemblyFeedbacks';
import { Feedback } from '../../Feedback/Feedback';
import { validateFeedback } from '../../Feedback/utils/validate';
import { type QuestionDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { generateThemePattern } from '../../ThemeColorInput/utils';

export interface DailyReportFormParams {
  appointmentId: string;
  petId: string;
  maxPictures?: number;
  maxVideos?: number;
  maxVideoSize?: number;
  onPostMessage?: (data: GetDailyReportForCustomerResult) => void;
  placeholder?: { additionalNote: string };
  moodQuestion?: QuestionDef;
  howDidItGoOptions: { value: string; label?: string }[];
  form: UseFormReturn<FormParams>;
  getData: () => Promise<void>;
  structure: DailyReportStructure;
  isDirty: BoolState;
  startDateTime?: Dayjs;
  endDateTime?: Dayjs;
  isBoarding?: boolean;
}

export interface DailyReportFormRef {
  moodRef: CommonFeedbackRef;
}
type CommonFeedbackRef = QuestionRef<FeedbackItemType>;

const PRESET_COLORS = [
  '#F96B18',
  '#FF3B30',
  '#FF9500',
  '#FFCC00',
  '#34C7BE',
  '#30B0C7',
  '#32ADE6',
  '#007AFF',
  '#5856D6',
  '#AF52DE',
  '#FF2D55',
  '#A2845E',
];

export const DailyReportForm = forwardRef<DailyReportFormRef, DailyReportFormParams>(
  (
    {
      maxPictures = 5,
      maxVideos = 1,
      maxVideoSize = 40,
      appointmentId,
      petId,
      onPostMessage,
      placeholder,
      form,
      howDidItGoOptions,
      moodQuestion,
      getData,
      structure,
      isDirty,
      startDateTime: startDateTimeValue,
      endDateTime: endDateTimeValue,
      isBoarding,
    }: DailyReportFormParams,
    ref,
  ) => {
    /**
     * 从overview 进来时，由于没有打开过侧边栏，会导致mainService, apptDates为空，所以需要传值
     */
    const [business, mainService, apptDates] = useSelector(
      selectCurrentBusiness,
      selectMainServiceInAppt(appointmentId),
      selectApptStartAndEndTime(appointmentId),
    );
    const { petInfo, businessInfo } = usePetAndBusiness(petId);

    const { startDateTime, endDateTime } = apptDates;
    const [videos, themeColor, howDidItGo, howDidItGoInput, photos, customizedFeedbacks] = useWatch({
      control: form.control,
      name: ['videos', 'themeColor', 'howDidItGo', 'howDidItGoInput', 'photos', 'customizedFeedbacks'],
    });

    const start = startDateTimeValue || startDateTime;
    const end = endDateTimeValue || endDateTime;

    const isBoardingType = mainService.serviceItemType === ServiceItemType.BOARDING;

    const moodRef = useRef<CommonFeedbackRef>(null);

    const postToIframe = useDebounceCallback(() => {
      const data = moodRef.current?.getData();
      const postData = {
        pet: petInfo,
        business: businessInfo,
        serviceDate: structure.serviceDate,
        report: {
          content: {
            photos: photos.map((photo) => photo.url!).filter(Boolean),
            videos: videos.map((video) => video.url!).filter(Boolean),
            themeColor: themeColor,
            feedbacks: [
              ...assemblyFeedbacks(structure, form.getValues(), data),
              ...customizedFeedbacks.map((f) => f.value),
            ],
          },
        },
      };
      onPostMessage?.(postData);
    }, 500);

    useEffect(() => form.watch(postToIframe).unsubscribe, [form, postToIframe]);

    useImperativeHandle(ref, () => {
      return {
        moodRef: moodRef.current!,
      };
    });

    const colorsList = useMemo(() => {
      if (themeColor) {
        return PRESET_COLORS.includes(themeColor) ? PRESET_COLORS : PRESET_COLORS.concat(themeColor);
      } else return PRESET_COLORS;
    }, [themeColor]);

    const { fields } = useFieldArray({
      control: form.control,
      name: 'customizedFeedbacks',
    });

    return (
      <Form form={form} footer={null} className="moe-max-w-full">
        {isBoarding || isBoardingType ? (
          <Form.Item label="Date of the report" name="date">
            <DatePicker
              className="moe-w-full"
              isClearable={false}
              format={business.dateFormat}
              onChange={() => {
                getData?.();
              }}
              disabledDate={(currentDate) => {
                if (start && end) {
                  return (
                    currentDate.valueOf() < start?.startOf('day').valueOf() ||
                    currentDate.valueOf() > end?.endOf('day').valueOf()
                  );
                } else if (start) {
                  return currentDate.valueOf() < start?.startOf('day').valueOf();
                } else if (end) {
                  return currentDate.valueOf() > end?.endOf('day').valueOf();
                } else return false;
              }}
            />
          </Form.Item>
        ) : null}

        <div className="moe-flex moe-flex-col moe-gap-y-s">
          <Heading size="5">Showcase</Heading>
          <Form.Item label="Photo(s) of the day" name="photos">
            <MoeGoUIUpload
              isMultiple
              maxSize={20 * ByteUnit.MB}
              maxCount={maxPictures}
              placeholderDescription={`Up to ${maxPictures}`}
              accept="image/*"
              variant="image-card"
              onChange={() => {
                isDirty.open();
              }}
              onDownload={(file: UploadItem) => {
                const name = file.url?.split('name=')[1] || '';
                downloadFromUrl(file.url || '', name);
              }}
              itemClassNames={{
                fileIcon: 'moe-object-contain',
              }}
              customRequest={upload2WebpHandler}
            />
          </Form.Item>
          <Form.Item label="Video of the day" name="videos">
            <MoeGoTranscodeUpload
              isMultiple={false}
              maxSize={maxVideoSize * 1024 * 1024}
              enableDrop
              maxCount={maxVideos}
              placeholderDescription={
                <>
                  <label>Supported format: .mp4, .wav, .mov</label>
                  <br />
                  <label>{`Max size: ${maxVideoSize} MB`}</label>
                </>
              }
              accept="video/*"
              onChange={() => {
                isDirty.open();
              }}
              onDownload={async (file: UploadItem) => {
                if (!file.url) return; // 没有就不走了，类型安全+逻辑安全
                const { dismiss } = toast({
                  type: 'neutral',
                  title: 'downloading...',
                  duration: 0,
                });
                await downloadFromUrl(file.url, file.name);
                dismiss();
              }}
              itemClassNames={{
                content: 'moe-border moe-border-solid moe-border-[#CDCDCD]',
              }}
              classNames={{
                trigger: videos.length ? 'moe-hidden' : '',
                fileList: videos.length > 1 ? '' : 'moe-m-0',
              }}
            />
          </Form.Item>
        </div>

        <Divider className="!moe-m-0" dashed />

        <div className="moe-flex moe-flex-col moe-gap-y-s">
          <Heading size="5">Overall feedback</Heading>
          <div>
            <Select
              label="How did it go"
              value={howDidItGo}
              items={howDidItGoOptions}
              onChange={(v) => {
                form.setValue('howDidItGo', v!.toString());
                form.setValue('howDidItGoInput', '');
                isDirty.open();
              }}
            />
            {howDidItGo === HOW_DID_IT_GO_CUSTMIZE_OPTION && (
              <Input
                maxLength={30}
                value={howDidItGoInput}
                className="moe-w-full moe-mt-[12px]"
                placeholder="Enter customized feedback"
                size="m"
                onChange={(v) => {
                  form.setValue('howDidItGoInput', v.toString());
                  isDirty.open();
                }}
              />
            )}
          </div>

          <div>
            <Form.Label>Mood</Form.Label>
            <Mood
              ref={moodRef}
              question={moodQuestion}
              onDirty={() => {
                postToIframe();
                isDirty.open();
              }}
            />
          </div>

          <Form.Item label="Additional note" name="additionalNote">
            <TextArea
              placeholder={placeholder?.additionalNote || ''}
              onChange={() => {
                isDirty.open();
              }}
            />
          </Form.Item>
        </div>

        {fields.length > 0 && (
          <>
            <Divider className="!moe-m-0" dashed />
            <div className="moe-flex moe-flex-col moe-gap-y-s">
              <Heading size="5">Customized feedback</Heading>
              {fields.map((field, index) => {
                return (
                  <Form.Item
                    key={field.id}
                    name={`customizedFeedbacks.${index}.value`}
                    rules={{
                      validate: validateFeedback,
                    }}
                  >
                    <Feedback />
                  </Form.Item>
                );
              })}
            </div>
          </>
        )}

        <Divider className="!moe-m-0" dashed />

        <Form.Item label="Theme color" name="themeColor">
          <ColorPickerPalette
            defaultValue={themeColor || '#F96B18'}
            presetList={colorsList}
            onChange={(color) => {
              if (color) {
                try {
                  form.setValue('lightThemeColor', generateThemePattern(color).light);
                } catch (e) {
                  console.error(e);
                }
              }
              isDirty.open();
            }}
          />
        </Form.Item>
      </Form>
    );
  },
);
