import { type GetDailyReportForCustomerResult } from '@moego/api-web/moego/api/appointment/v1/daily_report_api';
import { type SentHistoryRecordDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { cn, Tag } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { dateMessageToString } from '../../../utils/utils';
import { usePetAndBusiness } from '../hook/DailyReport.hook';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';

interface DailyReportHistoryParams {
  onPostMessage?: (data: GetDailyReportForCustomerResult) => void;
  appointmentId: string;
  petId: string;
  historyList: SentHistoryRecordDef[];
  activeIndex: number;
  onActiveIndexChange: (index: number) => void;
}

export const DailyReportHistory = ({
  petId,
  onPostMessage,
  historyList,
  activeIndex,
  onActiveIndexChange,
}: DailyReportHistoryParams) => {
  const { petInfo, businessInfo } = usePetAndBusiness(petId);
  const [business] = useSelector(selectCurrentBusiness);

  const postToIframe = useLatestCallback((item: SentHistoryRecordDef) => {
    const { photos, videos, feedbacks, themeColor } = item.report.content || {};
    onPostMessage?.({
      pet: petInfo,
      business: businessInfo,
      serviceDate: item.serviceDate,
      report: {
        content: {
          photos: photos || [],
          videos: videos || [],
          feedbacks: feedbacks || [],
          themeColor: themeColor || '',
        },
      },
    });
  });

  const handleClick = (item: SentHistoryRecordDef, index: number) => {
    onActiveIndexChange(index);
    postToIframe(item);
  };

  useEffect(() => {
    postToIframe(historyList[activeIndex]);
  }, [activeIndex, historyList, postToIframe]);

  return (
    <div className=" moe-flex moe-flex-col moe-items-start moe-gap-[24px]">
      {historyList.map((item, index) => {
        return (
          <div
            key={index}
            className={cn(
              'moe-w-full moe-border moe-rounded-m moe-p-s moe-px-[24px] moe-cursor-pointer',
              'moe-bg-neutral-default moe-border-divider moe-transition-colors',
              'hover:moe-bg-neutral-sunken-0 hover:moe-border-button',
              activeIndex === index &&
                'moe-bg-brand-subtle hover:moe-bg-brand-subtle moe-border-brand hover:moe-border-brand',
            )}
            onClick={() => handleClick(item, index)}
          >
            <div className="moe-flex moe-items-center moe-justify-between">
              <div className="moe-font-[Manrope] moe-text-base  moe-font-bold">
                {dayjs(dateMessageToString(item.serviceDate)).format(business.dateFormat)}
              </div>
              {!item.sentSuccess && (
                <Tag
                  color="danger"
                  size="m"
                  className="moe-my-[-2px]"
                  isBordered
                  label="Failed to send"
                  variant="outlined"
                />
              )}
            </div>
            {item.description ? (
              <div className="moe-font-[Manrope] moe-text-sm moe-leading-[20px] moe-font-normal moe-mt-2 moe-text-[#828282]">
                {item.description}
              </div>
            ) : null}
          </div>
        );
      })}
    </div>
  );
};
