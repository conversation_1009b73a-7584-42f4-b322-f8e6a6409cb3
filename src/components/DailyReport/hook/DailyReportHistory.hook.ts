import { type SentHistoryRecordDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { useDispatch } from 'amos';
import { useState } from 'react';
import { getDailyReportSentHistory } from '../../../store/dailyReport/dailyReport.action';
import { useMount } from 'ahooks';

export const useGetHistoryData = (appointmentId: string, petId: string) => {
  const dispatch = useDispatch();
  const [historyList, setHistoryList] = useState<SentHistoryRecordDef[]>([]);

  useMount(async () => {
    dispatch(
      getDailyReportSentHistory({
        appointmentId,
        petId,
      }),
    ).then((list) => {
      setHistoryList(list);
    });
  });

  return historyList;
};
