import { type DateMessage } from '@moego/api-web/google/type/date';
import { type UpsertDailyReportConfigParams } from '@moego/api-web/moego/api/appointment/v1/daily_report_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type UploadItem, UploadStatus, useForm } from '@moego/ui';
import { useDispatch, useSelector, useStore } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { useMemo, useState } from 'react';
import { selectApptStartAndEndTime, selectMainServiceInAppt } from '../../../container/Appt/store/appt.selectors';
import { getDailyReportConfig, setDailyReportStatus } from '../../../store/dailyReport/dailyReport.action';
import { selectDailyReportConfig } from '../../../store/dailyReport/dailyReport.selectors';
import { useAsyncEffect } from '../../../utils/hooks/useAsyncEffect';
import { stringToDateMessage } from '../../../utils/utils';
import { generateUID } from '../../Upload/MoeGoUIUpload';
import { type QuestionDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { groupBy } from 'lodash';
import { QuestionCategoryType } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { useSerialCallback } from '@moego/tools';
import { generateThemePattern } from '../../ThemeColorInput/utils';

export interface FormParams {
  date: Dayjs;
  photos: UploadItem[];
  videos: UploadItem[];
  howDidItGo: string;
  moodList: string[];
  additionalNote: string;
  themeColor: string;
  lightThemeColor: string;
  howDidItGoInput: string;
  customizedFeedbacks: { value: QuestionDef }[];
}

export const HOW_DID_IT_GO = 'how_did_it_go';
export const MOOD = 'mood';
export const ADDITIONAL_NOTE = 'additional_note';
export const HOW_DID_IT_GO_CUSTMIZE_OPTION = 'Customize';

export type DailyReportStructure = Omit<UpsertDailyReportConfigParams, 'customerId'>;

export const useDailyReportForm = (appointmentId: string, petId: string) => {
  const dispatch = useDispatch();
  const [mainService, apptDates] = useSelector(
    selectMainServiceInAppt(appointmentId),
    selectApptStartAndEndTime(appointmentId),
  );
  const isBoarding = mainService.serviceItemType === ServiceItemType.BOARDING;
  const { startDateTime, endDateTime } = apptDates;
  const store = useStore();

  // structure 为了保存整体的结构，主要是其中部分不需要修改的数据， 后台为全量覆盖，所以必须要有完整结构及数据
  const [structure, setStructure] = useState<DailyReportStructure>({
    appointmentId,
    petId,
    serviceDate: {} as DateMessage,
    report: {
      content: {
        photos: [],
        videos: [],
        feedbacks: [],
        themeColor: '',
      },
    },
  });

  const form = useForm<FormParams>({
    defaultValues: {
      date: dayjs(),
      photos: [],
      videos: [],
      howDidItGo: '',
      howDidItGoInput: '',
      moodList: [],
      additionalNote: '',
      themeColor: '',
      lightThemeColor: '',
      customizedFeedbacks: [],
    },
    mode: 'onChange',
  });

  const [moodQuestion, setMoodQuestion] = useState<QuestionDef>();
  const [howDidItGoOptions, setHowDidItGoOptions] = useState<{ value: string; label: string }[]>([]);

  const [placeholder, setPlaceholder] = useState({
    additionalNote: '',
  });

  const getData = useSerialCallback(async () => {
    const dailyReportConfig = store.select(selectDailyReportConfig(+appointmentId));
    const content = dailyReportConfig.report.content;

    const {
      [QuestionCategoryType.FEEDBACK]: feedbacks = [],
      [QuestionCategoryType.CUSTOMIZE_FEEDBACK]: customizedFeedbacks = [],
    } = groupBy(
      content.feedbacks,
      // 存量的 feedbacks 旧数据可能不存在 category，此时需要做兼容
      (feedback) => feedback.category ?? QuestionCategoryType.FEEDBACK,
    );

    const howDidItGoItem = feedbacks.find((item) => item.key === HOW_DID_IT_GO);
    const moodListItem = feedbacks.find((item) => item.key === MOOD);
    const additionalNoteItem = feedbacks.find((item) => item.key === ADDITIONAL_NOTE);

    setHowDidItGoOptions(
      (howDidItGoItem?.options || [])
        .concat([HOW_DID_IT_GO_CUSTMIZE_OPTION])
        .map((item) => ({ label: item, value: item })),
    );
    setMoodQuestion(moodListItem);

    form.setValue(
      'photos',
      content.photos.map((photo, index) => ({
        url: photo,
        uid: generateUID(index),
        status: UploadStatus.success,
      })),
    );
    form.setValue(
      'videos',
      content.videos.map((video, index) => {
        const filename = new URLSearchParams(new URL(video).search).get('name') || 'video.mp4';
        return {
          url: video,
          uid: generateUID(index),
          status: UploadStatus.success,
          name: filename,
        };
      }),
    );

    form.setValue('themeColor', content.themeColor);
    if (content.themeColor && !content.lightThemeColor) {
      try {
        form.setValue('lightThemeColor', generateThemePattern(content.themeColor).light);
      } catch (e) {
        console.error(e);
      }
    } else {
      form.setValue('lightThemeColor', content.lightThemeColor || '');
    }

    form.setValue(
      'howDidItGo',
      howDidItGoItem?.customOptions[0] && howDidItGoItem?.choices[0] === howDidItGoItem.customOptions[0]
        ? HOW_DID_IT_GO_CUSTMIZE_OPTION
        : howDidItGoItem?.choices[0] || '',
    );
    form.setValue('howDidItGoInput', howDidItGoItem?.customOptions[0] || '');
    form.setValue('moodList', moodListItem?.choices || []);
    form.setValue('additionalNote', additionalNoteItem?.inputText || '');
    form.setValue(
      'customizedFeedbacks',
      customizedFeedbacks.map((f) => ({ value: f })),
    );

    setPlaceholder({
      additionalNote: additionalNoteItem?.placeholder || '',
    });
    setStructure({
      appointmentId,
      petId,
      serviceDate: stringToDateMessage(form.getValues().date),
      report: {
        content: {
          photos: form.getValues().photos.map((photo) => photo.url!),
          videos: content.videos,
          themeColor: content.themeColor,
          feedbacks,
        },
      },
    });
    dispatch(setDailyReportStatus(+appointmentId, petId, dailyReportConfig.status));
  });

  const startTime = startDateTime?.valueOf();
  const endTime = endDateTime?.valueOf();
  /**
   * @description 假设service时间是6.1-6.3，如果是当天是5.30，那么默认选中6.1
   *              如果当天是6.4 那么默认选中6.3
   *              如果当天是6.2 就是6.2
   */
  const serviceDate = useMemo(() => {
    if (!startTime || !endTime) {
      return;
    }

    let reportDate = startTime;

    if (isBoarding) {
      const now = Date.now();
      reportDate = now < startTime ? startTime : now > endTime ? endTime : now;
    }

    // Daycare 返回 date 当天
    return dayjs(reportDate);
  }, [isBoarding, startTime, endTime]);

  useAsyncEffect(async () => {
    if (!serviceDate) {
      return;
    }

    form.setValue('date', serviceDate);

    await dispatch(
      getDailyReportConfig({
        appointmentId,
        serviceDate: stringToDateMessage(serviceDate),
        petId,
      }),
    );

    getData();
  }, [serviceDate]);

  return {
    moodQuestion,
    howDidItGoOptions,
    placeholder,
    form,
    structure,
    getData,
  };
};
