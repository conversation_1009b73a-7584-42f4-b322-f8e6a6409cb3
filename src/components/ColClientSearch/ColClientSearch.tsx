import { useDispatch, useSelector, useStore } from 'amos';
import { Input } from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import React, { type FC, useEffect, useState } from 'react';
import { host } from '../../config/host/host';
import { type IClientSearch } from '../../config/interface';
import { http } from '../../middleware/api';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { type CustomerBookingSearchListDto } from '../../store/customer/clientFilters.action';
import { getCustomerList, transformMembershipSubscription } from '../../store/customer/customer.actions';
import { type CustomerRecord, customerMapBox } from '../../store/customer/customer.boxes';
import { selectBusinessCustomers } from '../../store/customer/customer.selectors';
import {
  type SaveCustomerActiveSubscriptionListParam,
  saveCustomerActiveSubscriptionList,
} from '../../store/membership/membership.actions';
import { customerPetListBox, petMapBox } from '../../store/pet/pet.boxes';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { Avatar } from '../Avatar/Avatar';
import { WithPermission } from '../GuardRoute/WithPermission';
import { Loading } from '../Loading/Loading';
import { MembershipIdentify } from '../MembershipIdentify/MembershipIdentify';

type TColType = 'normal' | 'popup';

type SearchStatus = 'initial' | 'searching' | 'finished';
interface IInputStatus {
  focusing: boolean;
  searching: SearchStatus;
}

interface ColClientSearchProps {
  onSelectClient: (id: number, client: any) => void;
  onAddNewClient?: () => void;
  onRemoveClient?: () => void;
  type?: TColType;
  showSelected?: boolean;
}

export const ColClientSearch: FC<ColClientSearchProps> = (props) => {
  const { onSelectClient, onAddNewClient, onRemoveClient, type, showSelected } = props;

  const inputStatusInitial: IInputStatus = {
    focusing: false,
    searching: 'initial',
  };
  const [inputStatus, setInputStatus] = useState(inputStatusInitial);
  const [keyword, setKeyword] = useState('');
  const [filterList, setFilterList] = useState([] as IClientSearch[]);
  const [selectedClient, setSelectedClient] = useState<IClientSearch | null>(null);
  const [popupVisible, setPopupVisible] = useState(false);
  const [top10CustomerList, setTop10CustomerList] = useState<IClientSearch[]>([] as IClientSearch[]);
  const store = useStore();
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness);

  // debounce search - begin
  // improve - cancel last unfinish query when new query comes
  const [, setSearchQuery] = useState({});

  const doSearchClients = async (filterKeyword: string) => {
    const keyword = filterKeyword.trim();
    if (!keyword) {
      const arr: IClientSearch[] = [];
      setFilterList(arr);
      setInputStatus(
        _.merge({}, inputStatus, {
          searching: 'initial',
        }),
      );
      return;
    }

    setInputStatus(
      _.merge({}, inputStatus, {
        searching: 'searching',
      }),
    );
    const res = await http.request({
      url: host.bookNew.searchClients(),
      method: 'GET',
      data: {
        keyword,
      },
    });
    const membershipSubscriptionInfo: SaveCustomerActiveSubscriptionListParam[] = res.data.customerList.map(
      (clientInfo: CustomerBookingSearchListDto['customerList'][number]) => {
        return {
          customerId: clientInfo.customerId + '',
          membershipSubscriptions: clientInfo.membershipSubscriptions?.map(transformMembershipSubscription) || [],
        };
      },
    );
    dispatch(saveCustomerActiveSubscriptionList(membershipSubscriptionInfo));
    setInputStatus(
      _.merge({}, inputStatus, {
        searching: 'finished',
      }),
    );

    if (res.code === 200) {
      setFilterList(res.data?.customerList || []);
    }
  };

  const sendQuery = async (value: string) => {
    doSearchClients(value);
  };

  const handleKeywordChange = (e: any) => {
    const { value } = e.target;
    setKeyword(value);

    const search = _.debounce(sendQuery, 500);

    setSearchQuery((prevSearch: any) => {
      if (prevSearch.cancel) {
        prevSearch.cancel();
      }
      return search;
    });

    search(value);
  };

  const handleFocusingChange = (status: boolean) => {
    // 保证 top10 clients 不会立即被卸载掉，才能被选中
    setTimeout(() => {
      setInputStatus(
        _.merge({}, inputStatus, {
          focusing: status,
        }),
      );
    }, 200);
  };

  const handleClickClient = (client: IClientSearch) => {
    if (showSelected) {
      setSelectedClient(client);
      setKeyword('');
    }
    if (type === 'popup') {
      setPopupVisible(false);
    }
    onSelectClient(client.customerId, client);
  };

  const returnPetsString = (pets: IClientSearch['petNameList']) => {
    if (pets && pets.length > 0) {
      return (
        `${pets
          .filter((_, index) => index < 3)
          .map((pet) => pet.petName)
          .join(' / ')}` + (pets.length > 3 ? `, +${pets.length - 3}` : '')
      );
    }
    return '';
  };

  const handleClickAdd = () => {
    onAddNewClient?.();
  };

  const classes = classNames('fc-col-client-search-wrapper', {
    'fc-col-client-search-wrapper-popup': type === 'popup' && popupVisible,
  });

  const computedAddShow: () => boolean = () => {
    if (type === 'normal') {
      return Boolean(onAddNewClient);
    }
    if (type === 'popup') {
      return Boolean(onAddNewClient) && popupVisible;
    }
    return false;
  };

  const computedListShow: () => boolean = () => {
    if (type === 'popup') {
      return !selectedClient && (popupVisible || Boolean(keyword));
    }
    return inputStatus.focusing || Boolean(keyword);
  };

  const removeSelectedClient = () => {
    setSelectedClient(null);
    onRemoveClient?.();
  };

  const handleClickDetail = () => {
    if (!selectedClient) {
      return;
    }
    window.open(`${window.location.origin}/client/${selectedClient.customerId}/overview`, '_target');
  };

  /**
   * 在 Active 客户中获取前 10 条数据
   */
  const getTop10Customers = useSerialCallback(async () => {
    await dispatch(getCustomerList({ queries: { keyword: '' }, filters: undefined }));
    const top10CustomerIds = store.select(selectBusinessCustomers()).getList();
    // 渲染没有用到 petList 属性就省略了
    // 也是因为 petList 太复杂了
    const top10Customers: Array<Omit<IClientSearch, 'petList' | 'businessId'>> = top10CustomerIds.map((customerId) => {
      const customer: CustomerRecord = store.select(customerMapBox.mustGetItem(customerId));
      const petList = store.select(customerPetListBox.getList(customerId));
      const petNameList = petList.toArray().map((petId) => {
        const pet = store.select(petMapBox.mustGetItem(petId));
        const { breed, customerId, petName } = pet;
        return {
          breed,
          customerId,
          petId,
          petName,
        };
      });
      const { firstName, lastName, inactive, phoneNumber, avatarPath, clientColor } = customer;
      return {
        customerId,
        firstName,
        lastName,
        inactive,
        phoneNumber: business.formatPhoneNumber(phoneNumber),
        clientColor,
        avatarPath,
        petNameList,
      };
    });

    setTop10CustomerList(top10Customers as any as IClientSearch[]);
  });

  useEffect(() => {
    if (inputStatus.focusing && type === 'popup') {
      setPopupVisible(true);
    }
  }, [inputStatus.focusing]);

  useEffect(() => {
    if (!popupVisible && type === 'popup') {
      setKeyword('');
    }
  }, [popupVisible]);

  useEffect(() => {
    getTop10Customers();
  }, []);

  const renderCustomerList = (customerList: Array<IClientSearch>) => {
    return customerList.map((client, cIndex) => (
      <div className="clients-list-item" key={cIndex} onClick={() => handleClickClient(client)}>
        <Avatar size="normal" className="clients-list-item-avatar" src={client.avatarPath || ''} />
        <div className="info-content">
          <div className="info-content-row">
            <p
              className="info-content-name-container moe-overflow-hidden"
              style={{
                color: client.clientColor || '#2a2d34',
              }}
            >
              <div
                title={`${client.firstName} ${client.lastName}`}
                className="info-content-name-v2"
              >{`${client.firstName} ${client.lastName}`}</div>
              <MembershipIdentify
                customerId={String(client.customerId)}
                className="moe-align-bottom moe-ml-1 moe-shrink-0"
              />
            </p>
            <p className="info-content-phone rr-mask">{business.formatPhoneNumber(client.phoneNumber)}</p>
            <div className="info-content-tag">
              {client.inactive === 1 ? <span className="inactive-tag">Inactive</span> : ''}
            </div>
          </div>
          <div className="info-content-row">
            <p className="info-content-pets">{returnPetsString(client.petNameList)}</p>
          </div>
        </div>
      </div>
    ));
  };

  return (
    <>
      {popupVisible ? <div className="fc-col-client-search-wrapper-mask" onClick={() => setPopupVisible(false)} /> : ''}
      <div className={classes}>
        <div className="client-search-wrapper">
          <Avatar
            size="large"
            type={inputStatus.focusing || keyword ? 'user-active' : 'user'}
            className="avatar"
            src={selectedClient?.avatarPath || ''}
          />
          {showSelected && selectedClient ? (
            <>
              <div className="selected-client-info">
                <p
                  className="client-name moe-flex moe-w-full"
                  style={{
                    color: selectedClient?.clientColor || '#2a2d34',
                  }}
                >
                  <div
                    title={`${selectedClient.firstName} ${selectedClient.lastName}`}
                    className="moe-max-w-fit moe-text-ellipsis moe-overflow-hidden moe-whitespace-nowrap"
                  >{`${selectedClient.firstName} ${selectedClient.lastName}`}</div>
                  <MembershipIdentify
                    customerId={String(selectedClient.customerId)}
                    className="moe-align-bottom moe-ml-1 moe-shrink-0"
                  />
                </p>
                <div className="selected-client-info-row">
                  <p className="text-phone">{selectedClient.phoneNumber}</p>
                  <span className="text-detail" onClick={handleClickDetail}>
                    Detail
                  </span>
                </div>
              </div>
              <div className="operation">
                <p className="remove" onClick={removeSelectedClient}>
                  Remove
                </p>
              </div>
            </>
          ) : (
            <div className="search-wrapper">
              {inputStatus.focusing || keyword ? <p className="searching-text">Select client</p> : ''}
              <Input
                size="large"
                value={keyword}
                onChange={handleKeywordChange}
                placeholder="Searching with name, pet name, phone"
                onFocus={() => handleFocusingChange(true)}
                onBlur={() => handleFocusingChange(false)}
                style={{ border: 0, outline: 'none', background: 'none' }}
                autoFocus
              />
            </div>
          )}
        </div>
        {computedListShow() ? (
          <div className="clients-list normal-scrollbar">
            {inputStatus.searching !== 'initial' ? (
              <Loading loading={inputStatus.searching === 'searching'}>
                {filterList.length > 0 ? (
                  renderCustomerList(filterList)
                ) : (
                  <div className="no-data">
                    {keyword && inputStatus.searching === 'finished' ? 'Unmatched client' : ''}
                  </div>
                )}
              </Loading>
            ) : (
              renderCustomerList(top10CustomerList)
            )}
          </div>
        ) : (
          ''
        )}
        {computedAddShow() ? (
          <WithPermission permissions={'createNewClient'}>
            <div className="add-new-client">
              <span onClick={handleClickAdd}>+ Add new client</span>
            </div>
          </WithPermission>
        ) : (
          ''
        )}
      </div>
    </>
  );
};

ColClientSearch.defaultProps = {
  type: 'normal',
  showSelected: false,
};
