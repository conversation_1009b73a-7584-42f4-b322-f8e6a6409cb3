.fc-col-client-search-wrapper {
  background: $white;
  max-height: 90vh;
  display: flex;
  flex-direction: column;

  &-mask {
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    background: rgba(0, 0, 0, 0.3);
  }

  &-popup {
    z-index: 11;
    position: relative;
  }

  .client-search-wrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 24px;
    display: flex;
    align-items: center;
    flex: none;

    .avatar {
      flex: none;
    }

    .search-wrapper {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      flex: 1;

      .searching-text {
        margin-bottom: 8px;
        font-size: $font-size-sm;
        font-weight: $font-weight-base;
        line-height: $line-height-19;
        color: $primary;
        text-align: left;
      }
    }

    .selected-client-info {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      flex: 1;
      text-align: left;
      overflow: hidden;

      .client-name {
        @include font-regular;
        color: $text-primary-color;
        margin-bottom: 10px;
        white-space: nowrap;
        width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &-row {
        display: flex;
        @include font-sm;
        width: 100%;

        .text-phone {
          max-width: 40%;
          color: $text-title-color;
          margin-right: 6px;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .text-detail {
          color: $primary;
          display: inline-block;
          cursor: pointer;
          flex: none;
        }
      }
    }

    .operation {
      flex: none;
      margin-left: 14px;

      .remove {
        color: $text-secondary-color;
        cursor: pointer;
      }
    }
  }

  .add-new-client {
    width: 100%;
    box-sizing: border-box;
    padding: 24px;
    border-top: 1px solid #eaedf1;
    text-align: left;
    color: $text-title-color;
    font-weight: $font-weight-base;
    font-size: $font-size-regular;
    line-height: $line-height-22;
    text-align: left;
    flex: none;

    & span {
      cursor: pointer;
      user-select: none;
    }
  }

  .clients-list {
    flex: 1;
    overflow-y: auto;
    border-top: 1px solid #eaedf1;
    transition: height 0.1s ease;

    .no-data {
      padding: 20px;
      box-sizing: border-box;
      width: 100%;
      font-size: $font-size-regular;
      font-weight: $font-weight-bold;
      line-height: $line-height-22;
      color: $text-disabled-color;
    }

    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      border-radius: $scroll-bar-border-radius;
      background-color: #f5f5f5;
    }

    &::-webkit-scrollbar {
      width: 6px;
      background-color: #f5f5f5;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: $scroll-bar-border-radius;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      background-color: $scroll-bar-color;
    }

    &-item {
      width: 100%;
      box-sizing: border-box;
      padding: 19px 33px;
      display: flex;
      align-items: center;
      cursor: pointer;
      border-top: 1px solid #eaedf1;

      &:first-child {
        border-top: 0;
      }

      &:hover {
        background: $primary-lighten;
      }

      &-avatar {
        margin-right: 17px;
        flex: none;
      }

      .info-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;

        &-row {
          display: flex;
          align-items: center;
          width: 100%;
        }

        &-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: $font-size-regular;
          font-weight: $font-weight-bold;
          line-height: $line-height-22;
          margin-right: 8px;
          color: $text-primary-color;
          text-align: left;
        }

        &-name-container {
          flex: 1;
          display: flex;
          font-size: $font-size-regular;
          font-weight: $font-weight-bold;
          line-height: $line-height-22;
          margin-right: 8px;
          color: $text-primary-color;
          text-align: left;
        }
        &-name-v2{
          max-width: fit-content;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &-phone {
          flex: none;
          font-size: $font-size-sm;
          font-weight: $font-weight-base;
          line-height: $line-height-19;
          color: $cancelled-ticket-color;
        }

        &-tag {
          flex: none;
          margin-left: 16px;
          width: 56px;

          .inactive-tag {
            height: 20px;
            border-radius: 4px;
            border: solid 1px $danger;
            background-color: rgba(208, 2, 27, 0.06);
            padding: 1px 5px;
            box-sizing: border-box;
            display: inline-block;
            width: 100%;
            font-family: Nunito;
            font-size: 13px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: normal;
            text-align: center;
            color: $danger;
            white-space: nowrap;
          }
        }

        &-pets {
          flex: 1;
          text-align: left;
          color: $text-title-color;
          font-size: $font-size-sm;
          font-weight: $font-weight-base;
          line-height: $line-height-19;
        }
      }
    }
  }
}
