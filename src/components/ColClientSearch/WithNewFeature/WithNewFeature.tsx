import React, { type PropsWithChildren } from 'react';
import { NewFeatureScopeInternal, useNewFeatureContext } from './NewFeatureCustomScope';
import { type FeatureKey, newFeatureConfigMap } from './utils';

interface Props {
  feature: FeatureKey;
}

const NewFeatureGuard = ({ children, feature }: PropsWithChildren<Props>) => {
  const newFeatureState = useNewFeatureContext(feature);
  if (!newFeatureState) {
    throw new Error('Did you forget to wrap <WithNewFeature /> within a <NewFeatureCustomScope />?');
  }
  const { isNew, isDismissed } = newFeatureState;
  return isNew && !isDismissed ? <>{children}</> : null;
};

/**
 * Render the children only if a new feature has not yet been dismissed.
 * @constructor
 */
export const WithNewFeature = (props: PropsWithChildren<Props>) => {
  const guardedChildren = <NewFeatureGuard {...props} />;
  return newFeatureConfigMap[props.feature].customContext ? (
    guardedChildren
  ) : (
    <NewFeatureScopeInternal feature={props.feature}>{guardedChildren}</NewFeatureScopeInternal>
  );
};
