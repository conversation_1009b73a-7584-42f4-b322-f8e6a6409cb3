import { useDispatch, useSelector, useStore } from 'amos';
import React, { type Context, type PropsWith<PERSON>hildren, createContext, useContext, useEffect, useMemo } from 'react';
import { getMetadataByKey } from '../../../store/metadata/metadata.actions';
import { selectMetaDataByKey, selectMetaDataLoaded } from '../../../store/metadata/metadata.selectors';
import { useBool } from '../../../utils/hooks/useBool';
import { type FeatureKey, defaultParseValue, newFeatureConfigMap } from './utils';

export interface NewFeatureState {
  feature: FeatureKey;
  isNew: boolean;
  isDismissed: boolean;
}

export type NewFeatureContextType = Context<NewFeatureState | null>;

export const createNewFeatureContext = (): NewFeatureContextType => {
  return createContext<NewFeatureState | null>(null);
};

export const NewFeatureContext = createNewFeatureContext();

// The created custom context must be placed in this file to avoid circular dependency
const contextMap: Record<string, NewFeatureContextType> = {};

export const newFeatureContextOf = (feature: FeatureKey) => {
  if (!contextMap[feature]) {
    contextMap[feature] = newFeatureConfigMap[feature].customContext ? createNewFeatureContext() : NewFeatureContext;
  }
  return contextMap[feature];
};

export const useNewFeatureContext = (feature: FeatureKey) => {
  const context = newFeatureContextOf(feature);
  return useContext(context);
};

interface Props {
  feature: FeatureKey;
}

const useNewFeatureState = (feature: FeatureKey): NewFeatureState => {
  const { metadataKey, parseValue = defaultParseValue } = newFeatureConfigMap[feature];
  const store = useStore();
  const dispatch = useDispatch();
  const [loaded] = useSelector(selectMetaDataLoaded(metadataKey));
  const { value: isNew, as: setNew } = useBool();
  const { value: isDismissed, as: setDismissed } = useBool();

  useEffect(() => {
    if (!loaded) {
      dispatch(getMetadataByKey(metadataKey));
    } else {
      // Initialize isNew/isDismissed only once. The following value updates should not dismiss these local states
      const { isNew, isDismissed } = parseValue(store.select(selectMetaDataByKey(metadataKey))!);
      setNew(isNew);
      setDismissed(isDismissed);
    }
  }, [loaded]);

  return useMemo<NewFeatureState>(
    () => ({
      feature,
      isNew,
      isDismissed,
    }),
    [feature, isNew, isDismissed],
  );
};

// Built-in scope should only be used by WithNewFeature.
export const NewFeatureScopeInternal = ({ children, feature }: PropsWithChildren<Props>) => {
  const CustomContext = newFeatureContextOf(feature);
  const newFeatureState = useNewFeatureState(feature);
  return <CustomContext.Provider value={newFeatureState}>{children}</CustomContext.Provider>;
};

/**
 * Specify the scope of a new feature. This is needed when your new feature hint is rendered conditionally, in which
 * case you should wrap the nearest stable ancestor with this component to ensure that the internal state will not be
 * reset during condition rendering.
 */
export const NewFeatureCustomScope = ({ children, feature }: PropsWithChildren<Props>) => {
  if (!newFeatureConfigMap[feature].customContext) {
    throw new Error('The feature must define "customContext: true" in its config.');
  }
  return <NewFeatureScopeInternal feature={feature}>{children}</NewFeatureScopeInternal>;
};
