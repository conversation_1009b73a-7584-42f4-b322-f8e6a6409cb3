import { useDispatch, useSelector, useStore } from 'amos';
import { useCallback, useEffect } from 'react';
import { getMetadataByKey, updateMetadata } from '../../../store/metadata/metadata.actions';
import { selectMetaDataByKey, selectMetaDataLoaded } from '../../../store/metadata/metadata.selectors';
import { type FeatureKey, defaultParseValue, newFeatureConfigMap } from './utils';

export const useDismissNewFeature = (feature: FeatureKey) => {
  const store = useStore();
  const dispatch = useDispatch();
  const c = newFeatureConfigMap[feature];
  return useCallback(
    async (...args: any[]) => {
      if (c.dismiss) {
        await c.dismiss(...args);
      } else {
        const dismissedValue = c.getDismissedValue
          ? c.getDismissedValue(store.select(selectMetaDataByKey(c.metadataKey)), ...args)
          : true;
        await dispatch(updateMetadata(c.metadataKey, dismissedValue));
      }
    },
    [c.dismiss, c.getDismissedValue, c.metadataKey],
  );
};

export const useNewFeature = (
  feature: FeatureKey,
): [boolean, (...args: any[]) => Promise<void>, Record<string, any>] => {
  const { metadataKey, parseValue = defaultParseValue } = newFeatureConfigMap[feature];
  const dispatch = useDispatch();
  const [loaded, metadataValue] = useSelector(selectMetaDataLoaded(metadataKey), selectMetaDataByKey(metadataKey));
  let isNewFeature: boolean;
  let extra = {};
  if (loaded) {
    const { isNew, isDismissed, ...rest } = parseValue(metadataValue);
    isNewFeature = isNew && !isDismissed;
    extra = rest;
  } else {
    isNewFeature = false;
  }

  useEffect(() => {
    if (!loaded) {
      dispatch(getMetadataByKey(metadataKey));
    }
  }, [loaded]);
  const dismissNewFeature = useDismissNewFeature(feature);

  return [isNewFeature, dismissNewFeature, extra];
};
