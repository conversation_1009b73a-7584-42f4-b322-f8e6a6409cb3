import classNames from 'classnames';
import React, { type ReactNode } from 'react';
import IconIconExclamationCircleRedFilled16Svg from '../../assets/icon/icon-exclamation-circle-red-filled-16.svg';
import IconIconExclamationCircleYellowFilled16Svg from '../../assets/icon/icon-exclamation-circle-yellow-filled-16.svg';
import IconIconInfoCircleGreyOutlinedSvg from '../../assets/icon/icon-info-circle-grey-outlined.svg';

interface Props {
  className?: string;
  color: string;
  icon: string | ReactNode;
  iconSize?: number;
  text: string | ReactNode;
  textColor?: string;
}

/**
 * A rounded colored area to show text-based hint.
 */
export const ColoredHint = ({ className, color, icon, iconSize, text, textColor }: Props) => {
  return (
    <div
      className={classNames(
        'moe-flex moe-items-start moe-px-[16px] moe-py-[12px] moe-rounded-[8px] moe-gap-[8px]',
        className,
      )}
      style={{ backgroundColor: color }}
    >
      {typeof icon === 'string' ? <img src={icon} style={{ width: iconSize, height: iconSize }} /> : icon}
      {typeof text === 'string' ? (
        <div className="moe-text-xs moe-font-medium" style={{ color: textColor }}>
          {text}
        </div>
      ) : (
        text
      )}
    </div>
  );
};

export const ErrorHint = (props: Omit<Props, 'color' | 'icon'> & Partial<Pick<Props, 'color' | 'icon'>>) => {
  return <ColoredHint color="#FAE6E8" textColor="#333" icon={IconIconExclamationCircleRedFilled16Svg} {...props} />;
};

export const WarningHint = (props: Omit<Props, 'color' | 'icon'> & Partial<Pick<Props, 'color' | 'icon'>>) => {
  return <ColoredHint color="#FFF7E8" textColor="#333" icon={IconIconExclamationCircleYellowFilled16Svg} {...props} />;
};

export const InfoHint = (props: Omit<Props, 'color' | 'icon'> & Partial<Pick<Props, 'color' | 'icon'>>) => {
  return <ColoredHint color="#F7F8FA" textColor="#666" icon={IconIconInfoCircleGreyOutlinedSvg} {...props} />;
};
