import React, { memo, useMemo } from 'react';
import { MenuSelect } from '@moego/business-components';
import { Checkbox, Separator, Text, cn } from '@moego/ui';
import { type ServiceFilter } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { useCareTypeServiceList } from './hooks/useCareTypeServiceList';

export interface CareTypeServices {
  value: ServiceFilter;
  onChange: (value: ServiceFilter) => void;
}

export const CareTypeServices = memo((props: CareTypeServices) => {
  const { value, onChange } = props;
  const { serviceItemType, isAllService, serviceIds = [] } = value || {};

  const [careTypeServiceList, optionList] = useCareTypeServiceList(serviceItemType);

  const isIndeterminate = serviceIds.length > 0 && !isAllService;

  const selectedKeys = useMemo(() => {
    if (isAllService) {
      return careTypeServiceList.toArray().map((serviceId) => String(serviceId));
    }
    return serviceIds;
  }, [isAllService, careTypeServiceList, serviceIds]);

  const handleChange = (nextServiceIds: string[], isAllService?: boolean) => {
    const isAll = isAllService ?? careTypeServiceList.every((serviceId) => nextServiceIds.includes(String(serviceId)));

    onChange?.({ serviceItemType, isAllService: isAll, serviceIds: isAll ? [] : nextServiceIds });
  };

  return (
    <MenuSelect.List
      prefix={
        <div>
          <div
            className="hover:moe-bg-neutral-sunken-0 moe-rounded-s moe-cursor-pointer"
            onClick={() => handleChange([], !isAllService)}
          >
            <Checkbox
              className=" moe-px-spacing-xs moe-py-[10px]"
              isSelected={isAllService}
              isIndeterminate={isIndeterminate && !isAllService}
            >
              <Text as={'span'} variant="small" className={cn({ 'moe-font-bold': isAllService || isIndeterminate })}>
                All services
              </Text>
            </Checkbox>
          </div>
          <Separator />
        </div>
      }
      selectionMode="multiple"
      selectedKeys={selectedKeys}
      onSelectionChange={(nextValue) => handleChange([...(nextValue as Set<string>)])}
    >
      {optionList.map((option) => {
        return (
          <MenuSelect.Item key={option.value} value={option.value}>
            {option.label}
          </MenuSelect.Item>
        );
      })}
    </MenuSelect.List>
  );
});
