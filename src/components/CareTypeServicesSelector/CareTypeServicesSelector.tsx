import { MenuSelect } from '@moego/business-components';
import { useSelector } from 'amos';
import React, { useMemo, type FC } from 'react';
import { selectSceneCareTypeAsOptions } from '../../store/careType/careType.selectors';
import { AllCareTypeValue } from '../../store/service/scene.enum';
import { Text } from '@moego/ui';
import { CareTypeServices } from './CareTypeServices';
import { useQuery } from '../../store/utils/useQuery';
import { getBusinessBasicServiceInfoList } from '../../store/service/actions/public/service.actions';
import { type ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type ServiceFilter } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { formatSelectedServices } from './CareTypeServicesSelector.utils';
import { usePropsState } from '../../utils/hooks/usePropsState';
import { useFormatSelectorValue } from './hooks/useFormatSelectorValue';
import { getBusinessApplicableEvaluationList } from '../../store/evaluation/evaluation.actions';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';

export interface CareTypeServicesSelectorProps {
  className?: string;
  value: ServiceFilter[];
  onChange: (value: ServiceFilter[]) => void;
}

export const CareTypeServicesSelector: FC<CareTypeServicesSelectorProps> = (props) => {
  const { className, value, onChange } = props;

  const [careTypeOptions] = useSelector(selectSceneCareTypeAsOptions);
  const [innerValue, setInnerValue] = usePropsState(value);
  const formatValue = useFormatSelectorValue();

  const leftOptions = useMemo(() => {
    return [{ value: AllCareTypeValue, label: 'All services' }].concat(careTypeOptions);
  }, [careTypeOptions]);

  const getCareTypeServiceFilter = (careType: ServiceItemType): ServiceFilter => {
    return (
      innerValue.find((v) => v.serviceItemType === careType) ?? {
        serviceItemType: careType,
        serviceIds: [],
        isAllService: false,
      }
    );
  };

  const handleServiceChange = (serviceFilter: ServiceFilter) => {
    const nextValue = innerValue.filter((v) => v.serviceItemType !== serviceFilter.serviceItemType);
    // service filter must be has service ids or is all service
    if (serviceFilter.isAllService || serviceFilter.serviceIds.length > 0) {
      nextValue.push(serviceFilter);
    }
    setInnerValue(nextValue);
  };

  // only emit change event when menu is closed
  const handleOpenChange = useLatestCallback((isOpen: boolean) => {
    if (!isOpen) {
      onChange(innerValue);
    }
  });

  useQuery(
    getBusinessBasicServiceInfoList({
      serviceType: ServiceType.SERVICE,
    }),
  );

  useQuery(getBusinessApplicableEvaluationList());

  return (
    <MenuSelect
      className={className}
      leftOptions={leftOptions}
      canExpandRight={(key) => key !== String(AllCareTypeValue)}
      classNames={{
        menu: 'moe-h-[320px]',
        menuLeft: 'moe-flex-1 moe-w-[325px]',
        menuRight: 'moe-flex-1 moe-w-[325px]',
      }}
      isClearable={false}
      content={formatValue(innerValue)}
      defaultExpandedKey={String(AllCareTypeValue)}
      renderRight={({ leftSelectedKey }) => {
        const careTypeServiceFilter = getCareTypeServiceFilter(Number(leftSelectedKey));
        return <CareTypeServices value={careTypeServiceFilter} onChange={handleServiceChange} />;
      }}
      onLeftChange={({ leftSelectedKey, closeMenu }) => {
        if (leftSelectedKey === String(AllCareTypeValue)) {
          setInnerValue([]);
          closeMenu();
        }
      }}
      onOpenChange={handleOpenChange}
    >
      {(item) => {
        const careTypeServiceFilter = getCareTypeServiceFilter(Number(item.value));
        return (
          <MenuSelect.Item key={item.value} value={item.value} textValue={`${item.value}`}>
            <div className="moe-flex moe-justify-between">
              <Text variant="small" className="moe-text-primary">
                {item.label}
              </Text>
              {item.value === AllCareTypeValue ? null : (
                <Text variant="caption" className="moe-text-tertiary">
                  {formatSelectedServices(careTypeServiceFilter)}
                </Text>
              )}
            </div>
          </MenuSelect.Item>
        );
      }}
    </MenuSelect>
  );
};
