import { useStore } from 'amos';
import { selectAllServiceList } from '../../../store/service/service.selectors';
import { type ServiceFilter } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { withPl } from '../../../utils/calculator';
import { useCallback } from 'react';
import { selectBusinessApplicableEvaluationList } from '../../../store/evaluation/evaluation.selectors';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export const useFormatSelectorValue = () => {
  const store = useStore();

  return useCallback(
    (serviceFilters: ServiceFilter[]) => {
      if (!serviceFilters?.length) {
        return 'All services';
      }

      const allServiceList = store.select(selectAllServiceList);
      const serviceMap = store.select(serviceMapBox);
      const businessEvaluationList = store.select(selectBusinessApplicableEvaluationList);

      const careTypeServiceList = allServiceList.groupBy((serviceId) => {
        const service = serviceMap.mustGetItem(serviceId);
        return service.serviceItemType;
      });

      const totalSelectedServices = serviceFilters.reduce((acc, serviceFilter) => {
        const isEvaluation = serviceFilter.serviceItemType === ServiceItemType.EVALUATION;
        const careTypeAllServiceList = isEvaluation
          ? businessEvaluationList.toArray()
          : (careTypeServiceList.get(serviceFilter.serviceItemType)?.toJSON() ?? []);
        const careTypeSelectedServices = serviceFilter.isAllService
          ? careTypeAllServiceList.length
          : serviceFilter.serviceIds.length;
        return acc + careTypeSelectedServices;
      }, 0);

      return withPl(totalSelectedServices, 'service');
    },
    [store],
  );
};
