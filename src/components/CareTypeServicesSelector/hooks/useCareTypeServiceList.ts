import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import { selectServiceList } from '../../../store/service/service.selectors';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { businessEvaluationMapBox } from '../../../store/evaluation/evaluation.boxes';
import { selectBusinessApplicableEvaluationList } from '../../../store/evaluation/evaluation.selectors';

export const useCareTypeServiceList = (serviceItemType: ServiceItemType) => {
  const [careTypeServiceList, serviceMap, businessEvaluationList, evaluationMap] = useSelector(
    selectServiceList([ServiceType.SERVICE, serviceItemType]),
    serviceMapBox,
    selectBusinessApplicableEvaluationList,
    businessEvaluationMapBox,
  );

  const isEvaluation = serviceItemType === ServiceItemType.EVALUATION;
  const evaluationOptionList = businessEvaluationList
    .map((id) => {
      const evaluation = evaluationMap.mustGetItem(id);
      return {
        value: id,
        label: evaluation.name,
      };
    })
    .toArray();

  const otherCareTypeServiceList = careTypeServiceList
    .map((id) => {
      const service = serviceMap.mustGetItem(id);
      return {
        value: id,
        label: service.name,
      };
    })
    .toArray();

  return isEvaluation
    ? ([businessEvaluationList, evaluationOptionList] as const)
    : ([careTypeServiceList, otherCareTypeServiceList] as const);
};
