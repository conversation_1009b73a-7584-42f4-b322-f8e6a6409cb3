import { Checkbox, CheckboxGroup } from '@moego/ui';
import React, { useEffect, useState } from 'react';
import { isExistingClientAccepted, isNewClientAccepted } from '../../store/onlineBooking/onlineBooking.boxes';

const toEnum = (stringsValue: ('new' | 'existing')[]): number => {
  const isNew = stringsValue.includes('new');
  const isExisting = stringsValue.includes('existing');
  return (isNew ? 1 : 0) + (isExisting ? 2 : 0);
};

const toStrings = (enumValue: number): ('new' | 'existing')[] => {
  if (Math.floor(enumValue) !== enumValue || enumValue < 0 || enumValue > 3) {
    throw new Error('enumValue must be 0, 1, 2 or 3.');
  }
  const r: ('new' | 'existing')[] = [];
  isNewClientAccepted(enumValue) && r.push('new');
  isExistingClientAccepted(enumValue) && r.push('existing');
  return r;
};

interface Props {
  value?: number;
  onChange?: (newValue: number) => void;
}

/**
 * Transform the enum values defined by the backend to the antd Checkbox Group values back and forth.
 */
export const ClientGroupsCheckboxGroup = ({ value, onChange }: Props) => {
  const [valueInternal, setValueInternal] = useState<('new' | 'existing')[]>([]);

  useEffect(() => {
    if (value !== undefined) {
      setValueInternal(toStrings(value));
    }
  }, [value]);

  const onChangeInternal = (checkedValue: ('new' | 'existing')[]) => {
    const enumValue = toEnum(checkedValue);
    onChange?.(enumValue);
    if (value === undefined) {
      // Uncontrolled
      setValueInternal(checkedValue);
    }
  };

  return (
    <CheckboxGroup value={valueInternal} onChange={onChangeInternal}>
      <div className="moe-flex moe-flex-col moe-gap-[16px] moe-text-[14px] moe-leading-[18px] moe-font-medium moe-text-[#333]">
        <Checkbox value="new">New visitors</Checkbox>
        <Checkbox className="!moe-ml-0" value="existing">
          Existing clients with filters
        </Checkbox>
      </div>
    </CheckboxGroup>
  );
};
