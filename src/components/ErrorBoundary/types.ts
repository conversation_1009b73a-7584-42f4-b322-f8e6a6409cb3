import {
  type Component,
  type ComponentType,
  type ErrorInfo,
  type FunctionComponent,
  type PropsWithChildren,
  type ReactElement,
  type ReactNode,
} from 'react';

export enum ReportModule {
  // CRM
  Referral = 'Referral',
  FeatureOnBoarding = 'FeatureOnBoarding',
  MessagePackage = 'MessagePackage',
  NPS = 'NPS',
  Notifications = 'Notifications',
  CallCenter = 'CallCenter',
  PricingUpgrade = 'PricingUpgrade',
  //ERP
  ClockInOut = 'ClockInOut',
  WaitList = 'WaitList',
  PrintCard = 'PrintCard',
  SmartScheduling = 'SmartScheduling',
  // FIN-TECH
  Accounting = 'Accounting',
  Stripe = 'Stripe',
  QuickBook = 'QuickBook',
  Invoice = 'Invoice',
  // INFRA
  AnnounceKit = 'AnnounceKit',
}

declare function FallbackRender(props: FallbackProps): ReactNode;

export type FallbackProps = {
  error: any;
  resetErrorBoundary: (...args: any[]) => void;
};

type ErrorBoundarySharedProps = PropsWithChildren<{
  onError?: (error: Error, info: ErrorInfo) => void;
  onReset?: (
    details:
      | { reason: 'imperative-api'; args: any[] }
      | { reason: 'keys'; prev: any[] | undefined; next: any[] | undefined },
  ) => void;
  resetKeys?: any[];
}>;

export type ErrorBoundaryProps = ErrorBoundarySharedProps & {
  fallback?: ReactElement<unknown, string | FunctionComponent | typeof Component> | null;
  FallbackComponent?: ComponentType<FallbackProps>;
  fallbackRender?: typeof FallbackRender;
  reportModule?: ReportModule;
};

export function hasArrayChanged(a: any[] = [], b: any[] = []) {
  return a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]));
}
