import React, { type ComponentProps, type ComponentType } from 'react';
import { ErrorBoundary } from './ErrorBoundary';
import { type ErrorBoundaryProps } from './types';

/**
 * This is a higher order component that wraps the component with an error boundary.
 * It catches any errors that are thrown in the wrapped component and displays an error message.
 * */
export const withErrorBoundary = <T extends ComponentType<any>>(
  Component: T,
  errorBoundaryProps: ErrorBoundaryProps = {},
) => {
  const WrappedComponent = (props: ComponentProps<T>) => {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };

  WrappedComponent.displayName = `ErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};
