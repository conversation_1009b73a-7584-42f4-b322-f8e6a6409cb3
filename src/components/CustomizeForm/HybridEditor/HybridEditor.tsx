/**
 * HybridEditor
 * Auto Message 详情页的混合表单组件
 * Template 表单输入组件，支持 Input 和 TextArea 两种输入方式
 */
import { type FormFieldSharedProps, type UseFormReturn, cn } from '@moego/ui';
import { FormItemHelpText } from '@moego/ui/dist/esm/components/Form/FormItemHelpText';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { type Transformer } from '@moego/ui/dist/esm/types/common';
import React, { type ComponentType, type FocusEvent, type MemoExoticComponent, useMemo } from 'react';
import { useBool } from '../../../utils/hooks/useBool';
import { memo } from '../../../utils/react';
import { Condition } from '../../Condition';
import { HybridEditorContext } from './HybridEditorContext';
import { HybridEditorInput, HybridEditorInputDisplayName } from './HybridEditorInput';
import { HybridEditorTextArea, HybridEditorTextAreaDisplayName } from './HybridEditorTextArea';

export interface HybridEditorProps<SourceValue, TargetValue> extends Omit<FormFieldSharedProps, 'children'> {
  className?: string;
  children?: React.ReactNode;
  form: UseFormReturn<any, any>;
  wrapperClassName?: string;
  slotWrapperClassName?: string;
  onFocus?: (e: FocusEvent<Element>) => void;
  onBlur?: (e: FocusEvent<Element>) => void;
  textAreaTransformer?: Transformer<SourceValue, TargetValue>;
  inputTransformer?: Transformer<SourceValue, TargetValue>;
  valuePropName?: string;
  hintBlock?: React.ReactNode;
  defaultOpenResetChildren?: boolean;
}

interface HybridEditorComponent extends MemoExoticComponent<ComponentType<HybridEditorProps<unknown, unknown>>> {
  Input: typeof HybridEditorInput;
  TextArea: typeof HybridEditorTextArea;
}

export const HybridEditor = memo(function HybridEditor<SourceValue = unknown, TargetValue = SourceValue>(
  props: HybridEditorProps<SourceValue, TargetValue>,
) {
  const {
    className,
    isRequired,
    tooltip,
    label,
    children,
    form,
    helpTextClassNames,
    description,
    wrapperClassName,
    slotWrapperClassName,
    onFocus,
    onBlur,
    inputTransformer,
    hintBlock,
    isDisabled,
    defaultOpenResetChildren,
  } = props;

  const restChildrenVisible = useBool(!!defaultOpenResetChildren);

  const childrenArray = React.Children.toArray(children);
  const inputChild = childrenArray.find((child) => {
    if (React.isValidElement(child)) {
      return (child as any).type.displayName === HybridEditorInputDisplayName;
    }
    return false;
  }) as React.ReactElement;
  const textAreaChild = childrenArray.find((child) => {
    if (React.isValidElement(child)) {
      return (child as any).type.displayName === HybridEditorTextAreaDisplayName;
    }
    return false;
  }) as React.ReactElement;
  const restChildren = inputChild && textAreaChild ? childrenArray.splice(2) : childrenArray.splice(1);
  const childrenNames = childrenArray.map((child) => (child as React.ReactElement).props.name);

  const errorsField = childrenNames.map((name) => form.getFieldState(name)).filter((item) => item.invalid);
  const hasError = isRequired && form.formState.errors && errorsField.length > 0;
  const errorMessage = useMemo(() => {
    let errorMsg = '';
    if (hasError) {
      const [{ error }] = errorsField;
      if (error?.message) {
        errorMsg = error.message as string;
      } else if (error?.type === 'required') {
        errorMsg = 'Required';
      } else {
        errorMsg = 'Invalid';
      }
    }
    return errorMsg;
  }, [errorsField]);

  return (
    <HybridEditorContext.Provider
      value={{
        hasError,
        form,
      }}
    >
      <div className={className}>
        <div className="moe-flex moe-justify-between">
          <FormItemLabel isRequired={isRequired} tooltip={tooltip} label={label} />
          {hintBlock}
        </div>
        <div
          className={cn(
            'moe-border moe-border-button moe-rounded-[8px] moe-overflow-hidden',
            'hover:moe-border-focus-dark focus:moe-border-focus-dark',
            {
              '!moe-border-danger': hasError,
              '!moe-border-button': isDisabled,
            },
            wrapperClassName,
          )}
          onFocus={(e) => {
            restChildrenVisible.open();
            onFocus?.(e);
          }}
          onBlur={onBlur}
        >
          {inputChild &&
            React.cloneElement(inputChild, {
              ...inputChild.props,
              className: cn(inputChild.props.className, 'moe-border-b moe-border-button'),
              transformer: inputTransformer,
            })}

          {textAreaChild &&
            React.cloneElement(textAreaChild, {
              ...textAreaChild?.props,
              className: cn(textAreaChild?.props.className, 'moe-border-button'),
              transformer: inputTransformer,
            })}

          <Condition if={restChildren.length > 0 && !isDisabled && restChildrenVisible.value}>
            <div className={cn('moe-p-[10px]', slotWrapperClassName)}>{restChildren}</div>
          </Condition>
        </div>
        <FormItemHelpText
          className="moe-mt-xxs"
          classNames={helpTextClassNames}
          description={description}
          errorMessage={errorMessage}
        />
      </div>
    </HybridEditorContext.Provider>
  );
}) as HybridEditorComponent;

HybridEditor.displayName = 'HybridEditor';
HybridEditor.Input = HybridEditorInput;
HybridEditor.TextArea = HybridEditorTextArea;
