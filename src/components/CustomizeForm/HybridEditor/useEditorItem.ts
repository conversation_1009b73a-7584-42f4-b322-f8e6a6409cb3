import { type ControllerRenderProps, type MoeGoFormProps, useController } from '@moego/ui';
import { get } from 'lodash';
import { type HybridEditorTextAreaProps } from './HybridEditorTextArea';

export function useEditorItem({
  form,
  name,
  rules,
}: {
  form: MoeGoFormProps['form'];
  name: HybridEditorTextAreaProps['name'];
  rules: HybridEditorTextAreaProps['rules'];
}): ControllerRenderProps {
  const { field } = useController({
    name,
    rules,
    defaultValue: get(form.formState.defaultValues, name),
    control: form.control,
  });

  return {
    ...field,
  };
}
