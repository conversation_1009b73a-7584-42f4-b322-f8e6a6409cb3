import { type FormItemProps } from '@moego/ui/dist/esm/components/Form/FormItem';
import { TextArea, type TextAreaProps } from '@moego/ui/dist/esm/components/Input/TextArea';
import React, { type MutableRefObject, memo, useContext } from 'react';
import { mergeFuncs } from '../../../utils/utils';
import { HybridEditorContext } from './HybridEditorContext';
import { useEditorItem } from './useEditorItem';

export type HybridEditorTextAreaProps<SourceValue = unknown, TargetValue = SourceValue> = TextAreaProps &
  FormItemProps<SourceValue, TargetValue> & {
    className?: string;
    forwardedRef?: MutableRefObject<HTMLTextAreaElement | null>;
  };

export const HybridEditorTextArea = memo((props: HybridEditorTextAreaProps) => {
  const { className, errorMessage, name, rules, transformer, forwardedRef, maxLength, ...restProps } = props;
  const { form } = useContext(HybridEditorContext);

  const editorItemProps = useEditorItem({
    form,
    name,
    rules,
  });

  let value: string =
    typeof transformer?.input === 'function' ? transformer.input(editorItemProps.value) : editorItemProps.value;
  if (maxLength) {
    value = value?.substring(0, maxLength);
  }

  return (
    <TextArea
      className={className}
      classNames={{
        inputBox: '!moe-outline-0 moe-rounded-none',
        inputWrapper: restProps.isDisabled ? 'moe-bg-neutral-sunken-0' : '',
      }}
      {...editorItemProps}
      {...restProps}
      onChange={mergeFuncs(editorItemProps.onChange, restProps.onChange)}
      ref={(e) => {
        if (forwardedRef) {
          forwardedRef.current = e; // 更新ref
        }
        editorItemProps.ref(e); // 同时注册到React Hook Form
      }}
      value={value}
    ></TextArea>
  );
});

export const HybridEditorTextAreaDisplayName = 'HybridEditorTextArea';

HybridEditorTextArea.displayName = HybridEditorTextAreaDisplayName;
