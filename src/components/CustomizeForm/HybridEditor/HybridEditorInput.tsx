import { type FormItemProps } from '@moego/ui/dist/esm/components/Form/FormItem';
import { TextInput, type TextProps } from '@moego/ui/dist/esm/components/Input/TextInput';
import React, { memo, useContext } from 'react';
import { mergeFuncs } from '../../../utils/utils';
import { HybridEditorContext } from './HybridEditorContext';
import { useEditorItem } from './useEditorItem';

export type HybridEditorInputProps<SourceValue = unknown, TargetValue = SourceValue> = TextProps &
  FormItemProps<SourceValue, TargetValue> & {
    className?: string;
  };

export const HybridEditorInput = memo<HybridEditorInputProps>((props) => {
  const { className, errorMessage, name, rules, prefix = 'Title: ', transformer, ...restProps } = props;
  const { form } = useContext(HybridEditorContext);

  const editorItemProps = useEditorItem({
    form,
    name,
    rules,
  });

  return (
    <TextInput
      prefix={prefix}
      className={className}
      classNames={{
        inputBox: '!moe-outline-0 moe-rounded-none',
      }}
      {...editorItemProps}
      {...restProps}
      onChange={mergeFuncs(editorItemProps.onChange, restProps.onChange)}
      value={
        (typeof transformer?.input === 'function'
          ? transformer.input(editorItemProps.value)
          : editorItemProps.value) as string
      }
    ></TextInput>
  );
});

export const HybridEditorInputDisplayName = 'HybridEditorInput';

HybridEditorInput.displayName = HybridEditorInputDisplayName;
