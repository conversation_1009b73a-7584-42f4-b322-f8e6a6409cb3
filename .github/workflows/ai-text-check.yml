name: AI Text Check

on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - "src/**/*.ts"
      - "src/**/*.tsx"

jobs:
  ai-text-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.7

      - name: NPM Login
        run: |
          npx npm-cli-login \
            -u "$NPM_PUBLISHER_USR" \
            -p "$NPM_PUBLISHER_PSW" \
            -e <EMAIL> \
            -r "https://nexus.devops.moego.pet/repository/npm-local"
        env:
          NPM_PUBLISHER_USR: ${{ vars.NPM_PUBLISHER_USR }}
          NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}

      - name: Install AI dependencies
        run: |
          pnpm add @google/generative-ai openai

      - name: Run AI text analysis
        id: text-analysis
        run: |
          node scripts/text-analysis-ai.mjs
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          OPENAI_TOKEN: ${{ secrets.OPENAI_TOKEN }}
          GITHUB_WORKSPACE: ${{ github.workspace }}

      - name: Find or create AI comment
        id: ai-comment
        uses: actions/github-script@v7
        with:
          script: |
            // 获取当前PR的所有评论
            const comments = await github.rest.issues.listComments({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo
            });

            // 查找现有的AI评论
            let existingComment = null;
            for (const comment of comments.data) {
              if (comment.user.type === 'Bot' && comment.body.includes('AI文案检查报告')) {
                existingComment = comment;
                break;
              }
            }

            if (existingComment) {
              console.log(`Found existing AI comment: ${existingComment.id}`);
              core.setOutput('comment-id', existingComment.id.toString());
              core.setOutput('comment-exists', 'true');
            } else {
              console.log('No existing AI comment found, will create new one');
              core.setOutput('comment-exists', 'false');
            }

      - name: Update or create AI comment
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const reportPath = 'text-analysis-report.md';
            let commentBody = '';

            // 根据是否有问题来决定评论内容
            const hasIssues = '${{ steps.text-analysis.outputs.has-issues }}' === 'true';

            if (hasIssues) {
              // 有问题的情况
              if (fs.existsSync(reportPath)) {
                commentBody = fs.readFileSync(reportPath, 'utf8');
              } else {
                commentBody = '## 🤖 AI文案检查报告\n\n⚠️ **发现文案问题，但报告文件生成失败。**\n\n请检查AI分析日志获取详细信息。\n\n*此报告由 AI 自动生成。*';
              }
            } else {
              // 无问题的情况
              commentBody = '## 🤖 AI文案检查报告\n\n🎉 **检查完成！未发现文案问题。**\n\n所有文案都通过了AI检查，继续保持！\n\n*此报告由 AI 自动生成。*';
            }

            const commentExists = '${{ steps.ai-comment.outputs.comment-exists }}' === 'true';
            const commentId = '${{ steps.ai-comment.outputs.comment-id }}';

            if (commentExists && commentId) {
              // 更新现有评论
              await github.rest.issues.updateComment({
                comment_id: parseInt(commentId),
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: commentBody
              });
              console.log(`Updated existing AI comment: ${commentId}`);
            } else {
              // 创建新评论
              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: commentBody
              });
              console.log('Created new AI comment');
            }
