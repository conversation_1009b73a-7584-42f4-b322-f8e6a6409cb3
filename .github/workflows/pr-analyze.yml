name: PR Analyze

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.7

      - name: NPM Login
        run: |
          npx npm-cli-login \
            -u "$NPM_PUBLISHER_USR" \
            -p "$NPM_PUBLISHER_PSW" \
            -e <EMAIL> \
            -r "https://nexus.devops.moego.pet/repository/npm-local"
        env:
          NPM_PUBLISHER_USR: ${{ vars.NPM_PUBLISHER_USR }}
          NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}

      - name: Install dependencies
        run: pnpm install

      - name: Run Effect
        run: pnpm tsx ./ci/effect.ts

      - name: Run Analyze
        run: pnpm tsx ./ci/pr-analyze-symbol.ts
        env:
          REPO_NAME: ${{ github.event.repository.name }}
          OWNER_NAME: ${{ github.event.repository.owner.login }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          ADMIN_TOKEN_GITHUB: ${{ secrets.ADMIN_TOKEN_GITHUB }}
