#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

GIT_DIR=$(git rev-parse --git-dir)
if [ ! -f "$GIT_DIR/MERGE_HEAD" ] && \
   [ ! -f "$GIT_DIR/REBASE_HEAD" ] && \
   [ ! -f "$GIT_DIR/CHERRY_PICK_HEAD" ] && \
   [ ! -f "$GIT_DIR/REVERT_HEAD" ]; then
  echo "✅ 非 Git 操作状态，执行 lint-staged"
  git -c core.whitespace=-space-before-tab,-trailing-space --no-pager diff --cached --check && npx lint-staged
else
  echo "🚫 Git 正在执行 merge/rebase/cherry-pick/revert，跳过 lint-staged"
fi

STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep "[jt]sx\?$" || true)

if [ "$STAGED_FILES" = "" ]; then
  exit 0
fi

npx vitest related --run --passWithNoTests $STAGED_FILES

if [ $? = 0 ]; then
  printf "\n\033[1;32mTest Passed\033[0m\n"
  exit 0
else
  printf "\n\033[41mTest Failed:\033[0m Fix test errors and try again!\n"
  exit 1
fi
