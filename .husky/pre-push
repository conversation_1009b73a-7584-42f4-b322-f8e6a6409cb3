#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# release branch need to be release-YYMMDD -> release-210101
./node_modules/.bin/git-branch-is -r "^(feature|bugfix|gate)-([0-9a-z.-]+)|master$|staging$|online$|^release-[0-9]{6}$"

current_branch=$(git rev-parse --abbrev-ref HEAD)
excluded_branches="production|online|gate-.*|release-.*|main"

if [[ ! $current_branch =~ $excluded_branches ]]; then
  echo "fetch origin main..."
  git fetch origin main > /dev/null 2>&1

  if git merge-base --is-ancestor origin/main $current_branch; then
    printf "\033[1;32mCurrent branch has synchronized with the latest main\033[0m\n"
  else
    printf "\033[1;31mCurrent branch is behind the latest main branch, please sync it ASAP\033[0m\n"
  fi
fi
