import fs from 'fs';
import path from 'path';
import { RsbuildPlugin, RsbuildPluginAPI } from '@rsbuild/core';

const financeKitRoot = process.env.FINANCE_KIT_ROOT || path.resolve(__dirname, '../../moego-finance-kit');
const nodeModulesPath = path.resolve(__dirname, '../node_modules');
const packagePathMap = {
  '@moego/finance-core': path.resolve(financeKitRoot, 'packages/finance-core/dist/esm/src/index.js'),
  '@moego/finance-assets': path.resolve(financeKitRoot, 'packages/finance-assets'),
  '@moego/finance-plugins': path.resolve(financeKitRoot, 'packages/finance-plugins/dist/esm/src/index.js'),
  '@moego/finance-ui': path.resolve(financeKitRoot, 'packages/finance-ui/dist/esm/src/index.js'),
  '@moego/finance-utils': path.resolve(financeKitRoot, 'packages/finance-utils/dist/esm/src/index.js'),
  '@moego/finance-terminal': path.resolve(financeKitRoot, 'packages/finance-terminal/dist/esm/src/index.js'),
  '@moego/finance-web-kit': path.resolve(financeKitRoot, 'embeds/finance-web-kit/dist/esm/src/index.js'),
  '@moego/ui': path.resolve(nodeModulesPath, '@moego/ui'),
};

export class FinanceKitProxyPlugin implements RsbuildPlugin {
  name = 'finance-kit-proxy-plugin';

  constructor() {
    console.log(`${this.name} loaded, set PROXY_FINANCE_KIT=false to disable\n`);
  }

  setup(api: RsbuildPluginAPI) {
    api.modifyRspackConfig((config) => {
      config.resolve = config.resolve || {};
      config.resolve.alias = config.resolve.alias || {};
      for (const [pkg, filePath] of Object.entries(packagePathMap)) {
        const resolvedPath = path.resolve(__dirname, filePath);
        if (fs.existsSync(resolvedPath)) {
          console.log(`[${this.name}] Resolved ${pkg} to ${resolvedPath}`);
          config.resolve.alias[pkg] = resolvedPath;
        } else {
          console.warn(`[${this.name}] File for package ${pkg} not found at ${resolvedPath}`);
        }
      }
    });
  }
}
