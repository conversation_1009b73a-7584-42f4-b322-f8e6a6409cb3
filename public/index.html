<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="<%= assetPrefix %>/logo.jpg" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=yes" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="theme-color" content="#000000" />
    <meta name="google" content="notranslate" />
    <meta name="applicable-device" content="mobile" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-itunes-app" content="app-id=1561621817, app-argument=main" />
    <meta name="format-detection" content="telephone=no,email=no" />
    <meta name="referrer" content="strict-origin-when-cross-origin" />
    <link rel="apple-touch-icon" href="<%= assetPrefix %>/logo.jpg" />
    <title>MoeGo - All-in-One Pet Care Software</title>
    <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="index,follow" />
    <meta
      name="description"
      content="MoeGo is the best all-in-one pet care software designed to automate and streamline your business."
    />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@site" />
    <meta name="twitter:creator" content="@handle" />
    <meta property="og:title" content="MoeGo - All-in-One Pet Care Software" />
    <meta
      property="og:description"
      content="MoeGo is the best all-in-one pet care software designed to automate and streamline your business."
    />
    <meta property="og:url" content="www.moego.pet" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="<%= assetPrefix %>/og-logo.png" />
    <meta property="og:image:alt" content="MoeGo" />
    <meta property="og:image:width" content="600" />
    <meta property="og:image:height" content="315" />
    <meta property="og:site_name" content="MoeGo" />

    <script>
      if (!window.BigInt) {
        document.addEventListener("DOMContentLoaded", function () {
          document.body.innerHTML = `<p style="padding: 16px;">This website may not function properly on your browser. Please upgrade your browser version or switch to another browser, such as <a href='https://www.google.com/chrome/'>Chrome</a>.</p>`;
        });
      }
      window.MOE_VERSION = "<%= htmlWebpackPlugin.options.buildVersion %>";
      window.MOE_BUILD_TIME = "<%= htmlWebpackPlugin.options.buildTime %>";
    </script>

    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-T3WPJCL");
    </script>
    <!-- End Google Tag Manager -->
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--vite-inject-->
    <!--
This HTML file is a template.
If you open it directly in the browser, you will see an empty page.

You can add webfonts, meta tags, or analytics to this file.
The build step will place the bundled scripts into the <body> tag.

To begin the development, run `npm start` or `pnpm start`.
To create a production bundle, use `npm run build` or `pnpm build`.
-->
    <script>
      (function () {
        var w = window;
        var d = document;
        var i = function () {
          i.c(arguments);
        };
        i.q = [];
        i.c = function (args) {
          i.q.push(args);
        };
        w.Intercom = i;

        function l() {
          var s = d.createElement("script");
          s.type = "text/javascript";
          s.async = true;
          s.src =
            window.__env__ === "online"
              ? "https://widget.intercom.io/widget/oh5g31xm"
              : "https://widget.intercom.io/widget/mqmyiwto";
          var x = d.getElementsByTagName("script")[0];
          x.parentNode.insertBefore(s, x);
        }

        if (w.attachEvent) {
          w.attachEvent("onload", l);
        } else {
          w.addEventListener("load", l, false);
        }
      })();
    </script>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-T3WPJCL"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <!-- End Google Tag Manager (noscript) -->
    <!-- Status page -->
    <script src="https://tmyxdjg8hmtm.statuspage.io/embed/script.js" async></script>
    <!-- End Status page -->
  </body>
</html>
