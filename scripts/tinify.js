const fs = require('fs');
const path = require('path');
const tinify = require('tinify');
const glob = require('glob');

const config = {
  max: 5200000,
  min: 20000,
  key: 'fl3gzRtKpHMVsdxd977sYm15vLBlvLmd', // 500/month
  dirPath: path.resolve(__dirname, '../src/assets'),
};

tinify.key = config.key;

glob(`${config.dirPath}/**/*.{jpg,jpeg,png,avif,webp}`, async function (err, files) {
  const task = files
    .map((file) => {
      const stat = fs.statSync(file);
      if (stat.size < config.max && stat.size > config.min) {
        return tinify.fromFile(file).toFile(file);
      }
      return false;
    })
    .filter(Boolean);

  console.log(`Tinify ${task.length} files start...`);
  await Promise.all(task);
  console.log('Tinify done');
});
