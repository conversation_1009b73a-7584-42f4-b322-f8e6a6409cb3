import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { TextLintEngine } from 'textlint';
import { Project } from 'ts-morph';

const __dirname = fileURLToPath(new URL('.', import.meta.url));

// 加载 .env 文件
dotenv.config();

// 检查环境变量
if (process.env.USE_TEXT_LINT !== 'TRUE') {
  console.log('\x1b[33m提示\x1b[0m: 已跳过文本检查，如需检查可在 .env 文件中设置 USE_TEXT_LINT=TRUE');
  process.exit(0);
}

const config = {
  // 需要检查的 JSX 属性
  targetAttributes: ['placeholder', 'title', 'alt', 'label', 'description', 'content', 'tooltip'],
  // 需要忽略的 JSX 属性
  ignoreAttributes: [
    'className',
    'class',
    'style',
    'id',
    'name',
    'type',
    'key',
    'data-testid',
    'data-*', // 所有 data- 属性
    'aria-label',
    'aria-description',
  ],
};

// 缓存 TextLintEngine 实例
const engine = new TextLintEngine({
  configFile: resolve(__dirname, '..', '.textlintrc'),
});

// 添加标识位
let hasError = false;

// 修改 lintTexts 函数
async function lintTexts(validateText, filePath, lineNumber) {
  // 使用缓存的 engine 实例
  const results = await engine.executeOnText(validateText);

  if (results.length === 0 || results[0].messages.length === 0) {
    return true;
  }
  // 输出检查结果
  results[0].messages.forEach((message) => {
    const { message: msg, ruleId } = message;
    console.log(`\x1b[31m错误\x1b[0m [${ruleId}] ${filePath}:${lineNumber}:`, msg);
    hasError = true;
  });

  return false;
}

// 修改 checkText 函数
async function checkText(toCheckText, filePath, lineNumber) {
  try {
    // 移除引号并去除 HTML 实体字符
    const text = toCheckText
      .replace(/^['"`]|['"`]$/g, '')
      .replace(/&[a-zA-Z]+;/g, ' ')
      .replace(/\n\s+/g, ' ')
      .replace(/\s{2,}/g, ' ')
      .trim();

    if (text && text.length > 0 && /[a-zA-Z]/.test(text)) {
      // 使用本地 node_modules 中的 cspell
      try {
        execSync(`echo "${text}" | ./node_modules/.bin/cspell stdin --no-progress --no-summary`, {
          stdio: ['pipe', 'pipe', 'pipe'],
        });
      } catch (error) {
        if (error.status === 1) {
          console.log(`\x1b[31m拼写错误\x1b[0m [cspell] ${filePath}:${lineNumber}:`, `"${text}" 存在拼写错误`);
          hasError = true;
        }
      }

      // 继续进行 textlint 检查
      await lintTexts(text, filePath, lineNumber);
    }
    return true;
  } catch (error) {
    console.error(`检查文本失败 [${filePath}:${lineNumber}]:`, error);
    hasError = true;
    return false;
  }
}

// 获取 git staged 的文件
function getStagedFiles() {
  try {
    const output = execSync('git diff --cached --name-only --diff-filter=ACMR').toString();
    return output
      .split('\n')
      .filter((file) => file && /\.tsx?$/.test(file))
      .map((file) => resolve(process.cwd(), file));
  } catch (error) {
    console.error('获取 git staged 文件失败:', error);
    return [];
  }
}

const project = new Project();

// 只添加 git staged 的文件
const stagedFiles = getStagedFiles();
if (stagedFiles.length === 0) {
  console.log('没有需要检查的 TSX 文件');
  process.exit(0);
}

project.addSourceFilesAtPaths(stagedFiles);

// 添加全局标识位
let isInIgnoreBlock = false;
let currentIgnoreFile = null;

// 检查是否是忽略块的开始或结束
function checkIgnoreBlock(node) {
  // 检查前导注释
  const leadingComments = node.getLeadingCommentRanges();
  if (leadingComments && leadingComments.length > 0) {
    for (const comment of leadingComments) {
      const commentText = comment.getText();
      if (commentText.includes('@text-lint ignore start')) {
        isInIgnoreBlock = true;
        currentIgnoreFile = node.getSourceFile().getFilePath();
        return true;
      }
      if (commentText.includes('@text-lint ignore end')) {
        isInIgnoreBlock = false;
        currentIgnoreFile = null;
        return true;
      }
    }
  }

  // 检查 JSX 注释
  if (node.getKindName() === 'JsxExpression') {
    const text = node.getText().trim();
    if (text.startsWith('{/*') && text.endsWith('*/}') && text.includes('@text-lint ignore start')) {
      isInIgnoreBlock = true;
      currentIgnoreFile = node.getSourceFile().getFilePath();
      return true;
    }
    if (text.startsWith('{/*') && text.endsWith('*/}') && text.includes('@text-lint ignore end')) {
      isInIgnoreBlock = false;
      currentIgnoreFile = null;
      return true;
    }
  }

  return false;
}

function hasIgnoreComment(node) {
  // 获取节点的所有前导注释
  const leadingComments = node.getLeadingCommentRanges();
  if (leadingComments && leadingComments.length > 0) {
    // 检查每个注释
    for (const comment of leadingComments) {
      const commentText = comment.getText();
      // 检查单行忽略
      if (commentText.includes('@text-lint ignore')) {
        return true;
      }
    }
  }

  // 获取前一个兄弟节点的 JSX 注释
  const previousSibling = node.getPreviousSibling();
  if (!previousSibling) return false;
  // 检查是否是 JSX 注释
  if (previousSibling.getKindName() === 'JsxExpression') {
    const text = previousSibling.getText().trim();
    if (text.startsWith('{/*') && text.endsWith('*/}') && text.includes('@text-lint ignore')) {
      return true;
    }
  }

  return false;
}

// 检查是否正在进行 merge 操作
function isInMergeProcess() {
  try {
    // 使用 fs.existsSync 检查 MERGE_HEAD 文件
    return existsSync(resolve(process.cwd(), '.git/MERGE_HEAD'));
  } catch (error) {
    return false;
  }
}

function isFragmentedText(node) {
  // 首先检查当前节点是否是 JsxText
  if (node.getKindName() !== 'JsxText') return false;

  // 获取父节点
  const parent = node.getParent();
  if (!parent) return false;

  // 获取所有兄弟节点
  const siblings = parent.getChildren();

  // 找到 SyntaxList 节点
  const syntaxList = siblings.find((s) => s.getKindName() === 'SyntaxList');
  if (!syntaxList) return false;

  const syntaxListText = syntaxList.getText();

  // 检查是否包含：
  // 1. JSX 标签 (<tag>)
  // 2. JSX 表达式 ({expression})
  // 3. JSX 自闭合标签 (<tag />)
  return (
    /<[A-Za-z]/.test(syntaxListText) || // JSX 标签
    /\{[^}]+\}/.test(syntaxListText) || // JSX 表达式
    /\/\s*>/.test(syntaxListText) // 自闭合标签
  );
}

async function main() {
  try {
    // 检查是否正在进行 merge
    if (isInMergeProcess()) {
      console.log('merge 操作，跳过文本检查');
      process.exit(0);
    }

    // 重置错误标识
    hasError = false;

    for (const file of project.getSourceFiles()) {
      // 重置忽略块状态
      isInIgnoreBlock = false;
      currentIgnoreFile = null;

      const filePath = file.getFilePath();
      const forEachDescendantAsArray = file.forEachDescendantAsArray();
      for (const node of forEachDescendantAsArray) {
        // 检查是否是忽略块的开始或结束
        checkIgnoreBlock(node);

        // 如果在忽略块内，跳过检查
        if (isInIgnoreBlock && currentIgnoreFile === filePath) {
          continue;
        }

        // JSX 属性中的文案
        if (node.getKindName() === 'JsxAttribute') {
          const attributeName = node.getChildAtIndex(0).getText();

          if (config.ignoreAttributes.includes(attributeName)) {
            continue;
          }

          // 检查目标属性
          if (config.targetAttributes.includes(attributeName)) {
            const value = node.getChildAtIndex(2); // 获取属性值
            if (value && !hasIgnoreComment(node)) {
              // 检查整个属性节点
              const valueKind = value.getKindName();
              const lineNumber = value.getStartLineNumber();

              if (valueKind === 'StringLiteral') {
                // 如果是字符串字面量，直接提取文本
                await checkText(value.getText(), filePath, lineNumber);
              } else if (valueKind === 'JsxExpression') {
                // 如果是表达式，尝试提取表达式中的字符串字面量
                for (const child of value.getChildren()) {
                  // 处理直接的字符串字面量
                  if (child.getKindName() === 'StringLiteral') {
                    await checkText(child.getText(), filePath, child.getStartLineNumber());
                  }
                  // 处理模板字符串
                  else if (child.getKindName() === 'TemplateExpression') {
                    const templateText = child.getText().replace(/\${[^}]+}/g, ''); // 移除插值部分
                    await checkText(templateText, filePath, child.getStartLineNumber());
                  }
                  // 处理函数调用中的字符串（如 t('some text')）
                  else if (child.getKindName() === 'CallExpression') {
                    for (const arg of child.getArguments()) {
                      if (arg.getKindName() === 'StringLiteral') {
                        await checkText(arg.getText(), filePath, arg.getStartLineNumber());
                      }
                    }
                  }
                }
              }
            }
          }
          continue;
        }

        // JSX 文本内容
        if (node.getKindName() === 'JsxText') {
          const text = node.getText().trim();
          if (!text) continue;

          // 如果是被元素分割的文本片段，跳过检查
          if (isFragmentedText(node)) {
            continue;
          }

          if (!hasIgnoreComment(node)) {
            await checkText(text, filePath, node.getStartLineNumber());
          }
          continue;
        }
      }
    }
  } catch (error) {
    console.error('检查过程发生错误:', error);
    process.exit(1);
  }
}

main().then(() => {
  if (hasError) {
    process.exit(1);
  }
});
