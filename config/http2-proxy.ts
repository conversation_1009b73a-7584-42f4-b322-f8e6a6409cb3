import { IncomingMessage } from 'http';
import * as Http2 from 'http2';
import proxy from 'http2-proxy';

type http2WebOptions = Parameters<typeof proxy.web>[2];

const error = (message: string): never => {
  throw new Error(message);
};

export const createHttp2ProxyMiddlewares = (options: {
  [regexp: string]: {
    target: string;
    rewrite?: (url: string) => string;
    secure?: boolean;
    onReq?: http2WebOptions['onReq'];
    onRes?: (req: Http2.Http2ServerRequest, res: Http2.Http2ServerResponse, proxyRes: IncomingMessage) => void;
  };
}) => {
  const middlewares: ((req: Http2.Http2ServerRequest, res: Http2.Http2ServerResponse, next: () => void) => void)[] = [];

  for (const [regexp, { target, rewrite, secure = false, onReq, onRes }] of Object.entries(options)) {
    const re = new RegExp(regexp);
    const tu = new URL(target);

    if (!tu.pathname.endsWith('/')) {
      tu.pathname += '/';
    }

    const protocol = /^https?:$/.test(tu.protocol)
      ? (tu.protocol.slice(0, -1) as 'https' | 'http')
      : error(`Invalid protocol: ${tu.href}`);

    const port =
      tu.port === ''
        ? { https: 443, http: 80 }[protocol]
        : /^\d+$/.test(tu.port)
          ? Number(tu.port)
          : error(`Invalid port: ${tu.href}`);

    middlewares.push((req, res, next) => {
      if (req.url && re.test(req.url)) {
        const url = (rewrite?.(req.url) ?? req.url).replace(/^\/+/, '');
        const { pathname, search } = new URL(url, tu);
        // @ts-expect-error req type mismatch
        proxy.web(req, res, {
          protocol,
          port,
          hostname: tu.hostname,
          path: pathname + search,
          onReq: onReq as any,
          onRes: onRes as any,
          rejectUnauthorized: secure,
        });
      } else {
        next();
      }
    });
  }

  return middlewares;
};

// 实现change origin效果
export const proxyOnRes = async (
  req: Http2.Http2ServerRequest,
  res: Http2.Http2ServerResponse,
  proxyRes: IncomingMessage,
) => {
  const reqHost = (req.headers.host || req.headers[':authority'] || '') as string;
  const host = reqHost.replace(/:\d+$/, '');
  res.setHeader('x-powered-by', 'http2-proxy');
  if (host && proxyRes.headers['set-cookie']?.length) {
    res.setHeader(
      'set-cookie',
      proxyRes.headers['set-cookie']!.map((s) => {
        return s.replace(/;\s*domain=.*?(?=;|$)/gi, `; Domain=${host}`);
      }),
    );
    delete proxyRes.headers['set-cookie'];
  }

  res.writeHead(proxyRes.statusCode || 200, proxyRes.headers);

  proxyRes.pipe(res);
};
