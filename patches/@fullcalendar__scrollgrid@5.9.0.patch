diff --git a/main.js b/main.js
index 3de5ca95d9e50437b5b81456b16c9463fac1c564..809357d89dd49179ae902de76fdf732949ca0fe3 100644
--- a/main.js
+++ b/main.js
@@ -212,6 +212,7 @@ var StickyScrolling = /** @class */ (function () {
         if (this.listener) {
             this.listener.destroy();
         }
+        this.scrollEl = null;
     };
     StickyScrolling.prototype.queryElGeoms = function (els) {
         var _a = this, scrollEl = _a.scrollEl, isRtl = _a.isRtl;
@@ -417,6 +418,7 @@ var ScrollSyncer = /** @class */ (function () {
             var scrollListener = _a[_i];
             scrollListener.destroy();
         }
+        this.scrollEls = null;
     };
     ScrollSyncer.prototype.bindScroller = function (el) {
         var _this = this;
@@ -503,8 +505,8 @@ var ScrollGrid = /** @class */ (function (_super) {
         _this.scrollSyncersBySection = {};
         _this.scrollSyncersByColumn = {};
         // for row-height-syncing
-        _this.rowUnstableMap = new Map(); // no need to groom. always self-cancels
-        _this.rowInnerMaxHeightMap = new Map();
+        _this.rowUnstableMap = new WeakMap(); // no need to groom. always self-cancels
+        _this.rowInnerMaxHeightMap = new WeakMap();
         _this.anyRowHeightsChanged = false;
         _this.recentSizingCnt = 0;
         _this.state = {
