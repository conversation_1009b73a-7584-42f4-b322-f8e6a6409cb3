diff --git a/main.js b/main.js
index 88a9ef82d54112b4e19fbfd438b05f5a27518ff2..9780973f7cbcfa3103537e0928b37d9f65e3b770 100644
--- a/main.js
+++ b/main.js
@@ -1491,6 +1491,7 @@ var EventDragging = /** @class */ (function (_super) {
     }
     EventDragging.prototype.destroy = function () {
         this.dragging.destroy();
+        this.subjectEl = null;
     };
     // render a drag state on the next receivingCalendar
     EventDragging.prototype.displayDrag = function (nextContext, state) {
